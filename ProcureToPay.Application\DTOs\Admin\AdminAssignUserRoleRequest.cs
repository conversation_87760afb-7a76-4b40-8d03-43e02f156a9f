using ProcureToPay.Domain.Enums; // For UserRole
using System;
using System.ComponentModel.DataAnnotations;

namespace ProcureToPay.Application.DTOs.Admin
{
    public class AdminAssignUserRoleRequest
    {
        [Required] // Ensures the field is provided
        [EnumDataType(typeof(UserRole))] // Ensures the value is a valid UserRole enum member
        public UserRole NewRole { get; set; }
    }
}
