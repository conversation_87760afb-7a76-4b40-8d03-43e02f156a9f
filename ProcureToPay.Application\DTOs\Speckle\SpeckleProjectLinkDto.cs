using System;

namespace ProcureToPay.Application.DTOs.Speckle
{
    public class SpeckleProjectLinkDto
    {
        public Guid Id { get; set; }
        public Guid P2PProjectId { get; set; }
        public string? ProjectName { get; set; } // For display purposes
        public string? SpeckleServerUrl { get; set; }
        public string? SpeckleStreamId { get; set; }
        public string? SpeckleCommitId { get; set; }
        // We might not want to expose the token directly in DTOs,
        // or only a masked/partial version. For now, including for completeness.
        // Consider removing or redacting in a real application.
        public string? SpeckleAuthTokenHint { get; set; } // e.g., "Token Set" or last 4 chars

        // SECURITY NOTE: Exposing the full token to the client for viewer initialization is a security risk.
        // Consider dedicated, scoped, read-only tokens for viewer access if possible.
        public string? SpeckleAuthToken { get; set; }

        public DateTime LinkedAt { get; set; }
        public DateTime? LastRefreshedAt { get; set; }
    }
}
