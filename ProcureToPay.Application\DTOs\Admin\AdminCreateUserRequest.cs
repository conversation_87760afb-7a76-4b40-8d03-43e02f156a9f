using ProcureToPay.Domain.Enums; // For UserRole
using System;
using System.ComponentModel.DataAnnotations;

namespace ProcureToPay.Application.DTOs.Admin
{
    public class AdminCreateUserRequest
    {
        [Required]
        [StringLength(256)]
        public string Username { get; set; } = string.Empty;

        [Required]
        [EmailAddress]
        [StringLength(256)]
        public string Email { get; set; } = string.Empty;

        [Required]
        public string Password { get; set; } = string.Empty; // Will be validated against PasswordPolicy

        [Required]
        public UserRole Role { get; set; } // The UserRole enum directly

        public Guid? OrganizationId { get; set; } // Required for OrgAdmins, optional for MarketplaceAdmins if they create unassigned users

        [StringLength(256)]
        public string? FullName { get; set; }

        [Phone]
        [StringLength(50)]
        public string? PhoneNumber { get; set; }
    }
}
