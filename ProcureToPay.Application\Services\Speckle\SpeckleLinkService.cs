using Microsoft.EntityFrameworkCore;
using ProcureToPay.Application.DTOs.Speckle;
using ProcureToPay.Domain.Entities;
using ProcureToPay.Infrastructure.Persistence;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
// using AutoMapper; // Uncomment if AutoMapper is used

namespace ProcureToPay.Application.Services.Speckle
{
    public class SpeckleLinkService : ISpeckleLinkService
    {
        private readonly ApplicationDbContext _context;
        // private readonly IMapper _mapper; // Uncomment if AutoMapper is used

        // Temporarily remove IMapper until it's confirmed to be set up
        public SpeckleLinkService(ApplicationDbContext context /*, IMapper mapper */)
        {
            _context = context;
            // _mapper = mapper;
        }

        public async Task<SpeckleProjectLinkDto> CreateLinkAsync(CreateSpeckleLinkRequest request, Guid userId)
        {
            // TODO: Add validation - e.g., check if P2PProjectId exists and is valid for the user.
            // TODO: Check for existing link for the same P2PProjectId if only one is allowed.
            var existingLink = await _context.SpeckleProjectLinks
                                             .FirstOrDefaultAsync(l => l.P2PProjectId == request.P2PProjectId);
            if (existingLink != null)
            {
                // Or throw a custom exception to be handled by controller
                throw new InvalidOperationException($"A Speckle link already exists for project ID {request.P2PProjectId}.");
            }


            var project = await _context.Projects.FindAsync(request.P2PProjectId);
            if (project == null)
            {
                throw new ArgumentException($"Project with ID {request.P2PProjectId} not found.", nameof(request.P2PProjectId));
            }

            var speckleLink = new SpeckleProjectLink(
                request.P2PProjectId,
                request.SpeckleServerUrl,
                request.SpeckleStreamId,
                request.SpeckleAuthToken // SECURITY: Token stored as is. Note for encryption.
            )
            {
                SpeckleCommitId = request.SpeckleCommitId
                // CreatedAt is set by BaseEntity constructor
                // CreatedBy does not exist on BaseEntity
                // LastRefreshedAt will be null initially
            };

            _context.SpeckleProjectLinks.Add(speckleLink);
            await _context.SaveChangesAsync();

            // Manual mapping for now
            return MapToDto(speckleLink, project.Name);
        }

        public async Task<SpeckleProjectLinkDto?> GetLinkByIdAsync(Guid linkId)
        {
            var link = await _context.SpeckleProjectLinks
                                     .Include(l => l.Project) // Include project for ProjectName
                                     .FirstOrDefaultAsync(l => l.Id == linkId);
            if (link == null) return null;

            return MapToDto(link, link.Project?.Name);
        }

        public async Task<SpeckleProjectLinkDto?> GetLinkBySpeckleStreamIdAsync(string speckleStreamId)
        {
            var link = await _context.SpeckleProjectLinks
                                     .Include(l => l.Project)
                                     .FirstOrDefaultAsync(l => l.SpeckleStreamId == speckleStreamId);
            if (link == null) return null;

            return MapToDto(link, link.Project?.Name);
        }

        public async Task<SpeckleProjectLinkDto?> GetLinkByP2PProjectIdAsync(Guid p2pProjectId)
        {
            var link = await _context.SpeckleProjectLinks
                                     .Include(l => l.Project)
                                     .FirstOrDefaultAsync(l => l.P2PProjectId == p2pProjectId);
            if (link == null) return null;

            return MapToDto(link, link.Project?.Name);
        }

        public async Task<IEnumerable<SpeckleProjectLinkDto>> GetAllLinksAsync()
        {
            // TODO: Consider user-specific filtering or pagination for large datasets.
            var links = await _context.SpeckleProjectLinks
                                      .Include(l => l.Project)
                                      .ToListAsync();

            return links.Select(link => MapToDto(link, link.Project?.Name));
        }

        public async Task<bool> UpdateLinkAsync(Guid linkId, CreateSpeckleLinkRequest request, Guid userId)
        {
            var link = await _context.SpeckleProjectLinks.FindAsync(linkId);
            if (link == null) return false;

            // Ensure the link is for the same P2P Project if P2PProjectId is part of the update logic
            // For now, assuming P2PProjectId does not change with this update method.
            // If it can change, add validation: if (link.P2PProjectId != request.P2PProjectId) throw new InvalidOperationException("Cannot change P2P Project ID of an existing link.");

            link.SpeckleServerUrl = request.SpeckleServerUrl;
            link.SpeckleStreamId = request.SpeckleStreamId;
            link.SpeckleCommitId = request.SpeckleCommitId; // Update commit ID
            link.SpeckleAuthToken = request.SpeckleAuthToken; // SECURITY: Token updated. Note for encryption.
            // LastModifiedBy does not exist on BaseEntity
            link.UpdateModifiedAt(); // Use method from BaseEntity
            link.LastRefreshedAt = DateTime.UtcNow; // Also update LastRefreshedAt as the link details changed

            _context.SpeckleProjectLinks.Update(link);
            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> UpdateLinkCommitAsync(Guid linkId, string newCommitId, Guid userId)
        {
            var link = await _context.SpeckleProjectLinks.FindAsync(linkId);
            if (link == null) return false;

            link.SpeckleCommitId = newCommitId;
            // LastModifiedBy does not exist on BaseEntity
            link.UpdateModifiedAt(); // Use method from BaseEntity
            link.LastRefreshedAt = DateTime.UtcNow; // Commit ID change implies a refresh

            _context.SpeckleProjectLinks.Update(link);
            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<string?> GetSpeckleAuthTokenAsync(Guid p2pProjectId)
        {
            // SECURITY: This method directly exposes the token.
            // Ensure it's only called by trusted internal services (like SpeckleService).
            // Consider more secure ways to pass credentials if SpeckleService runs in a less trusted environment.
            var link = await _context.SpeckleProjectLinks
                                     .AsNoTracking() // No need to track changes for a read-only operation
                                     .FirstOrDefaultAsync(l => l.P2PProjectId == p2pProjectId);

            return link?.SpeckleAuthToken;
        }

        public async Task<bool> DeleteLinkAsync(Guid linkId, Guid userId)
        {
            var link = await _context.SpeckleProjectLinks.FindAsync(linkId);
            if (link == null) return false;

            // TODO: Add any business logic before deletion, e.g., check permissions for 'userId'.
            _context.SpeckleProjectLinks.Remove(link);
            return await _context.SaveChangesAsync() > 0;
        }

        // Helper for manual mapping (replace with AutoMapper if available)
        private SpeckleProjectLinkDto MapToDto(SpeckleProjectLink link, string? projectName)
        {
            if (link == null) return null;
            return new SpeckleProjectLinkDto
            {
                Id = link.Id,
                P2PProjectId = link.P2PProjectId,
                ProjectName = projectName ?? "N/A", // Handle if project name is not available
                SpeckleServerUrl = link.SpeckleServerUrl,
                SpeckleStreamId = link.SpeckleStreamId,
                SpeckleCommitId = link.SpeckleCommitId,
                // SECURITY: Do not expose full token. Provide a hint or status.
                SpeckleAuthTokenHint = string.IsNullOrEmpty(link.SpeckleAuthToken) ? "Not Set" : "Token Set",
                SpeckleAuthToken = link.SpeckleAuthToken, // Pass the token for viewer (with security note)
                LinkedAt = link.LinkedAt,
                LastRefreshedAt = link.LastRefreshedAt
            };
        }

        public async Task<IEnumerable<SpeckleObjectProcurementStatusDto>> GetSpeckleObjectStatusesAsync(Guid speckleProjectLinkId)
        {
            var objectMetadataList = await _context.SpeckleObjectMetadata
                .Where(m => m.SpeckleProjectLinkId == speckleProjectLinkId)
                .ToListAsync();

            var result = new List<SpeckleObjectProcurementStatusDto>();

            foreach (var metadata in objectMetadataList)
            {
                // Find the first procurement link for this specific speckle object metadata
                var procurementLink = await _context.ProcurementSpeckleLinks
                    .FirstOrDefaultAsync(l => l.SpeckleObjectMetadataId == metadata.Id);

                var statusDto = new SpeckleObjectProcurementStatusDto
                {
                    SpeckleObjectId = metadata.SpeckleObjectId,
                    HasActiveLink = procurementLink != null,
                    ProcurementSpeckleLinkId = procurementLink?.Id,
                    PrimaryProcurementItemType = procurementLink?.ProcurementItemType,
                    PrimaryProcurementItemId = procurementLink?.ProcurementItemId,
                    VisualOverrideKey = DetermineVisualOverrideKey(procurementLink)
                };
                result.Add(statusDto);
            }

            return result;
        }

        private string DetermineVisualOverrideKey(ProcurementSpeckleLink? link)
        {
            if (link == null)
            {
                return "NOT_LINKED";
            }

            return link.ProcurementItemType switch
            {
                "PurchaseOrderLine" => "LINKED_PO",
                "RequisitionLine" => "LINKED_REQUISITION",
                // Add more cases as procurement item types are defined
                _ => "LINKED_GENERIC",
            };
        }
    }
}
