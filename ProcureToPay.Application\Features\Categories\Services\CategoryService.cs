using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using ProcureToPay.Application.Features.Categories.DTOs;
using ProcureToPay.Domain.Entities;
using ProcureToPay.Domain.Interfaces;
using ProcureToPay.Infrastructure.Persistence;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace ProcureToPay.Application.Features.Categories.Services
{
    /// <summary>
    /// Service implementation for managing categories.
    /// </summary>
    public class CategoryService : ICategoryService
    {
        private readonly IDbContextFactory<ApplicationDbContext> _dbContextFactory;
        private readonly ITenantProvider _tenantProvider;
        private readonly ILogger<CategoryService> _logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="CategoryService"/> class.
        /// </summary>
        /// <param name="dbContextFactory">The database context factory.</param>
        /// <param name="tenantProvider">The tenant provider.</param>
        /// <param name="logger">The logger.</param>
        public CategoryService(
            IDbContextFactory<ApplicationDbContext> dbContextFactory,
            ITenantProvider tenantProvider,
            ILogger<CategoryService> logger)
        {
            _dbContextFactory = dbContextFactory ?? throw new ArgumentNullException(nameof(dbContextFactory));
            _tenantProvider = tenantProvider ?? throw new ArgumentNullException(nameof(tenantProvider));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <inheritdoc/>
        public async Task<IEnumerable<CategoryDto>> GetCategoriesAsync()
        {
            var tenantId = _tenantProvider.GetCurrentTenantId();
            if (!tenantId.HasValue)
            {
                _logger.LogWarning("No tenant ID available when getting categories");
                return Enumerable.Empty<CategoryDto>();
            }

            await using var context = await _dbContextFactory.CreateDbContextAsync();
            
            return await context.ProductCategories
                .Where(c => c.TenantId == tenantId.Value)
                .Select(c => new CategoryDto
                {
                    Id = c.Id,
                    Name = c.Name,
                    Description = c.Description,
                    Code = c.Code,
                    UnspscCode = c.UnspscCode,
                    ParentCategoryId = c.ParentCategoryId,
                    ParentCategoryName = c.ParentCategory != null ? c.ParentCategory.Name : null,
                    ProductCount = c.Products.Count
                })
                .OrderBy(c => c.Name)
                .ToListAsync();
        }

        /// <inheritdoc/>
        public async Task<CategoryDto?> GetCategoryByIdAsync(Guid categoryId)
        {
            var tenantId = _tenantProvider.GetCurrentTenantId();
            if (!tenantId.HasValue)
            {
                _logger.LogWarning("No tenant ID available when getting category by ID {CategoryId}", categoryId);
                return null;
            }

            await using var context = await _dbContextFactory.CreateDbContextAsync();
            
            return await context.ProductCategories
                .Where(c => c.Id == categoryId && c.TenantId == tenantId.Value)
                .Select(c => new CategoryDto
                {
                    Id = c.Id,
                    Name = c.Name,
                    Description = c.Description,
                    Code = c.Code,
                    UnspscCode = c.UnspscCode,
                    ParentCategoryId = c.ParentCategoryId,
                    ParentCategoryName = c.ParentCategory != null ? c.ParentCategory.Name : null,
                    ProductCount = c.Products.Count
                })
                .FirstOrDefaultAsync();
        }

        /// <inheritdoc/>
        public async Task<(CategoryDto? Category, IEnumerable<string> Errors)> CreateCategoryAsync(CreateCategoryRequest request)
        {
            var errors = new List<string>();
            var tenantId = _tenantProvider.GetCurrentTenantId();
            
            if (!tenantId.HasValue)
            {
                errors.Add("No tenant ID available");
                return (null, errors);
            }

            await using var context = await _dbContextFactory.CreateDbContextAsync();
            
            // Validate code uniqueness within tenant
            if (!string.IsNullOrWhiteSpace(request.Code) && 
                await context.ProductCategories.AnyAsync(c => c.TenantId == tenantId.Value && c.Code == request.Code))
            {
                errors.Add($"A category with code '{request.Code}' already exists");
                return (null, errors);
            }

            // Validate parent category exists and belongs to the same tenant
            if (request.ParentCategoryId.HasValue)
            {
                var parentCategory = await context.ProductCategories
                    .FirstOrDefaultAsync(c => c.Id == request.ParentCategoryId.Value && c.TenantId == tenantId.Value);
                
                if (parentCategory == null)
                {
                    errors.Add("Parent category not found or does not belong to the current tenant");
                    return (null, errors);
                }
            }

            try
            {
                var category = new Category(
                    Guid.NewGuid(),
                    tenantId.Value,
                    request.Name,
                    request.Description,
                    request.Code,
                    request.UnspscCode,
                    request.ParentCategoryId);

                context.ProductCategories.Add(category);
                await context.SaveChangesAsync();

                _logger.LogInformation("Category '{Name}' created for tenant '{TenantId}'", category.Name, tenantId);

                return (new CategoryDto
                {
                    Id = category.Id,
                    Name = category.Name,
                    Description = category.Description,
                    Code = category.Code,
                    UnspscCode = category.UnspscCode,
                    ParentCategoryId = category.ParentCategoryId,
                    ProductCount = 0
                }, Enumerable.Empty<string>());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating category '{Name}' for tenant '{TenantId}'", request.Name, tenantId);
                errors.Add($"Error creating category: {ex.Message}");
                return (null, errors);
            }
        }

        /// <inheritdoc/>
        public async Task<(CategoryDto? Category, IEnumerable<string> Errors)> UpdateCategoryAsync(UpdateCategoryRequest request)
        {
            var errors = new List<string>();
            var tenantId = _tenantProvider.GetCurrentTenantId();
            
            if (!tenantId.HasValue)
            {
                errors.Add("No tenant ID available");
                return (null, errors);
            }

            await using var context = await _dbContextFactory.CreateDbContextAsync();
            
            // Get the category
            var category = await context.ProductCategories
                .FirstOrDefaultAsync(c => c.Id == request.Id && c.TenantId == tenantId.Value);
            
            if (category == null)
            {
                errors.Add("Category not found or does not belong to the current tenant");
                return (null, errors);
            }

            // Validate code uniqueness within tenant
            if (!string.IsNullOrWhiteSpace(request.Code) && 
                await context.ProductCategories.AnyAsync(c => c.TenantId == tenantId.Value && c.Code == request.Code && c.Id != request.Id))
            {
                errors.Add($"A category with code '{request.Code}' already exists");
                return (null, errors);
            }

            // Validate parent category exists and belongs to the same tenant
            if (request.ParentCategoryId.HasValue)
            {
                // Prevent circular reference
                if (request.ParentCategoryId.Value == request.Id)
                {
                    errors.Add("A category cannot be its own parent");
                    return (null, errors);
                }

                var parentCategory = await context.ProductCategories
                    .FirstOrDefaultAsync(c => c.Id == request.ParentCategoryId.Value && c.TenantId == tenantId.Value);
                
                if (parentCategory == null)
                {
                    errors.Add("Parent category not found or does not belong to the current tenant");
                    return (null, errors);
                }
            }

            try
            {
                // Update the category
                category.UpdateDetails(request.Name, request.Description, request.Code, request.UnspscCode);
                category.AssignParent(request.ParentCategoryId);

                await context.SaveChangesAsync();

                _logger.LogInformation("Category '{Name}' (ID: {Id}) updated for tenant '{TenantId}'", category.Name, category.Id, tenantId);

                return (new CategoryDto
                {
                    Id = category.Id,
                    Name = category.Name,
                    Description = category.Description,
                    Code = category.Code,
                    UnspscCode = category.UnspscCode,
                    ParentCategoryId = category.ParentCategoryId,
                    ProductCount = category.Products.Count
                }, Enumerable.Empty<string>());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating category (ID: {Id}) for tenant '{TenantId}'", request.Id, tenantId);
                errors.Add($"Error updating category: {ex.Message}");
                return (null, errors);
            }
        }

        /// <inheritdoc/>
        public async Task<bool> DeleteCategoryAsync(Guid categoryId)
        {
            var tenantId = _tenantProvider.GetCurrentTenantId();
            if (!tenantId.HasValue)
            {
                _logger.LogWarning("No tenant ID available when deleting category {CategoryId}", categoryId);
                return false;
            }

            await using var context = await _dbContextFactory.CreateDbContextAsync();
            
            var category = await context.ProductCategories
                .Include(c => c.ChildCategories)
                .Include(c => c.Products)
                .FirstOrDefaultAsync(c => c.Id == categoryId && c.TenantId == tenantId.Value);
            
            if (category == null)
            {
                _logger.LogWarning("Category {CategoryId} not found or does not belong to tenant {TenantId}", categoryId, tenantId);
                return false;
            }

            // Check if the category has child categories or products
            if (category.ChildCategories.Any())
            {
                _logger.LogWarning("Cannot delete category {CategoryId} because it has child categories", categoryId);
                return false;
            }

            if (category.Products.Any())
            {
                _logger.LogWarning("Cannot delete category {CategoryId} because it has products", categoryId);
                return false;
            }

            try
            {
                context.ProductCategories.Remove(category);
                await context.SaveChangesAsync();

                _logger.LogInformation("Category (ID: {Id}) deleted for tenant '{TenantId}'", categoryId, tenantId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting category {CategoryId} for tenant {TenantId}", categoryId, tenantId);
                return false;
            }
        }

        /// <inheritdoc/>
        public async Task<IEnumerable<CategoryDto>> GetParentCategoryCandidatesAsync(Guid? currentCategoryId = null)
        {
            var tenantId = _tenantProvider.GetCurrentTenantId();
            if (!tenantId.HasValue)
            {
                _logger.LogWarning("No tenant ID available when getting parent category candidates");
                return Enumerable.Empty<CategoryDto>();
            }

            await using var context = await _dbContextFactory.CreateDbContextAsync();
            
            var query = context.ProductCategories
                .Where(c => c.TenantId == tenantId.Value);
            
            // Exclude the current category to prevent self-reference
            if (currentCategoryId.HasValue)
            {
                query = query.Where(c => c.Id != currentCategoryId.Value);
            }

            return await query
                .Select(c => new CategoryDto
                {
                    Id = c.Id,
                    Name = c.Name,
                    Code = c.Code
                })
                .OrderBy(c => c.Name)
                .ToListAsync();
        }
    }
}
