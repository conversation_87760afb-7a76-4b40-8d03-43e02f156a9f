using System;

namespace ProcureToPay.Application.DTOs.Speckle
{
    public class SpeckleObjectProcurementStatusDto
    {
        public string SpeckleObjectId { get; set; } = null!; // From SpeckleObjectMetadata.SpeckleObjectId
        public bool HasActiveLink { get; set; }
        public Guid? ProcurementSpeckleLinkId { get; set; } // ID of the link itself, if one exists
        public string? PrimaryProcurementItemType { get; set; } // e.g., "PurchaseOrderLine"
        public Guid? PrimaryProcurementItemId { get; set; } // e.g., PurchaseOrderLineId
        public string VisualOverrideKey { get; set; } = "NOT_LINKED"; // e.g., "NOT_LINKED", "LINKED_PO", "LINKED_REQUISITION"

        // Optional: Add more details if readily available and useful for direct display
        // public string? ProcurementItemDescription { get; set; }
        // public string? PurchaseOrderNumber { get; set; }
    }
}
