using System.ComponentModel.DataAnnotations;

namespace ProcureToPay.Application.DTOs.Auth
{
    public class RegisterUserRequest
    {
        [Required]
        [StringLength(256, MinimumLength = 3)]
        public string Username { get; set; } = string.Empty;

        [Required]
        [EmailAddress]
        [StringLength(256)]
        public string Email { get; set; } = string.Empty;

        [Required]
        [StringLength(100, MinimumLength = 8)] // Basic length check, actual policy is more complex
        public string Password { get; set; } = string.Empty;
        // UserRole will be set by business logic, not directly by external request for now.
        // Or, could be part of request if self-registration implies a default role.
        // For FR-USR-001, focus is on password complexity and admin setup.
    }
}
