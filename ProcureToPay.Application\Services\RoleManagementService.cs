using Microsoft.EntityFrameworkCore;
using ProcureToPay.Application.DTOs.Role; // Will create this DTO later if needed for complex return types
using ProcureToPay.Domain.Constants; // For Permissions class
using ProcureToPay.Domain.Entities;
using ProcureToPay.Infrastructure.Persistence;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace ProcureToPay.Application.Services
{
    public interface IRoleManagementService
    {
        Task<IEnumerable<RoleDto>> GetRolesAsync(); // RoleDto will be simple for now
        Task<IEnumerable<string>> GetPermissionsForRoleAsync(Guid roleId);
        Task<(bool Success, IEnumerable<string> Errors)> UpdatePermissionsForRoleAsync(Guid roleId, List<string> newPermissions, string? ipAddress);
        Task<IEnumerable<string>> GetAllPermissionsAsync();
        // LogAuditEvent is in IAuthenticationService, will inject that or move LogAuditEvent to a shared service later.
    }

    // RoleDto is now in ProcureToPay.Application.DTOs.Role namespace

    public class RoleManagementService : IRoleManagementService
    {
        private readonly ApplicationDbContext _context;
        private readonly IAuthenticationService _authenticationService; // For logging

        public RoleManagementService(ApplicationDbContext context, IAuthenticationService authenticationService)
        {
            _context = context;
            _authenticationService = authenticationService;
        }

        public async Task<IEnumerable<RoleDto>> GetRolesAsync()
        {
            return await _context.P2PRoles // Renamed from Roles
                .Select(r => new RoleDto { Id = r.Id, Name = r.Name, Description = r.Description })
                .ToListAsync();
        }

        public async Task<IEnumerable<string>> GetPermissionsForRoleAsync(Guid roleId)
        {
            return await _context.RolePermissions
                .Where(rp => rp.RoleId == roleId)
                .Select(rp => rp.PermissionString)
                .ToListAsync();
        }

        public async Task<IEnumerable<string>> GetAllPermissionsAsync()
        {
            return await Task.FromResult(Permissions.All());
        }

        public async Task<(bool Success, IEnumerable<string> Errors)> UpdatePermissionsForRoleAsync(Guid roleId, List<string> newPermissions, string? ipAddress)
        {
            var role = await _context.P2PRoles.Include(r => r.RolePermissions).FirstOrDefaultAsync(r => r.Id == roleId); // Renamed from Roles
            if (role == null)
            {
                return (false, new List<string> { "Role not found." });
            }

            var errors = new List<string>();

            // Validate new permissions to ensure they are defined in the system
            var allSystemPermissions = Permissions.All();
            foreach (var perm in newPermissions)
            {
                if (!allSystemPermissions.Contains(perm))
                {
                    errors.Add($"Permission '{perm}' is not a valid system permission.");
                }
            }
            if (errors.Any()) return (false, errors);

            // Get current permissions for the role
            var currentPermissions = role.RolePermissions.Select(rp => rp.PermissionString).ToList();

            // Permissions to add
            var permissionsToAdd = newPermissions.Except(currentPermissions).ToList();
            foreach (var permToAdd in permissionsToAdd)
            {
                _context.RolePermissions.Add(new RolePermission(roleId, permToAdd));
            }

            // Permissions to remove
            var permissionsToRemoveStrings = currentPermissions.Except(newPermissions).ToList();
            var rolePermissionsToRemove = role.RolePermissions.Where(rp => permissionsToRemoveStrings.Contains(rp.PermissionString)).ToList();
            if (rolePermissionsToRemove.Any())
            {
                _context.RolePermissions.RemoveRange(rolePermissionsToRemove);
            }

            if (!permissionsToAdd.Any() && !rolePermissionsToRemove.Any())
            {
                 return (true, Enumerable.Empty<string>()); // No changes
            }

            await _context.SaveChangesAsync();
            await _authenticationService.LogAuditEvent(
                actionType: "PermissionsUpdatedForRole",
                success: true,
                performingUserId: null, // Placeholder: This should be the ID of the admin performing the action
                targetEntityType: "Role",
                targetEntityId: roleId.ToString(),
                ipAddress: ipAddress,
                details: $"Permissions updated for role {role.Name}. Added: [{string.Join(", ", permissionsToAdd)}]. Removed: [{string.Join(", ", permissionsToRemoveStrings)}].");

            return (true, Enumerable.Empty<string>());
        }
    }
}
