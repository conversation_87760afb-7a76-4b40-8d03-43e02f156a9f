using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore; // For ToListAsync, FirstOrDefaultAsync
using ProcureToPay.Domain.Enums; // For UserRole enum
using ProcureToPay.Infrastructure.Persistence; // For ApplicationDbContext
using System;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace ProcureToPay.WebApp.AuthZ // New namespace
{
    public class PermissionHandler : AuthorizationHandler<PermissionRequirement>
    {
        private readonly IServiceProvider _serviceProvider; // To resolve scoped DbContext

        public PermissionHandler(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
        }

        protected override async Task HandleRequirementAsync(AuthorizationHandlerContext context, PermissionRequirement requirement)
        {
            var userIdClaim = context.User.FindFirst(ClaimTypes.NameIdentifier);
            var userRoleClaim = context.User.FindFirst(ClaimTypes.Role); // Assuming UserRole enum string is stored in standard Role claim

            if (userIdClaim == null || userRoleClaim == null)
            {
                // User not authenticated or role claim missing
                context.Fail();
                return;
            }

            if (!Enum.TryParse<UserRole>(userRoleClaim.Value, true, out var userRoleEnum))
            {
                // Invalid role claim
                context.Fail(new AuthorizationFailureReason(this, $"Invalid role claim: {userRoleClaim.Value}"));
                return;
            }

            // Resolve DbContext from a new scope since AuthorizationHandlers can be singletons
            // or live longer than a request, while DbContext is scoped.
            using (var scope = _serviceProvider.CreateScope())
            {
                var dbContext = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();

                // Construct the role name as seeded (e.g., "SystemRole_Buyer")
                string roleNameInDb = $"SystemRole_{userRoleEnum}";

                var role = await dbContext.P2PRoles
                                          .AsNoTracking()
                                          .FirstOrDefaultAsync(r => r.Name == roleNameInDb);

                if (role == null)
                {
                    context.Fail(new AuthorizationFailureReason(this, $"Role '{roleNameInDb}' not found in database."));
                    return;
                }

                var hasPermission = await dbContext.RolePermissions
                                                   .AsNoTracking()
                                                   .AnyAsync(rp => rp.RoleId == role.Id && rp.PermissionString == requirement.Permission);

                if (hasPermission)
                {
                    context.Succeed(requirement);
                }
                else
                {
                    // Optionally log failed permission check
                    // _logger.LogWarning($"User {userIdClaim.Value} (Role: {userRoleEnum}) failed permission check for {requirement.Permission}");
                    context.Fail(new AuthorizationFailureReason(this, $"Permission '{requirement.Permission}' denied for role '{userRoleEnum}'."));
                }
            }
        }
    }
}
