using System;

namespace ProcureToPay.Domain.Entities
{
    public class SpeckleObjectMetadata : BaseEntity<Guid>
    {
        public Guid SpeckleProjectLinkId { get; set; }
        public virtual SpeckleProjectLink SpeckleProjectLink { get; set; } = null!; // Navigation property

        public string SpeckleObjectId { get; set; } = null!; // Spec<PERSON>'s internal ID (hash or applicationID)
        public string? RevitElementId { get; set; } // Optional, e.g., from obj["elementId"]
        public string? IfcGuid { get; set; } // Optional, e.g., from obj["ifcGuid"]
        public string ObjectType { get; set; } = null!; // E.g., obj.speckle_type or obj["category"]

        /// <summary>
        /// Stores a JSON representation of various extracted parameters.
        /// </summary>
        public string ParametersJson { get; set; } = "{}"; // Default to empty JSON object

        public DateTime LastExtractedAt { get; set; }

        // Constructor for required fields
        public SpeckleObjectMetadata(Guid speckleProjectLinkId, string speckleObjectId, string objectType, string parametersJson)
            : base(Guid.NewGuid())
        {
            SpeckleProjectLinkId = speckleProjectLinkId;
            SpeckleObjectId = speckleObjectId ?? throw new ArgumentNullException(nameof(speckleObjectId));
            ObjectType = objectType ?? throw new ArgumentNullException(nameof(objectType));
            ParametersJson = parametersJson ?? throw new ArgumentNullException(nameof(parametersJson));
            LastExtractedAt = DateTime.UtcNow;
        }

        // Parameterless constructor for EF Core
        protected SpeckleObjectMetadata() : base(Guid.NewGuid())
        {
            // Properties will be set by EF Core.
            // Null forgiving operator used for non-nullable ones not set in constructor.
        }
    }
}
