using System;
using ProcureToPay.Domain.Entities; // Assuming BaseEntity is here or it's not needed if not inheriting

namespace ProcureToPay.Infrastructure.Persistence.Entities
{
    public class SqlMigrationHistory // If BaseEntity provides Id, inherit from it: : BaseEntity
    {
        public int Id { get; set; } // Or Guid Id if BaseEntity uses Guid
        public string ScriptName { get; set; } = null!;
        public DateTime AppliedDate { get; set; }
    }
}
