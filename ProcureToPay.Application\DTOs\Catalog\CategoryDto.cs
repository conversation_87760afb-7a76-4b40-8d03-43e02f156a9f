using System;
using System.Collections.Generic;

namespace ProcureToPay.Application.DTOs.Catalog
{
    public class CategoryDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
        public Guid? ParentCategoryId { get; set; }
        public string? ParentCategoryName { get; set; } // For display
        public bool IsActive { get; set; }
        public int SubcategoryCount { get; set; } // Number of ProductCategories
        public int ProductCount { get; set; } // Number of CatalogProducts in this category
        public List<CategoryDto> Subcategories { get; set; } = new List<CategoryDto>(); // For hierarchical view
    }
}
