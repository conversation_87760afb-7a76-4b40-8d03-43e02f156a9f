using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProcureToPay.Domain.Entities;
using ProcureToPay.Domain.Enums;
using ProcureToPay.Domain.ValueObjects; // For Money, Address
using System;

namespace ProcureToPay.Infrastructure.Persistence.Configurations
{
    /// <summary>
    /// EF Core Configuration for the PurchaseRequisition entity.
    /// </summary>
    public class PurchaseRequisitionConfiguration : IEntityTypeConfiguration<PurchaseRequisition>
    {

        public void Configure(EntityTypeBuilder<PurchaseRequisition> builder)
        {
            builder.ToTable("purchase_requisitions");

            // Assuming PurchaseRequisition inherits from BaseEntity<Guid>
            builder.HasKey(pr => pr.Id);

            // --- Concurrency Control (PostgreSQL xmin - EF Core 7+) ---
            builder.Property<uint>("xmin")
                   .HasColumnType("xid")
                   .ValueGeneratedOnAddOrUpdate()
                   .IsConcurrencyToken();

            // --- Properties ---
            builder.Property(pr => pr.TenantId)
                   .IsRequired();
            builder.HasIndex(pr => pr.TenantId); // Index for tenant filtering

            builder.Property(pr => pr.RequisitionNumber)
                   .IsRequired()
                   .HasMaxLength(50);
            // Unique index within a tenant
            builder.HasIndex(pr => new { pr.TenantId, pr.RequisitionNumber }).IsUnique();

            builder.Property(pr => pr.RequestorName)
                   .IsRequired()
                   .HasMaxLength(150);

            builder.Property(pr => pr.RequestorEmail)
                   .IsRequired()
                   .HasMaxLength(254);

            builder.Property(pr => pr.RequestorUserId)
                   .HasMaxLength(450); // Match Identity User Id length if applicable
            builder.HasIndex(pr => pr.RequestorUserId); // Index for lookup

            builder.Property(pr => pr.Department)
                   .IsRequired()
                   .HasMaxLength(100);

            builder.Property(pr => pr.RequestDate)
                   .IsRequired();
            builder.HasIndex(pr => pr.RequestDate); // Index for date filtering

            builder.Property(pr => pr.DateNeeded); // Nullable DateTime

            builder.Property(pr => pr.Justification)
                   .IsRequired()
                   .HasMaxLength(1000);

            // Map Status Enum
            builder.Property(pr => pr.Status)
                   .IsRequired()
                   .HasConversion<string>()
                   .HasMaxLength(50);
            builder.HasIndex(pr => pr.Status); // Index for filtering by status

            // Configure Currency Code
            builder.Property(pr => pr.CurrencyCode)
                   .IsRequired()
                   .HasMaxLength(3);

            builder.Property(pr => pr.Notes)
                   .HasMaxLength(2000); // Example max length

            builder.Property(pr => pr.AssociatedPurchaseOrderId); // Nullable Guid
            builder.HasIndex(pr => pr.AssociatedPurchaseOrderId);


            // --- Value Object Mapping ---

            // Embed TotalEstimatedCost (Money VO)
            builder.OwnsOne(pr => pr.TotalEstimatedCost, cost =>
            {
                cost.Property(m => m.Amount)
                    .HasColumnName("total_estimated_cost_amount")
                    .HasPrecision(18, 4) // Consistent precision
                    .IsRequired();
                // Ignore CurrencyCode mapping here as it's a direct property on PurchaseRequisition
                cost.Ignore(m => m.CurrencyCode);
            });
            builder.Navigation(pr => pr.TotalEstimatedCost).IsRequired();

            // Embed ShippingAddress (nullable Address VO)
            builder.OwnsOne(pr => pr.ShippingAddress, addr =>
            {
                addr.Property(a => a.Street).HasColumnName("shipping_street").HasMaxLength(200); // Nullable based on VO property
                addr.Property(a => a.City).HasColumnName("shipping_city").HasMaxLength(100);
                addr.Property(a => a.State).HasColumnName("shipping_state").HasMaxLength(100);
                addr.Property(a => a.Country).HasColumnName("shipping_country").HasMaxLength(100);
                addr.Property(a => a.PostalCode).HasColumnName("shipping_postal_code").HasMaxLength(20);
            });
            // Navigation is implicitly optional as ShippingAddress property is nullable


            // --- Relationships ---

            // Relationship to Lines (One Requisition to Many Lines)
            builder.HasMany(pr => pr.Lines)
                   .WithOne(prl => prl.PurchaseRequisition) // Assumes PurchaseRequisitionLine has PurchaseRequisition nav prop
                   .HasForeignKey(prl => prl.PurchaseRequisitionId) // Assumes PurchaseRequisitionLine has FK
                   .IsRequired()
                   .OnDelete(DeleteBehavior.Cascade); // Deleting header deletes lines

            // Relationship to primary AssociatedPurchaseOrder (One Requisition to Zero or One PO)
            // This configures the optional navigation property on the Requisition side.
            // The main relationship (PO -> Req) is configured in PurchaseOrderConfiguration.
            builder.HasOne(pr => pr.AssociatedPurchaseOrder)
                   .WithMany() // A PO might be associated with only one Req in this specific way
                   .HasForeignKey(pr => pr.AssociatedPurchaseOrderId)
                   .IsRequired(false)
                   .OnDelete(DeleteBehavior.SetNull); // If PO deleted, nullify this link

            // Relationship to potentially multiple PurchaseOrders generated from this Req
            // Configured primarily on PurchaseOrder side (HasOne Req -> WithMany POs)
            // Ensure inverse property exists on PurchaseRequisition: ICollection<PurchaseOrder> PurchaseOrders
            builder.HasMany(pr => pr.PurchaseOrders)
                  .WithOne(po => po.Requisition)
                  .HasForeignKey(po => po.RequisitionId)
                  .IsRequired(false) // PO doesn't always require a Requisition
                  .OnDelete(DeleteBehavior.SetNull); // If Req deleted, nullify link on PO


            // --- Tenant Isolation ---
            // Note: Tenant filtering should be implemented at the DbContext level
            // Example:
            // builder.HasQueryFilter(pr => pr.TenantId == currentTenantId);
            // Where currentTenantId is obtained from the DbContext or a tenant provider service


            // --- Seeding (Optional Example) ---
            // Guid sampleTenantId = Guid.Parse("..."); // Replace with actual Tenant ID if seeding
            // builder.HasData(
            //     new PurchaseRequisition(
            //         id: Guid.NewGuid(),
            //         tenantId: sampleTenantId,
            //         requisitionNumber: "REQ-2025-0001",
            //         requestorName: "Default User",
            //         requestorEmail: "<EMAIL>",
            //         department: "IT",
            //         justification: "Need new monitors for developers",
            //         currencyCode: "SAR"
            //         // Initialize Money/Address appropriately if seeding non-defaults
            //     )
            //     // Add more seed data as needed
            // );
        }
    }
}

