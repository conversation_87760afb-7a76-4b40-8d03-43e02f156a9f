using System;
using System.Collections.Generic;

namespace ProcureToPay.Application.DTOs.Speckle
{
    public class BillOfMaterialDto
    {
        public Guid SpeckleProjectLinkId { get; set; }
        public DateTime GeneratedAt { get; set; }

        /// <summary>
        /// The properties used to group items in this BOM.
        /// </summary>
        public List<string> GroupByProperties { get; set; } = new List<string>();

        /// <summary>
        /// The list of column headers for the BOM table.
        /// This will include GroupByProperties, IncludeParametersInBom, and "Quantity".
        /// </summary>
        public List<string> Headers { get; set; } = new List<string>();

        /// <summary>
        /// The actual items in the Bill of Material.
        /// </summary>
        public List<BillOfMaterialItemDto> Items { get; set; } = new List<BillOfMaterialItemDto>();
    }
}
