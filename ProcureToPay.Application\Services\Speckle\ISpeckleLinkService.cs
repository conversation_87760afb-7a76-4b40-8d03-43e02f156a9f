using ProcureToPay.Application.DTOs.Speckle;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ProcureToPay.Application.Services.Speckle
{
    public interface ISpeckleLinkService
    {
        Task<SpeckleProjectLinkDto> CreateLinkAsync(CreateSpeckleLinkRequest request, Guid userId); // Assuming userId for audit/permissions
        Task<SpeckleProjectLinkDto?> GetLinkByIdAsync(Guid linkId);
        Task<SpeckleProjectLinkDto?> GetLinkByP2PProjectIdAsync(Guid p2pProjectId);
        Task<IEnumerable<SpeckleProjectLinkDto>> GetAllLinksAsync(); // Consider pagination for large datasets
        Task<SpeckleProjectLinkDto?> GetLinkBySpeckleStreamIdAsync(string speckleStreamId); // For SpeckleService to find its context
        Task<bool> UpdateLinkAsync(Guid linkId, CreateSpeckleLinkRequest request, Guid userId); // Full update, or dedicated update DTOs
        Task<bool> UpdateLinkCommitAsync(Guid linkId, string newCommitId, Guid userId);
        Task<bool> DeleteLinkAsync(Guid linkId, Guid userId);
        Task<string?> GetSpeckleAuthTokenAsync(Guid p2pProjectId); // Securely retrieve token for SpeckleService

        Task<IEnumerable<SpeckleObjectProcurementStatusDto>> GetSpeckleObjectStatusesAsync(Guid speckleProjectLinkId);
    }
}
