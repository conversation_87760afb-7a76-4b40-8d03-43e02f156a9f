using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProcureToPay.Domain.Entities;
using ProcureToPay.Domain.Enums;
using ProcureToPay.Domain.ValueObjects; // For Money VO
using System;

namespace ProcureToPay.Infrastructure.Persistence.Configurations
{
    /// <summary>
    /// EF Core Configuration for the VendorProduct entity.
    /// </summary>
    public class VendorProductConfiguration : IEntityTypeConfiguration<VendorProduct>
    {
        public void Configure(EntityTypeBuilder<VendorProduct> builder)
        {
            builder.ToTable("vendor_products");

            // Assuming VendorProduct inherits from BaseEntity<Guid>
            builder.HasKey(vp => vp.Id);

            // --- Properties ---
            builder.Property(vp => vp.VendorSku)
                .HasMaxLength(100); // Max length for vendor-specific SKU

            // Configure UnitOfMeasure Enum conversion
            builder.Property(vp => vp.UnitOfMeasure)
                .IsRequired()
                .HasConversion<string>() // Store as string
                .HasMaxLength(50); // Max length for enum string representation

            // Configure PackSize (nullable decimal)
            builder.Property(vp => vp.PackSize)
                .HasPrecision(18, 4); // Adjust precision as needed, make nullable in DB

            builder.Property(vp => vp.IsActive)
                .IsRequired();

            builder.Property(vp => vp.LeadTimeDays); // Nullable integer

            // --- Value Object Mapping (Money for UnitPrice) ---
            // Assumes Money VO has Amount (decimal) and CurrencyCode (string) properties
            builder.OwnsOne(vp => vp.UnitPrice, price =>
            {
                price.Property(m => m.Amount)
                    .HasColumnName("unit_price_amount") // Explicit column name
                    .HasPrecision(18, 4) // Match precision from previous Product config
                    .IsRequired();

                price.Property(m => m.CurrencyCode)
                    .HasColumnName("unit_price_currency_code") // Explicit column name
                    .HasMaxLength(3) // Standard ISO code length
                    .IsRequired();
            });
            // Ensure the owned entity itself is required
            builder.Navigation(vp => vp.UnitPrice).IsRequired();


            // --- Relationships ---

            // Relationship to ProductDefinition (Many VendorProducts to One ProductDefinition)
            builder.HasOne(vp => vp.ProductDefinition)
                   .WithMany(pd => pd.VendorProducts) // Assumes ProductDefinition has ICollection<VendorProduct> VendorProducts
                   .HasForeignKey(vp => vp.ProductDefinitionId)
                   .IsRequired()
                   .OnDelete(DeleteBehavior.Restrict); // Prevent deleting master definition if vendor offerings exist

            // Relationship to Vendor (Many VendorProducts to One Vendor)
            builder.HasOne(vp => vp.Vendor)
                   .WithMany(v => v.VendorProducts) // Assumes Vendor has ICollection<VendorProduct> VendorProducts
                   .HasForeignKey(vp => vp.VendorId)
                   .IsRequired()
                   .OnDelete(DeleteBehavior.Restrict); // Prevent deleting vendor if offerings exist

            // Relationship to PurchaseOrderLines (One VendorProduct to Many Lines)
            builder.HasMany(vp => vp.PurchaseOrderLines)
                   .WithOne(pol => pol.VendorProduct)
                   .HasForeignKey(pol => pol.VendorProductId)
                   .OnDelete(DeleteBehavior.Restrict); // Prevent deleting VendorProduct if used in POs


            // --- Indexes ---

            // Index for efficient lookup by Vendor + SKU (if used)
            builder.HasIndex(vp => new { vp.VendorId, vp.VendorSku })
                   .HasFilter(@"""vendor_sku"" IS NOT NULL"); // Index only non-null SKUs

            // Unique Index for Option B: Vendor + ProductDefinition + UoM + PackSize defines a unique offering
            // Note: PostgreSQL treats nulls as distinct in unique constraints by default.
            // If you need null PackSize to be unique per Vendor/Product/UoM, this index should work.
            // If you need stricter null handling, research PostgreSQL specific index options (e.g., partial indexes).
            builder.HasIndex(vp => new { vp.VendorId, vp.ProductDefinitionId, vp.UnitOfMeasure, vp.PackSize })
                   .IsUnique();


            // --- Tenant Isolation ---
            // Assuming VendorProduct belongs to a Tenant (likely via Vendor or ProductDefinition relationship)
            // If direct TenantId needed:
            // builder.Property<Guid>("TenantId").IsRequired();
            // builder.HasIndex("TenantId");
            // builder.HasQueryFilter(vp => EF.Property<Guid>(vp, "TenantId") == _tenantId); // Requires _tenantId in DbContext

        }
    }
}

