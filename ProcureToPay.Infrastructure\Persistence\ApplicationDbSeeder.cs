using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using ProcureToPay.Domain.Entities;
using ProcureToPay.Domain.Interfaces;
using ProcureToPay.Domain.Constants; // Added for Permissions class
using ProcureToPay.Infrastructure.Identity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace ProcureToPay.Infrastructure.Persistence
{
    /// <summary>
    /// Service for seeding application data that requires complex logic or access to services.
    /// This is used for data that can't be easily seeded with EF Core's HasData method.
    /// </summary>
    public class ApplicationDbSeeder
    {
        private readonly ILogger<ApplicationDbSeeder> _logger;
        private readonly ApplicationDbContext _dbContext;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly RoleManager<IdentityRole> _roleManager;
        private readonly IServiceScopeFactory _scopeFactory;

        // Default tenant ID (must match the one in TenantConfiguration)
        private readonly Guid _defaultTenantId = Guid.Parse("11111111-1111-1111-1111-111111111111");

        public ApplicationDbSeeder(
            ILogger<ApplicationDbSeeder> logger,
            ApplicationDbContext dbContext,
            UserManager<ApplicationUser> userManager,
            RoleManager<IdentityRole> roleManager,
            IServiceScopeFactory scopeFactory)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
            _userManager = userManager ?? throw new ArgumentNullException(nameof(userManager));
            _roleManager = roleManager ?? throw new ArgumentNullException(nameof(roleManager));
            _scopeFactory = scopeFactory ?? throw new ArgumentNullException(nameof(scopeFactory));
        }

        /// <summary>
        /// Seeds all required application data.
        /// </summary>
        public async Task SeedAllAsync()
        {
            try
            {
                _logger.LogInformation("Starting application data seeding...");

                // Create a scope to ensure tenant context is properly handled
                using (var scope = _scopeFactory.CreateScope())
                {
                    // Ensure the default tenant exists (should be created by migrations)
                    await EnsureDefaultTenantExistsAsync();

                    // Seed roles first
                    await SeedRolesAsync();

                    // Seed users with roles
                    await SeedUsersAsync();

                    // Seed departments if needed (fallback if HasData doesn't work)
                    await EnsureDepartmentsExistAsync();

                    // Seed default password policy
                    await SeedDefaultPasswordPolicyAsync();

                    // Seed RBAC roles and permissions
                    await SeedRolesAndPermissionsAsync();

                    // Additional seeding can be added here
                    // await SeedCustomDataAsync();

                    _logger.LogInformation("Application data seeding completed successfully.");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An error occurred while seeding application data.");
                throw;
            }
        }

        /// <summary>
        /// Ensures the default tenant exists in the database.
        /// </summary>
        private async Task EnsureDefaultTenantExistsAsync()
        {
            if (!await _dbContext.Set<Tenant>().AnyAsync(t => t.Id == _defaultTenantId))
            {
                _logger.LogWarning("Default tenant not found in database. This should have been created by migrations.");

                // Create the default tenant if it doesn't exist
                var defaultTenant = new Tenant(
                    "Default System Tenant",
                    "system",
                    "Standard",
                    "<EMAIL>");

                // Use reflection to set the ID property
                var idProperty = typeof(Tenant).GetProperty("Id");
                idProperty?.SetValue(defaultTenant, _defaultTenantId);

                // Activate the tenant
                defaultTenant.Activate();

                _dbContext.Add(defaultTenant);
                await _dbContext.SaveChangesAsync();
                _logger.LogInformation("Created default tenant.");
            }
        }

        /// <summary>
        /// Seeds default roles for the application.
        /// </summary>
        private async Task SeedRolesAsync()
        {
            _logger.LogInformation("Seeding roles...");

            string[] roleNames = {
                "SystemAdmin",
                "TenantAdmin",
                "Approver",
                "Purchaser",
                "Requester",
                "FinanceManager",
                "InventoryManager",
                "Viewer"
            };

            foreach (var roleName in roleNames)
            {
                if (!await _roleManager.RoleExistsAsync(roleName))
                {
                    await _roleManager.CreateAsync(new IdentityRole(roleName));
                    _logger.LogInformation("Created role: {RoleName}", roleName);
                }
            }
        }

        /// <summary>
        /// Seeds default users for the application.
        /// </summary>
        private async Task SeedUsersAsync()
        {
            _logger.LogInformation("Seeding users...");

            // Admin user
            var adminUser = new ApplicationUser
            {
                UserName = "<EMAIL>",
                Email = "<EMAIL>",
                EmailConfirmed = true
            };

            await CreateUserIfNotExistsAsync(adminUser, "Admin@123456", new[] { "SystemAdmin", "TenantAdmin" });

            // Demo users
            var demoUsers = new List<(ApplicationUser User, string Password, string[] Roles)>
            {
                (
                    new ApplicationUser
                    {
                        UserName = "<EMAIL>",
                        Email = "<EMAIL>",
                        EmailConfirmed = true
                    },
                    "Approver@123456",
                    new[] { "Approver" }
                ),
                (
                    new ApplicationUser
                    {
                        UserName = "<EMAIL>",
                        Email = "<EMAIL>",
                        EmailConfirmed = true
                    },
                    "Purchaser@123456",
                    new[] { "Purchaser" }
                ),
                (
                    new ApplicationUser
                    {
                        UserName = "<EMAIL>",
                        Email = "<EMAIL>",
                        EmailConfirmed = true
                    },
                    "Requester@123456",
                    new[] { "Requester" }
                ),
                (
                    new ApplicationUser
                    {
                        UserName = "<EMAIL>",
                        Email = "<EMAIL>",
                        EmailConfirmed = true
                    },
                    "Finance@123456",
                    new[] { "FinanceManager" }
                ),
                (
                    new ApplicationUser
                    {
                        UserName = "<EMAIL>",
                        Email = "<EMAIL>",
                        EmailConfirmed = true
                    },
                    "Inventory@123456",
                    new[] { "InventoryManager" }
                ),
                (
                    new ApplicationUser
                    {
                        UserName = "<EMAIL>",
                        Email = "<EMAIL>",
                        EmailConfirmed = true
                    },
                    "Viewer@123456",
                    new[] { "Viewer" }
                )
            };

            foreach (var (user, password, roles) in demoUsers)
            {
                await CreateUserIfNotExistsAsync(user, password, roles);
            }
        }

        /// <summary>
        /// Creates a user if it doesn't already exist and assigns roles.
        /// </summary>
        private async Task CreateUserIfNotExistsAsync(ApplicationUser user, string password, string[] roles)
        {
            if (user.Email == null)
            {
                _logger.LogWarning("User email is null. Cannot create user without an email.");
                return;
            }

            var existingUser = await _userManager.FindByEmailAsync(user.Email);

            if (existingUser == null)
            {
                var result = await _userManager.CreateAsync(user, password);

                if (result.Succeeded)
                {
                    _logger.LogInformation("Created user: {UserEmail}", user.Email);

                    foreach (var role in roles)
                    {
                        await _userManager.AddToRoleAsync(user, role);
                    }

                    _logger.LogInformation("Assigned roles to user: {UserEmail}", user.Email);
                }
                else
                {
                    var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                    _logger.LogWarning("Failed to create user {UserEmail}. Errors: {Errors}", user.Email, errors);
                }
            }
            else
            {
                _logger.LogInformation("User already exists: {UserEmail}", user.Email);

                // Ensure the user has all the required roles
                var userRoles = await _userManager.GetRolesAsync(existingUser);
                var rolesToAdd = roles.Except(userRoles);

                if (rolesToAdd.Any())
                {
                    await _userManager.AddToRolesAsync(existingUser, rolesToAdd);
                    _logger.LogInformation("Added missing roles to existing user: {UserEmail}", user.Email);
                }
            }
        }

        /// <summary>
        /// Ensures that default departments exist in the database.
        /// This is a fallback in case the HasData seeding in DepartmentConfiguration doesn't work.
        /// </summary>
        private async Task EnsureDepartmentsExistAsync()
        {
            _logger.LogInformation("Checking for default departments...");

            // Define department data
            var departments = new List<(Guid Id, string Name, string Code, string Description)>
            {
                (Guid.Parse("********-4444-4444-4444-********4401"), "Information Technology", "IT", "Handles IT infrastructure, software development, and technical support."),
                (Guid.Parse("********-4444-4444-4444-********4402"), "Finance", "FIN", "Manages financial operations, budgeting, and accounting."),
                (Guid.Parse("********-4444-4444-4444-********4403"), "Human Resources", "HR", "Handles employee recruitment, training, and personnel management."),
                (Guid.Parse("********-4444-4444-4444-********4404"), "Procurement", "PROC", "Manages purchasing, vendor relationships, and supply chain operations."),
                (Guid.Parse("********-4444-4444-4444-********4405"), "Operations", "OPS", "Oversees day-to-day operational activities and logistics."),
                (Guid.Parse("********-4444-4444-4444-********4406"), "Marketing", "MKT", "Handles advertising, brand management, and market research.")
            };

            // Check if departments already exist
            var existingDepartmentIds = await _dbContext.Departments
                .Where(d => d.TenantId == _defaultTenantId)
                .Select(d => d.Id)
                .ToListAsync();

            int createdCount = 0;

            // Create any missing departments
            foreach (var (id, name, code, description) in departments)
            {
                if (!existingDepartmentIds.Contains(id))
                {
                    var department = new Department(id, _defaultTenantId, name, code, description);
                    _dbContext.Departments.Add(department);
                    createdCount++;
                    _logger.LogInformation("Created department: {DepartmentName} ({DepartmentCode})", name, code);
                }
            }

            if (createdCount > 0)
            {
                await _dbContext.SaveChangesAsync();
                _logger.LogInformation("Created {Count} missing departments", createdCount);
            }
            else
            {
                _logger.LogInformation("All default departments already exist");
            }
        }

        /// <summary>
        /// Seeds a default password policy if none exists for the default tenant.
        /// </summary>
        private async Task SeedDefaultPasswordPolicyAsync()
        {
            _logger.LogInformation("Checking for default password policy...");

            if (!await _dbContext.PasswordPolicies.AnyAsync(p => p.TenantId == _defaultTenantId))
            {
                var defaultPolicy = new PasswordPolicy(Guid.NewGuid(), _defaultTenantId)
                {
                    MinLength = 8,
                    RequireUppercase = true,
                    RequireLowercase = true,
                    RequireDigit = true,
                    RequireSpecialCharacter = true,
                    PasswordHistoryCount = 5,
                    PasswordExpirationDays = 90,
                    MaxFailedLoginAttempts = 5,
                    AccountLockoutDurationMinutes = 30
                };
                _dbContext.PasswordPolicies.Add(defaultPolicy);
                await _dbContext.SaveChangesAsync();
                _logger.LogInformation("Created default password policy for tenant {TenantId}", _defaultTenantId);
            }
            else
            {
                _logger.LogInformation("Default password policy already exists for tenant {TenantId}", _defaultTenantId);
            }
        }

        private async Task SeedRolesAndPermissionsAsync()
        {
            _logger.LogInformation("Seeding default roles and permissions...");

            var existingRoles = await _dbContext.P2PRoles.ToDictionaryAsync(r => r.Name, r => r.Id); // Renamed from Roles
            var existingRolePermissions = await _dbContext.RolePermissions
                .Select(rp => new { rp.RoleId, rp.PermissionString })
                .ToListAsync();

            var rolesToSeed = new List<(string Name, string Description, List<string> Permissions)>
            {
                ("SystemRole_Buyer", "Default role for buyers", new List<string>
                {
                    Permissions.ViewUserProfile, Permissions.UpdateUserProfile, Permissions.ChangePassword, Permissions.ManageMfaSettings,
                    Permissions.ViewCategories, Permissions.ViewApprovedProducts, // FR-CAT-001
                    Permissions.CreateRequisition, Permissions.ViewOwnRequisitions,
                    Permissions.ViewOwnPurchaseOrders,
                    Permissions.ViewOwnReports
                }),
                ("SystemRole_Seller", "Default role for sellers", new List<string>
                {
                    Permissions.ViewUserProfile, Permissions.UpdateUserProfile, Permissions.ChangePassword, Permissions.ManageMfaSettings,
                    Permissions.ViewCategories, Permissions.ViewApprovedProducts, // FR-CAT-001
                    Permissions.ManageOwnProducts, Permissions.SubmitProductsForApproval, Permissions.ViewOwnProducts, // FR-CAT-001
                    Permissions.ViewOwnReports
                    // Add permissions for viewing orders related to their products, etc.
                }),
                ("SystemRole_BuyerAdmin", "Administrative role for a buyer organization", new List<string>
                {
                    Permissions.ViewUserProfile, Permissions.UpdateUserProfile, Permissions.ChangePassword, Permissions.ManageMfaSettings,
                    Permissions.ViewCategories, Permissions.ViewApprovedProducts, // FR-CAT-001
                    Permissions.CreateRequisition, Permissions.ViewOwnRequisitions, Permissions.ViewAllRequisitions,
                    Permissions.ApproveRequisition,
                    Permissions.ViewOwnPurchaseOrders, Permissions.ViewAllPurchaseOrders,
                    Permissions.ViewUsersOrganization, Permissions.ManageUsersOrganization,
                    Permissions.ViewSystemReports
                }),
                ("SystemRole_SellerAdmin", "Administrative role for a seller organization", new List<string>
                {
                    Permissions.ViewUserProfile, Permissions.UpdateUserProfile, Permissions.ChangePassword, Permissions.ManageMfaSettings,
                    Permissions.ViewCategories, Permissions.ViewApprovedProducts, // FR-CAT-001
                    Permissions.ManageOwnProducts, Permissions.SubmitProductsForApproval, Permissions.ViewOwnProducts, // FR-CAT-001
                    Permissions.ViewUsersOrganization, Permissions.ManageUsersOrganization,
                    Permissions.ViewSystemReports
                }),
                ("SystemRole_MarketplaceAdmin", "Top-level administrator for the marketplace", Permissions.All()) // Has all permissions
            };

            foreach (var roleInfo in rolesToSeed)
            {
                Guid roleId;
                if (!existingRoles.TryGetValue(roleInfo.Name, out roleId))
                {
                    var newRole = new Role(Guid.NewGuid(), roleInfo.Name, roleInfo.Description);
                    _dbContext.P2PRoles.Add(newRole); // Renamed from Roles
                    await _dbContext.SaveChangesAsync(); // Save to get Id
                    roleId = newRole.Id;
                    _logger.LogInformation("Created role: {RoleName}", roleInfo.Name);
                }

                foreach (var permission in roleInfo.Permissions)
                {
                    if (!existingRolePermissions.Any(rp => rp.RoleId == roleId && rp.PermissionString == permission))
                    {
                        _dbContext.RolePermissions.Add(new RolePermission(roleId, permission));
                        _logger.LogInformation("Assigned permission '{Permission}' to role '{RoleName}'", permission, roleInfo.Name);
                    }
                }
            }
            await _dbContext.SaveChangesAsync(); // Save all new RolePermissions
            _logger.LogInformation("Default roles and permissions seeding completed.");
        }
    }
}
