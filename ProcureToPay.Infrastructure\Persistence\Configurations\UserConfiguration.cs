using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProcureToPay.Domain.Entities;
using System.Text.Json;

namespace ProcureToPay.Infrastructure.Persistence.Configurations
{
    public class UserConfiguration : IEntityTypeConfiguration<User>
    {
        public void Configure(EntityTypeBuilder<User> builder)
        {
            builder.HasKey(u => u.Id);

            builder.Property(u => u.Username)
                .IsRequired()
                .HasMaxLength(256);

            builder.HasIndex(u => u.Username)
                .IsUnique();

            builder.Property(u => u.Email)
                .IsRequired()
                .HasMaxLength(256);

            builder.HasIndex(u => u.Email)
                .IsUnique();

            builder.Property(u => u.PasswordHash)
                .IsRequired();

            builder.Property(u => u.UserRole)
                .IsRequired();

            builder.Property(u => u.MfaSecretKey)
                .IsRequired(false);

            builder.Property(u => u.SsoProvider)
                .IsRequired(false)
                .HasMaxLength(100);

            builder.Property(u => u.SsoUserId)
                .IsRequired(false)
                .HasMaxLength(256);

            builder.Property(e => e.PasswordHistory)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, null as JsonSerializerOptions),
                    v => JsonSerializer.Deserialize<List<string>>(v, null as JsonSerializerOptions) ?? new List<string>())
                .HasColumnType("jsonb");

            // Profile properties
            builder.Property(u => u.FullName)
                .HasMaxLength(256)
                .IsRequired(false);

            builder.Property(u => u.PhoneNumber)
                .HasMaxLength(50) // Standard length for phone numbers with country code
                .IsRequired(false);

            builder.Property(u => u.PreferredLanguage)
                .HasMaxLength(10) // e.g., "en-US"
                .IsRequired(false);

            builder.Property(u => u.NotificationPreferences)
                .IsRequired(false); // Could be JSON, potentially large, so no max length here by default

            builder.Property(u => u.IsEmailVerified)
                .IsRequired(); // Defaults to false in entity constructor

            // User Administration fields (FR-USR-004)
            builder.Property(u => u.OrganizationId)
                .IsRequired(false); // Nullable

            builder.HasIndex(u => u.OrganizationId); // Index for faster querying by org

            builder.Property(u => u.IsActive)
                .IsRequired(); // Defaults to true in entity constructor

            builder.Property(u => u.PasswordResetTokenHash)
                .IsRequired(false)
                .HasMaxLength(512); // Accommodate longer hash strings if needed

            builder.Property(u => u.PasswordResetTokenExpiryUtc)
                .IsRequired(false);


            // Consider adding an index for SsoProvider and SsoUserId if they are frequently queried
            // builder.HasIndex(u => new { u.SsoProvider, u.SsoUserId }).IsUnique();
        }
    }
}
