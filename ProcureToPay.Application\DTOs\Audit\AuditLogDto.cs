using System;

namespace ProcureToPay.Application.DTOs.Audit
{
    public class AuditLogDto
    {
        public Guid Id { get; set; }
        public DateTime Timestamp { get; set; }
        public Guid? UserId { get; set; }
        public string? Username { get; set; } // Potentially populated
        public Guid? PerformingUserId { get; set; }
        public string? PerformingUsername { get; set; } // Potentially populated
        public string ActionType { get; set; } = string.Empty;
        public bool Success { get; set; }
        public string? TargetEntityType { get; set; }
        public string? TargetEntityId { get; set; }
        public string? IpAddress { get; set; }
        public string? Details { get; set; }
    }

    // A helper for paginated results, can be generic later if needed
    public class PaginatedAuditLogDto
    {
        public IEnumerable<AuditLogDto> Items { get; set; } = new List<AuditLogDto>();
        public int PageNumber { get; set; }
        public int PageSize { get; set; }
        public int TotalCount { get; set; }
        public int TotalPages { get; set; }
    }
}
