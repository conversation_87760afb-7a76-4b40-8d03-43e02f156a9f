using System.ComponentModel.DataAnnotations;

namespace ProcureToPay.Application.DTOs.Profile
{
    public class UpdateUserProfileRequest
    {
        [EmailAddress]
        public string? Email { get; set; } // Optional: if user wants to change email

        [StringLength(256)]
        public string? FullName { get; set; }

        [Phone]
        [StringLength(50)]
        public string? PhoneNumber { get; set; }

        [StringLength(10)] // e.g., "en-US"
        public string? PreferredLanguage { get; set; }

        public string? NotificationPreferences { get; set; } // Could be JSON string
    }
}
