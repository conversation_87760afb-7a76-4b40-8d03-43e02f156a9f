using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProcureToPay.Domain.Entities;

namespace ProcureToPay.Infrastructure.Persistence.Configurations
{
    public class SpeckleProjectLinkConfiguration : IEntityTypeConfiguration<SpeckleProjectLink>
    {
        public void Configure(EntityTypeBuilder<SpeckleProjectLink> builder)
        {
            builder.ToTable("SpeckleProjectLinks"); // Define the table name

            // BaseEntity already configures the Id as PK. If not, uncomment below:
            // builder.HasKey(spl => spl.Id);
            // builder.Property(spl => spl.Id).ValueGeneratedOnAdd();

            builder.Property(spl => spl.SpeckleServerUrl)
                .IsRequired()
                .HasMaxLength(2048); // Typical URL max length

            builder.Property(spl => spl.SpeckleStreamId)
                .IsRequired()
                .HasMaxLength(1024); // Stream IDs are usually shorter

            builder.Property(spl => spl.SpeckleCommitId)
                .HasMaxLength(1024); // Commit IDs can also be long

            // SECURITY NOTE: Storing tokens directly is a risk.
            // Consider encryption or integration with a secure vault for production.
            // For now, max length is arbitrary, adjust if token format is known.
            builder.Property(spl => spl.SpeckleAuthToken)
                .IsRequired()
                .HasMaxLength(2048);

            builder.Property(spl => spl.LinkedAt)
                .IsRequired();

            // Configure the foreign key relationship to the Project entity
            builder.HasOne(spl => spl.Project)
                   .WithMany() // Assuming Project entity does not have a direct navigation collection property for SpeckleProjectLinks. If it does, specify it here.
                   .HasForeignKey(spl => spl.P2PProjectId)
                   .IsRequired()
                   .OnDelete(DeleteBehavior.Cascade); // Or Restrict, depending on business rules

            // Add an index for P2PProjectId for faster lookups
            builder.HasIndex(spl => spl.P2PProjectId).IsUnique(false); // False if one P2PProject can have multiple links (e.g. to different streams)
                                                                    // True if one P2PProject can only link to one Speckle stream/setup.
                                                                    // Based on GetLinkByProjectIdAsync, it seems one P2P project has one link. Let's make it unique.
            builder.HasIndex(spl => spl.P2PProjectId).IsUnique(true);


            // If SpeckleProjectLink implements ITenantEntity, ensure TenantId is configured.
            // Assuming BaseEntity or SpeckleProjectLink itself handles TenantId if it's an ITenantEntity.
            // If ITenantEntity is implemented and TenantId is not in BaseEntity:
            // builder.Property(spl => spl.TenantId).IsRequired();
            // builder.HasIndex(spl => spl.TenantId);
        }
    }
}
