using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ProcureToPay.Application.DTOs.Admin;
using ProcureToPay.Application.Services;
using ProcureToPay.Domain.Constants; // For Permissions
using ProcureToPay.Domain.Enums; // For UserRole
using System;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace ProcureToPay.WebApp.Controllers
{
    [Authorize] // All admin actions require authentication
    [ApiController]
    [Route("api/admin")]
    public class AdminController : ControllerBase
    {
        private readonly IUserManagementService _userManagementService;
        private readonly IPermissionValidationService _permissionService;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public AdminController(
            IUserManagementService userManagementService,
            IPermissionValidationService permissionService,
            IHttpContextAccessor httpContextAccessor)
        {
            _userManagementService = userManagementService;
            _permissionService = permissionService;
            _httpContextAccessor = httpContextAccessor;
        }

        private Guid GetCurrentUserId()
        {
            var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
            if (string.IsNullOrEmpty(userIdClaim) || !Guid.TryParse(userIdClaim, out var userId))
            {
                throw new InvalidOperationException("User ID not found or invalid in token.");
            }
            return userId;
        }
        private string? GetIpAddress() => _httpContextAccessor.HttpContext?.Connection.RemoteIpAddress?.ToString();

        [HttpGet("users")]
        public async Task<IActionResult> GetUsers([FromQuery] string? organizationIdFilter = null) // Query param for potential future use by platform admin
        {
            try
            {
                var performingAdminUserId = GetCurrentUserId();
                // Permission check is handled within the service GetUsersAsync based on who is calling
                var users = await _userManagementService.GetUsersAsync(performingAdminUserId, GetIpAddress());
                return Ok(users);
            }
            catch (InvalidOperationException ex) // From GetCurrentUserId
            {
                return Unauthorized(new { Message = ex.Message });
            }
        }

        [HttpPost("users")]
        public async Task<IActionResult> CreateUser([FromBody] AdminCreateUserRequest request)
        {
            if (!ModelState.IsValid) return BadRequest(ModelState);
            try
            {
                var performingAdminUserId = GetCurrentUserId();
                // Permission check is handled within the service
                var (success, userId, errors) = await _userManagementService.AdminCreateUserAsync(performingAdminUserId, request, GetIpAddress());
                if (success) return CreatedAtAction(nameof(GetUserById), new { targetUserId = userId }, new { UserId = userId, Message = "User created successfully." });
                return BadRequest(new { Message = "User creation failed.", Errors = errors });
            }
            catch (InvalidOperationException ex)
            {
                return Unauthorized(new { Message = ex.Message });
            }
        }

        // Dummy GetUserById for CreatedAtAction (not part of FR-USR-004 spec but good for RESTful response)
        [HttpGet("users/{targetUserId}")]
        public async Task<IActionResult> GetUserById(Guid targetUserId)
        {
            // This endpoint would need its own permission check, e.g., ViewUsersPlatform or ViewUsersOrganization if target is in org
            // For simplicity, just returning a placeholder if the AdminUserDto were available or a generic message
             var performingAdminUserId = GetCurrentUserId();
             if(!await _permissionService.HasPermissionAsync(performingAdminUserId, Permissions.ViewUsersPlatform, GetIpAddress()) &&
                !await _permissionService.HasPermissionAsync(performingAdminUserId, Permissions.ViewUsersOrganization, GetIpAddress()))
             {
                 return Forbid();
             }
            // In a real scenario, fetch the user DTO here.
            return Ok(new { Message = $"Details for user {targetUserId}. (Actual user data not implemented in this specific helper endpoint)" });
        }


        [HttpPut("users/{targetUserId}/role")]
        public async Task<IActionResult> AssignUserRole(Guid targetUserId, [FromBody] AdminAssignUserRoleRequest request)
        {
            if (!ModelState.IsValid) return BadRequest(ModelState);
            try
            {
                var performingAdminUserId = GetCurrentUserId();
                var (success, errors) = await _userManagementService.AdminAssignUserRoleAsync(performingAdminUserId, targetUserId, request.NewRole, GetIpAddress());
                if (success) return Ok(new { Message = "User role assigned successfully." });
                return BadRequest(new { Message = "Failed to assign user role.", Errors = errors });
            }
            catch (InvalidOperationException ex)
            {
                return Unauthorized(new { Message = ex.Message });
            }
        }

        [HttpPut("users/{targetUserId}/activation")]
        public async Task<IActionResult> SetUserActivation(Guid targetUserId, [FromBody] AdminSetUserActivationRequest request)
        {
            if (!ModelState.IsValid) return BadRequest(ModelState);
            try
            {
                var performingAdminUserId = GetCurrentUserId();
                var (success, errors) = await _userManagementService.AdminSetUserActivationAsync(performingAdminUserId, targetUserId, request.IsActive, GetIpAddress());
                if (success) return Ok(new { Message = $"User activation status set to {request.IsActive}." });
                return BadRequest(new { Message = "Failed to set user activation status.", Errors = errors });
            }
            catch (InvalidOperationException ex)
            {
                return Unauthorized(new { Message = ex.Message });
            }
        }

        [HttpPost("users/{targetUserId}/initiate-password-reset")]
        public async Task<IActionResult> InitiatePasswordReset(Guid targetUserId)
        {
            try
            {
                var performingAdminUserId = GetCurrentUserId();
                var (success, tokenPreview, errors) = await _userManagementService.AdminInitiatePasswordResetAsync(performingAdminUserId, targetUserId, GetIpAddress());
                if (success) return Ok(new { Message = "Password reset initiated. Token (for demo): " + tokenPreview });
                return BadRequest(new { Message = "Failed to initiate password reset.", Errors = errors });
            }
            catch (InvalidOperationException ex)
            {
                return Unauthorized(new { Message = ex.Message });
            }
        }
    }
}
