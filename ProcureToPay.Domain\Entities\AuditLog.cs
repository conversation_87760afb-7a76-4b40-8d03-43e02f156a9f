using System;

namespace ProcureToPay.Domain.Entities
{
    public class AuditLog : BaseEntity<Guid>
    {
        public DateTime Timestamp { get; private set; }
        public Guid? UserId { get; private set; } // User the event is primarily about (e.g., user being modified, user logging in)
        public Guid? PerformingUserId { get; private set; } // User who performed the action (e.g., an admin)
        public string ActionType { get; private set; } // e.g., "LoginAttempt", "UserUpdatedProfile", "RolePermissionChanged"
        public bool Success { get; private set; } // Outcome: True for success, False for failure/denied
        public string? TargetEntityType { get; private set; } // e.g., "User", "Role", "Product"
        public string? TargetEntityId { get; private set; } // ID of the entity targeted by the action
        public string? IpAddress { get; private set; }
        public string? Details { get; private set; } // Additional information, e.g., error message, parameters, or JSON diff of changes

        // Private constructor for EF Core
        private AuditLog() : base(Guid.NewGuid())
        {
            ActionType = string.Empty; // Initialize non-nullable string
        }

        public AuditLog(
            string actionType,
            bool success,
            Guid? userId = null, // User context of the event
            Guid? performingUserId = null, // User performing action
            string? targetEntityType = null,
            string? targetEntityId = null,
            string? ipAddress = null,
            string? details = null)
            : base(Guid.NewGuid())
        {
            Timestamp = DateTime.UtcNow;
            ActionType = actionType ?? throw new ArgumentNullException(nameof(actionType));
            Success = success;
            UserId = userId;
            PerformingUserId = performingUserId;
            TargetEntityType = targetEntityType;
            TargetEntityId = targetEntityId;
            IpAddress = ipAddress;
            Details = details;
        }
    }
}
