{"ConnectionStrings": {"postgresdb": "Host={postgres.bindings.tcp.host};Port={postgres.bindings.tcp.port};Username={postgres.env.POSTGRES_USER};Password={postgres.env.POSTGRES_PASSWORD};Database=procuretopaydb"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "Speckle": {"ServerUrl": "YOUR_SPECKLE_SERVER_URL", "ApiToken": "YOUR_SPECKLE_API_TOKEN", "StreamId": "YOUR_DEFAULT_STREAM_ID", "BranchName": "main", "PollingIntervalSeconds": 60}}