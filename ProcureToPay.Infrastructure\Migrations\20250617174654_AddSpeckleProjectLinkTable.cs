﻿using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using ProcureToPay.Domain.ValueObjects;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace ProcureToPay.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddSpeckleProjectLinkTable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.EnsureSchema(
                name: "public");

            migrationBuilder.EnsureSchema(
                name: "catalog");

            migrationBuilder.CreateTable(
                name: "AspNetRoles",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    Name = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    NormalizedName = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ConcurrencyStamp = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AspNetRoles", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "AspNetUsers",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    UserName = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    NormalizedUserName = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    Email = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    NormalizedEmail = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    EmailConfirmed = table.Column<bool>(type: "boolean", nullable: false),
                    PasswordHash = table.Column<string>(type: "text", nullable: true),
                    SecurityStamp = table.Column<string>(type: "text", nullable: true),
                    ConcurrencyStamp = table.Column<string>(type: "text", nullable: true),
                    PhoneNumber = table.Column<string>(type: "text", nullable: true),
                    PhoneNumberConfirmed = table.Column<bool>(type: "boolean", nullable: false),
                    TwoFactorEnabled = table.Column<bool>(type: "boolean", nullable: false),
                    LockoutEnd = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    LockoutEnabled = table.Column<bool>(type: "boolean", nullable: false),
                    AccessFailedCount = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AspNetUsers", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "AuditLogs",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Timestamp = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UserId = table.Column<Guid>(type: "uuid", nullable: true),
                    PerformingUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    ActionType = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    Success = table.Column<bool>(type: "boolean", nullable: false),
                    TargetEntityType = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    TargetEntityId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    IpAddress = table.Column<string>(type: "character varying(45)", maxLength: 45, nullable: true),
                    Details = table.Column<string>(type: "text", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AuditLogs", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "budgets",
                schema: "public",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    Description = table.Column<string>(type: "text", nullable: true),
                    FiscalYear = table.Column<int>(type: "integer", nullable: false),
                    StartDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    EndDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    baseline_amount = table.Column<decimal>(type: "numeric(18,4)", nullable: false),
                    forecast_amount = table.Column<decimal>(type: "numeric(18,4)", nullable: true),
                    CurrencyCode = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Version = table.Column<int>(type: "integer", nullable: false, defaultValue: 1),
                    IsRollingForecast = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    ForecastPeriodsJson = table.Column<string>(type: "jsonb", nullable: true),
                    AllocationRulesJson = table.Column<string>(type: "jsonb", nullable: true),
                    WorkflowInstanceId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedById = table.Column<string>(type: "character varying(450)", maxLength: 450, nullable: false),
                    ApprovedById = table.Column<string>(type: "character varying(450)", maxLength: 450, nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_budgets", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "categories",
                schema: "public",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    Description = table.Column<string>(type: "text", nullable: true),
                    Code = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    UnspscCode = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    ParentCategoryId = table.Column<Guid>(type: "uuid", nullable: true),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_categories", x => x.Id);
                    table.ForeignKey(
                        name: "FK_categories_categories_ParentCategoryId",
                        column: x => x.ParentCategoryId,
                        principalSchema: "public",
                        principalTable: "categories",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "customers",
                schema: "public",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: false),
                    customer_code = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    name = table.Column<string>(type: "text", nullable: false),
                    legal_name = table.Column<string>(type: "text", nullable: true),
                    tax_identifier = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    is_active = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    credit_limit_amount = table.Column<decimal>(type: "numeric(18,2)", nullable: true),
                    credit_limit_currency_code = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: true),
                    default_payment_terms = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    default_currency_code = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: true),
                    website = table.Column<string>(type: "text", nullable: true),
                    customer_type = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    billing_street = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    billing_city = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    billing_state = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    billing_country = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    billing_postal_code = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    shipping_street = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    shipping_city = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    shipping_state = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    shipping_country = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    shipping_postal_code = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    primary_contact_name = table.Column<string>(type: "character varying(150)", maxLength: 150, nullable: false),
                    primary_contact_role = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    primary_contact_email = table.Column<string>(type: "character varying(254)", maxLength: 254, nullable: true),
                    primary_contact_phone = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    AssignedSalesRepId = table.Column<string>(type: "character varying(450)", maxLength: 450, nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_customers", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "departments",
                schema: "public",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    Code = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    Description = table.Column<string>(type: "text", nullable: true),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_departments", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "DocumentLink",
                columns: table => new
                {
                    Name = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    Url = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: false),
                    DocumentType = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true)
                },
                constraints: table =>
                {
                });

            migrationBuilder.CreateTable(
                name: "P2PRoles",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_P2PRoles", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "P2PUsers",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Username = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    PasswordHash = table.Column<string>(type: "text", nullable: false),
                    Email = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    UserRole = table.Column<int>(type: "integer", nullable: false),
                    IsMfaEnabled = table.Column<bool>(type: "boolean", nullable: false),
                    MfaSecretKey = table.Column<string>(type: "text", nullable: true),
                    SsoProvider = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    SsoUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    LastLoginDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    FailedLoginAttempts = table.Column<int>(type: "integer", nullable: false),
                    LockoutEndDateUtc = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    PasswordLastChangedDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    PasswordHistory = table.Column<string>(type: "jsonb", nullable: false),
                    FullName = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    PhoneNumber = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    PreferredLanguage = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: true),
                    NotificationPreferences = table.Column<string>(type: "text", nullable: true),
                    IsEmailVerified = table.Column<bool>(type: "boolean", nullable: false),
                    OrganizationId = table.Column<Guid>(type: "uuid", nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    PasswordResetTokenHash = table.Column<string>(type: "character varying(512)", maxLength: 512, nullable: true),
                    PasswordResetTokenExpiryUtc = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_P2PUsers", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "PasswordPolicies",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: false),
                    MinLength = table.Column<int>(type: "integer", nullable: false),
                    RequireUppercase = table.Column<bool>(type: "boolean", nullable: false),
                    RequireLowercase = table.Column<bool>(type: "boolean", nullable: false),
                    RequireDigit = table.Column<bool>(type: "boolean", nullable: false),
                    RequireSpecialCharacter = table.Column<bool>(type: "boolean", nullable: false),
                    PasswordHistoryCount = table.Column<int>(type: "integer", nullable: false),
                    PasswordExpirationDays = table.Column<int>(type: "integer", nullable: false),
                    MaxFailedLoginAttempts = table.Column<int>(type: "integer", nullable: false),
                    AccountLockoutDurationMinutes = table.Column<int>(type: "integer", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PasswordPolicies", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "procurement_workflows",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: false),
                    WorkflowName = table.Column<string>(type: "character varying(150)", maxLength: 150, nullable: false),
                    Name = table.Column<string>(type: "character varying(150)", maxLength: 150, nullable: false),
                    Description = table.Column<string>(type: "text", maxLength: 500, nullable: true),
                    SubjectDocumentId = table.Column<Guid>(type: "uuid", nullable: false),
                    SubjectDocumentType = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    WorkflowType = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    Version = table.Column<int>(type: "integer", maxLength: 20, nullable: true),
                    CurrentStatus = table.Column<int>(type: "integer", nullable: false),
                    StartedDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CompletedDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    InitiatedByUserId = table.Column<string>(type: "character varying(450)", maxLength: 450, nullable: true),
                    InitiatedByName = table.Column<string>(type: "character varying(150)", maxLength: 150, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_procurement_workflows", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "ProductCategories",
                schema: "catalog",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    Description = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    ParentCategoryId = table.Column<Guid>(type: "uuid", nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ProductCategories", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ProductCategories_ProductCategories_ParentCategoryId",
                        column: x => x.ParentCategoryId,
                        principalSchema: "catalog",
                        principalTable: "ProductCategories",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "projects",
                schema: "public",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: false),
                    ProjectCode = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    Description = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    StartDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    EndDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    budget_amount = table.Column<decimal>(type: "numeric(18,2)", precision: 18, scale: 2, nullable: true),
                    budget_currency_code = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_projects", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "sales_territories",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(150)", maxLength: 150, nullable: false),
                    Code = table.Column<string>(type: "text", nullable: true),
                    TerritoryCode = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    ParentTerritoryId = table.Column<Guid>(type: "uuid", nullable: true),
                    Description = table.Column<string>(type: "text", nullable: true),
                    PrimarySalespersonId = table.Column<string>(type: "text", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_sales_territories", x => x.Id);
                    table.ForeignKey(
                        name: "FK_sales_territories_sales_territories_ParentTerritoryId",
                        column: x => x.ParentTerritoryId,
                        principalTable: "sales_territories",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "SqlMigrationHistories",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    ScriptName = table.Column<string>(type: "text", nullable: false),
                    AppliedDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SqlMigrationHistories", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "tenant_products",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    SKU = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    Description = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    Price = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_tenant_products", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "tenants",
                schema: "public",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    Identifier = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    SubscriptionPlan = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    ContactEmail = table.Column<string>(type: "character varying(254)", maxLength: 254, nullable: true),
                    Settings = table.Column<string>(type: "jsonb", nullable: true),
                    AddressLine1 = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    City = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    PostalCode = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    Country = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_tenants", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "test_entities",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_test_entities", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "vendors",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(250)", maxLength: 250, nullable: false),
                    VendorCode = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false, defaultValue: "Pending"),
                    VatNumber = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    CommercialRegistrationNumber = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    TaxId = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    ContactName = table.Column<string>(type: "character varying(150)", maxLength: 150, nullable: true),
                    ContactEmail = table.Column<string>(type: "character varying(254)", maxLength: 254, nullable: true),
                    PhoneNumber = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    Website = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    primary_address_street = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    primary_address_city = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    primary_address_state = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    primary_address_country = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    primary_address_postal_code = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_vendors", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "AspNetRoleClaims",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    RoleId = table.Column<string>(type: "text", nullable: false),
                    ClaimType = table.Column<string>(type: "text", nullable: true),
                    ClaimValue = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AspNetRoleClaims", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AspNetRoleClaims_AspNetRoles_RoleId",
                        column: x => x.RoleId,
                        principalTable: "AspNetRoles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "AspNetUserClaims",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    UserId = table.Column<string>(type: "text", nullable: false),
                    ClaimType = table.Column<string>(type: "text", nullable: true),
                    ClaimValue = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AspNetUserClaims", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AspNetUserClaims_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "AspNetUserLogins",
                columns: table => new
                {
                    LoginProvider = table.Column<string>(type: "text", nullable: false),
                    ProviderKey = table.Column<string>(type: "text", nullable: false),
                    ProviderDisplayName = table.Column<string>(type: "text", nullable: true),
                    UserId = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AspNetUserLogins", x => new { x.LoginProvider, x.ProviderKey });
                    table.ForeignKey(
                        name: "FK_AspNetUserLogins_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "AspNetUserRoles",
                columns: table => new
                {
                    UserId = table.Column<string>(type: "text", nullable: false),
                    RoleId = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AspNetUserRoles", x => new { x.UserId, x.RoleId });
                    table.ForeignKey(
                        name: "FK_AspNetUserRoles_AspNetRoles_RoleId",
                        column: x => x.RoleId,
                        principalTable: "AspNetRoles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_AspNetUserRoles_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "AspNetUserTokens",
                columns: table => new
                {
                    UserId = table.Column<string>(type: "text", nullable: false),
                    LoginProvider = table.Column<string>(type: "text", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: false),
                    Value = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AspNetUserTokens", x => new { x.UserId, x.LoginProvider, x.Name });
                    table.ForeignKey(
                        name: "FK_AspNetUserTokens_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "product_definitions",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(250)", maxLength: 250, nullable: false),
                    Description = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    Sku = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Gtin = table.Column<string>(type: "character varying(14)", maxLength: 14, nullable: true),
                    Upc = table.Column<string>(type: "character varying(12)", maxLength: 12, nullable: true),
                    Ean = table.Column<string>(type: "character varying(13)", maxLength: 13, nullable: true),
                    LifecycleState = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false, defaultValue: "Active"),
                    CategoryId = table.Column<Guid>(type: "uuid", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    Version = table.Column<int>(type: "integer", nullable: false, defaultValue: 1),
                    AttributesJson = table.Column<string>(type: "jsonb", nullable: true),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_product_definitions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_product_definitions_categories_CategoryId",
                        column: x => x.CategoryId,
                        principalSchema: "public",
                        principalTable: "categories",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateTable(
                name: "products",
                schema: "public",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    ProductCode = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Description = table.Column<string>(type: "text", nullable: true),
                    UnitOfMeasure = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    CategoryId = table.Column<Guid>(type: "uuid", nullable: true),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_products", x => x.Id);
                    table.ForeignKey(
                        name: "FK_products_categories_CategoryId",
                        column: x => x.CategoryId,
                        principalSchema: "public",
                        principalTable: "categories",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateTable(
                name: "budget_allocations",
                schema: "public",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    BudgetId = table.Column<Guid>(type: "uuid", nullable: false),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: false),
                    DepartmentId = table.Column<Guid>(type: "uuid", nullable: false),
                    allocated_amount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    allocated_currency_code = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    consumed_amount = table.Column<decimal>(type: "numeric(18,2)", nullable: false, defaultValue: 0m),
                    consumed_currency_code = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    AllocationDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    FiscalPeriodIdentifier = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    Description = table.Column<string>(type: "text", nullable: true),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_budget_allocations", x => x.Id);
                    table.ForeignKey(
                        name: "FK_budget_allocations_budgets_BudgetId",
                        column: x => x.BudgetId,
                        principalSchema: "public",
                        principalTable: "budgets",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_budget_allocations_departments_DepartmentId",
                        column: x => x.DepartmentId,
                        principalSchema: "public",
                        principalTable: "departments",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "RolePermissions",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    RoleId = table.Column<Guid>(type: "uuid", nullable: false),
                    PermissionString = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RolePermissions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_RolePermissions_P2PRoles_RoleId",
                        column: x => x.RoleId,
                        principalTable: "P2PRoles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "procurement_workflow_steps",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    WorkflowId = table.Column<Guid>(type: "uuid", nullable: false),
                    ProcurementWorkflowId = table.Column<Guid>(type: "uuid", nullable: false),
                    StepName = table.Column<string>(type: "character varying(150)", maxLength: 150, nullable: false),
                    SequenceOrder = table.Column<int>(type: "integer", nullable: false),
                    StepOrder = table.Column<int>(type: "integer", nullable: false),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    AssigneeUserId = table.Column<string>(type: "character varying(450)", maxLength: 450, nullable: true),
                    ApproverRoleId = table.Column<Guid>(type: "uuid", nullable: true),
                    ApproverUserId = table.Column<string>(type: "character varying(450)", maxLength: 450, nullable: true),
                    AssigneeName = table.Column<string>(type: "character varying(150)", maxLength: 150, nullable: true),
                    AssignedDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ActionDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    Comments = table.Column<string>(type: "text", nullable: true),
                    ConditionExpression = table.Column<string>(type: "text", nullable: true),
                    SlaDuration = table.Column<TimeSpan>(type: "interval", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_procurement_workflow_steps", x => x.Id);
                    table.ForeignKey(
                        name: "FK_procurement_workflow_steps_AspNetUsers_ApproverUserId",
                        column: x => x.ApproverUserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_procurement_workflow_steps_procurement_workflows_Procuremen~",
                        column: x => x.ProcurementWorkflowId,
                        principalTable: "procurement_workflows",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_procurement_workflow_steps_procurement_workflows_WorkflowId",
                        column: x => x.WorkflowId,
                        principalTable: "procurement_workflows",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "CatalogProducts",
                schema: "catalog",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    SellerOrgId = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(250)", maxLength: 250, nullable: false),
                    Description = table.Column<string>(type: "character varying(4000)", maxLength: 4000, nullable: true),
                    CategoryId = table.Column<Guid>(type: "uuid", nullable: false),
                    ListPrice = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    ProductStatus = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    UnitOfMeasure = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    StockQuantity = table.Column<int>(type: "integer", nullable: true),
                    InventoryStatus = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    AvailableDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    StandardLeadTimeDays = table.Column<int>(type: "integer", nullable: true),
                    QuantityBasedPricingTiersJson = table.Column<string>(type: "text", nullable: true),
                    SubmittedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ApprovedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    ApprovedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    RejectionReason = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    Version = table.Column<int>(type: "integer", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CatalogProducts", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CatalogProducts_ProductCategories_CategoryId",
                        column: x => x.CategoryId,
                        principalSchema: "catalog",
                        principalTable: "ProductCategories",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "requests_for_information",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    RfiNumber = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Title = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    Description = table.Column<string>(type: "text", nullable: true),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: false),
                    IssuedDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IssueDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    ResponseDueDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ResponseDeadline = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    ProjectId = table.Column<Guid>(type: "uuid", nullable: true),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    IssuedByUserId = table.Column<string>(type: "character varying(450)", maxLength: 450, nullable: true),
                    IssuedByName = table.Column<string>(type: "character varying(150)", maxLength: 150, nullable: true),
                    TargetAudienceDescription = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    Notes = table.Column<string>(type: "text", nullable: true),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_requests_for_information", x => x.Id);
                    table.ForeignKey(
                        name: "FK_requests_for_information_projects_ProjectId",
                        column: x => x.ProjectId,
                        principalSchema: "public",
                        principalTable: "projects",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateTable(
                name: "SpeckleProjectLinks",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    P2PProjectId = table.Column<Guid>(type: "uuid", nullable: false),
                    SpeckleServerUrl = table.Column<string>(type: "character varying(2048)", maxLength: 2048, nullable: false),
                    SpeckleStreamId = table.Column<string>(type: "character varying(1024)", maxLength: 1024, nullable: false),
                    SpeckleCommitId = table.Column<string>(type: "character varying(1024)", maxLength: 1024, nullable: true),
                    SpeckleAuthToken = table.Column<string>(type: "character varying(2048)", maxLength: 2048, nullable: false),
                    LinkedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    LastRefreshedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SpeckleProjectLinks", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SpeckleProjectLinks_projects_P2PProjectId",
                        column: x => x.P2PProjectId,
                        principalSchema: "public",
                        principalTable: "projects",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "sales_territory_representatives",
                columns: table => new
                {
                    sales_territory_id = table.Column<Guid>(type: "uuid", nullable: false),
                    representative_id = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_sales_territory_representatives", x => new { x.sales_territory_id, x.representative_id });
                    table.ForeignKey(
                        name: "FK_sales_territory_representatives_AspNetUsers_representative_~",
                        column: x => x.representative_id,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_sales_territory_representatives_sales_territories_sales_ter~",
                        column: x => x.sales_territory_id,
                        principalTable: "sales_territories",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "contracts",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ContractNumber = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Title = table.Column<string>(type: "character varying(250)", maxLength: 250, nullable: false),
                    ContractType = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    VendorId = table.Column<Guid>(type: "uuid", nullable: false),
                    StartDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    EndDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    total_contract_value_amount = table.Column<decimal>(type: "numeric(18,4)", precision: 18, scale: 4, nullable: true),
                    total_contract_value_currency_code = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: true),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    PaymentTerms = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    RenewalTerms = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    IsAutoRenew = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    TerminationPenaltyTerms = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    TermsAndConditions = table.Column<string>(type: "text", nullable: true),
                    MilestonesJson = table.Column<string>(type: "jsonb", nullable: true),
                    SlaDetailsJson = table.Column<string>(type: "jsonb", nullable: true),
                    ComplianceDocumentLinksJson = table.Column<string>(type: "jsonb", nullable: true),
                    VendorPerformanceScoreSnapshot = table.Column<decimal>(type: "numeric(5,2)", precision: 5, scale: 2, nullable: true),
                    Version = table.Column<int>(type: "integer", nullable: false, defaultValue: 1),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: true),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_contracts", x => x.Id);
                    table.ForeignKey(
                        name: "FK_contracts_vendors_VendorId",
                        column: x => x.VendorId,
                        principalTable: "vendors",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "suppliers",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    SupplierName = table.Column<string>(type: "character varying(250)", maxLength: 250, nullable: false),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false, defaultValue: "Active"),
                    RiskRating = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    SustainabilityScore = table.Column<decimal>(type: "numeric(5,2)", precision: 5, scale: 2, nullable: true),
                    IsContractManufacturer = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    AverageLeadTimeDays = table.Column<int>(type: "integer", nullable: true),
                    IsCsrCompliant = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    CapacityUtilizationPercent = table.Column<decimal>(type: "numeric(5,2)", precision: 5, scale: 2, nullable: true),
                    VendorId = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    EmergencyContacts = table.Column<string>(type: "jsonb", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_suppliers", x => x.Id);
                    table.ForeignKey(
                        name: "FK_suppliers_vendors_VendorId",
                        column: x => x.VendorId,
                        principalTable: "vendors",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "vendor_products",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ProductDefinitionId = table.Column<Guid>(type: "uuid", nullable: false),
                    VendorId = table.Column<Guid>(type: "uuid", nullable: false),
                    VendorSku = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    unit_price_amount = table.Column<decimal>(type: "numeric(18,4)", precision: 18, scale: 4, nullable: false),
                    unit_price_currency_code = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    UnitOfMeasure = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    PackSize = table.Column<decimal>(type: "numeric(18,4)", precision: 18, scale: 4, nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    LeadTimeDays = table.Column<int>(type: "integer", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_vendor_products", x => x.Id);
                    table.ForeignKey(
                        name: "FK_vendor_products_product_definitions_ProductDefinitionId",
                        column: x => x.ProductDefinitionId,
                        principalTable: "product_definitions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_vendor_products_vendors_VendorId",
                        column: x => x.VendorId,
                        principalTable: "vendors",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "requests_for_proposal",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    RfpNumber = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Title = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    ScopeOfWork = table.Column<string>(type: "text", nullable: true),
                    EvaluationCriteria = table.Column<string>(type: "jsonb", nullable: true),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: false),
                    IssuedDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IssueDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    DecisionDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    ProjectId = table.Column<Guid>(type: "uuid", nullable: true),
                    QuestionDeadline = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    SubmissionDeadline = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    IssuedByUserId = table.Column<string>(type: "character varying(450)", maxLength: 450, nullable: true),
                    IssuedByName = table.Column<string>(type: "character varying(150)", maxLength: 150, nullable: true),
                    Department = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    ExpectedContractStartDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ExpectedContractDuration = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    AwardedVendorId = table.Column<Guid>(type: "uuid", nullable: true),
                    AwardedContractId = table.Column<Guid>(type: "uuid", nullable: true),
                    CompletedDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_requests_for_proposal", x => x.Id);
                    table.ForeignKey(
                        name: "FK_requests_for_proposal_contracts_AwardedContractId",
                        column: x => x.AwardedContractId,
                        principalTable: "contracts",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_requests_for_proposal_projects_ProjectId",
                        column: x => x.ProjectId,
                        principalSchema: "public",
                        principalTable: "projects",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_requests_for_proposal_vendors_AwardedVendorId",
                        column: x => x.AwardedVendorId,
                        principalTable: "vendors",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "vendor_proposals",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: false),
                    VendorId = table.Column<Guid>(type: "uuid", nullable: false),
                    RequestForProposalId = table.Column<Guid>(type: "uuid", nullable: false),
                    SubmissionDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    TotalProposedValue = table.Column<decimal>(type: "numeric(18,2)", nullable: true),
                    ValidityPeriodDays = table.Column<int>(type: "integer", nullable: false),
                    ValidityEndDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    AlternatePaymentTerms = table.Column<string>(type: "text", nullable: true),
                    ValueAddedServices = table.Column<string>(type: "text", nullable: true),
                    SustainabilityCommitments = table.Column<string>(type: "text", nullable: true),
                    RiskSharingClauses = table.Column<string>(type: "text", nullable: true),
                    PerformanceGuarantees = table.Column<string>(type: "text", nullable: true),
                    SubcontractorDisclosures = table.Column<string>(type: "text", nullable: true),
                    ComplianceCertificationsJson = table.Column<string>(type: "text", nullable: true),
                    Comments = table.Column<string>(type: "text", nullable: true),
                    Version = table.Column<int>(type: "integer", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_vendor_proposals", x => x.Id);
                    table.ForeignKey(
                        name: "FK_vendor_proposals_requests_for_proposal_RequestForProposalId",
                        column: x => x.RequestForProposalId,
                        principalTable: "requests_for_proposal",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_vendor_proposals_vendors_VendorId",
                        column: x => x.VendorId,
                        principalTable: "vendors",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "delivery_note_lines",
                columns: table => new
                {
                    DeliveryNoteId = table.Column<Guid>(type: "uuid", nullable: false),
                    LineNumber = table.Column<int>(type: "integer", nullable: false),
                    PurchaseOrderLineId = table.Column<Guid>(type: "uuid", nullable: true),
                    ProductId = table.Column<Guid>(type: "uuid", nullable: false),
                    SalesOrderId = table.Column<Guid>(type: "uuid", nullable: true),
                    SalesOrderLineNumber = table.Column<int>(type: "integer", nullable: true),
                    ProductSkuSnapshot = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    QuantityShipped = table.Column<decimal>(type: "numeric(18,4)", nullable: false),
                    UnitOfMeasure = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    BatchNumber = table.Column<string>(type: "text", maxLength: 100, nullable: true),
                    SerialNumber = table.Column<string>(type: "text", maxLength: 100, nullable: true),
                    Notes = table.Column<string>(type: "text", nullable: true),
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_delivery_note_lines", x => new { x.DeliveryNoteId, x.LineNumber });
                    table.ForeignKey(
                        name: "FK_delivery_note_lines_product_definitions_ProductId",
                        column: x => x.ProductId,
                        principalTable: "product_definitions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "delivery_notes",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: false),
                    DeliveryNoteNumber = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    PurchaseOrderId = table.Column<Guid>(type: "uuid", nullable: true),
                    VendorId = table.Column<Guid>(type: "uuid", nullable: false),
                    SalesOrderId = table.Column<Guid>(type: "uuid", nullable: true),
                    ShipmentDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    DeliveryDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    ReceivedBy = table.Column<string>(type: "character varying(150)", maxLength: 150, nullable: true),
                    carrier_name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    tracking_number = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    shipping_street = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    shipping_city = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    shipping_state = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    shipping_country = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    shipping_postal_code = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    billing_street = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    billing_city = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    billing_state = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    billing_country = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    billing_postal_code = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    Notes = table.Column<string>(type: "text", nullable: true),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_delivery_notes", x => x.Id);
                    table.ForeignKey(
                        name: "FK_delivery_notes_vendors_VendorId",
                        column: x => x.VendorId,
                        principalTable: "vendors",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "goods_receipt_note_lines",
                columns: table => new
                {
                    LineNumber = table.Column<int>(type: "integer", nullable: false),
                    GoodsReceiptNoteId = table.Column<Guid>(type: "uuid", nullable: false),
                    PurchaseOrderLineId = table.Column<Guid>(type: "uuid", nullable: false),
                    DeliveryNoteLineId = table.Column<Guid>(type: "uuid", nullable: true),
                    DeliveryNoteLineDeliveryNoteId = table.Column<Guid>(type: "uuid", nullable: true),
                    DeliveryNoteLineLineNumber = table.Column<int>(type: "integer", nullable: true),
                    ProductId = table.Column<Guid>(type: "uuid", nullable: false),
                    ProductSkuSnapshot = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    ProductDescriptionSnapshot = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    QuantityReceived = table.Column<decimal>(type: "numeric(18,4)", nullable: false, defaultValue: 0m),
                    UnitOfMeasure = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    QualityControlStatus = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    PutAwayLocation = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    BatchNumber = table.Column<string>(type: "text", nullable: true),
                    LotNumber = table.Column<string>(type: "text", nullable: true),
                    ExpiryDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    InspectionCompleted = table.Column<bool>(type: "boolean", nullable: false),
                    QuantityAccepted = table.Column<decimal>(type: "numeric(18,4)", nullable: false, defaultValue: 0m),
                    QuantityRejected = table.Column<decimal>(type: "numeric(18,4)", nullable: false, defaultValue: 0m),
                    RejectionReason = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    Notes = table.Column<string>(type: "text", nullable: true),
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_goods_receipt_note_lines", x => new { x.GoodsReceiptNoteId, x.LineNumber });
                    table.ForeignKey(
                        name: "FK_goods_receipt_note_lines_delivery_note_lines_DeliveryNoteLi~",
                        columns: x => new { x.DeliveryNoteLineDeliveryNoteId, x.DeliveryNoteLineLineNumber },
                        principalTable: "delivery_note_lines",
                        principalColumns: new[] { "DeliveryNoteId", "LineNumber" });
                    table.ForeignKey(
                        name: "FK_goods_receipt_note_lines_product_definitions_ProductId",
                        column: x => x.ProductId,
                        principalTable: "product_definitions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "goods_receipt_notes",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: false),
                    GoodsReceiptNoteNumber = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    GrnNumber = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    PurchaseOrderId = table.Column<Guid>(type: "uuid", nullable: false),
                    DeliveryNoteId = table.Column<Guid>(type: "uuid", nullable: true),
                    VendorId = table.Column<Guid>(type: "uuid", nullable: false),
                    ReceiptDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    InspectionDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    ReceivingLocation = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    ReceivedByUserId = table.Column<string>(type: "character varying(450)", maxLength: 450, nullable: false),
                    ReceivedByName = table.Column<string>(type: "character varying(150)", maxLength: 150, nullable: false),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Notes = table.Column<string>(type: "text", nullable: true),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_goods_receipt_notes", x => x.Id);
                    table.ForeignKey(
                        name: "FK_goods_receipt_notes_AspNetUsers_ReceivedByUserId",
                        column: x => x.ReceivedByUserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_goods_receipt_notes_delivery_notes_DeliveryNoteId",
                        column: x => x.DeliveryNoteId,
                        principalTable: "delivery_notes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_goods_receipt_notes_vendors_VendorId",
                        column: x => x.VendorId,
                        principalTable: "vendors",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "invoice_lines",
                columns: table => new
                {
                    LineNumber = table.Column<int>(type: "integer", nullable: false),
                    InvoiceId = table.Column<Guid>(type: "uuid", nullable: false),
                    PurchaseOrderLineId = table.Column<Guid>(type: "uuid", nullable: true),
                    ProductId = table.Column<Guid>(type: "uuid", nullable: true),
                    Description = table.Column<string>(type: "text", maxLength: 1000, nullable: false),
                    Quantity = table.Column<decimal>(type: "numeric(18,4)", nullable: false),
                    UnitPrice = table.Column<decimal>(type: "numeric(18,4)", nullable: false),
                    LineTotal = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    TaxAmount = table.Column<decimal>(type: "numeric(18,2)", nullable: true, defaultValue: 0m),
                    TaxRate = table.Column<decimal>(type: "numeric(5,4)", nullable: true),
                    UnitOfMeasure = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    Notes = table.Column<string>(type: "text", nullable: true),
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_invoice_lines", x => new { x.InvoiceId, x.LineNumber });
                    table.ForeignKey(
                        name: "FK_invoice_lines_product_definitions_ProductId",
                        column: x => x.ProductId,
                        principalTable: "product_definitions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "invoices",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: false),
                    InvoiceNumber = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    VendorId = table.Column<Guid>(type: "uuid", nullable: false),
                    CustomerId = table.Column<Guid>(type: "uuid", nullable: true),
                    PurchaseOrderId = table.Column<Guid>(type: "uuid", nullable: true),
                    InvoiceDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    DueDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    CurrencyCode = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    SubTotal = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    Subtotal = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    TaxAmount = table.Column<decimal>(type: "numeric(18,2)", nullable: false, defaultValue: 0m),
                    TotalAmount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    AmountPaid = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    PaymentDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    Notes = table.Column<string>(type: "text", nullable: true),
                    PaymentTerms = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_invoices", x => x.Id);
                    table.ForeignKey(
                        name: "FK_invoices_customers_CustomerId",
                        column: x => x.CustomerId,
                        principalSchema: "public",
                        principalTable: "customers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_invoices_vendors_VendorId",
                        column: x => x.VendorId,
                        principalTable: "vendors",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "payment_transactions",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: false),
                    transaction_reference = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    PaymentDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    Amount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    CurrencyCode = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    PaymentMethod = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    VendorId = table.Column<Guid>(type: "uuid", nullable: false),
                    InvoiceId = table.Column<Guid>(type: "uuid", nullable: true),
                    Notes = table.Column<string>(type: "text", nullable: true),
                    BankReference = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_payment_transactions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_payment_transactions_invoices_InvoiceId",
                        column: x => x.InvoiceId,
                        principalTable: "invoices",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_payment_transactions_vendors_VendorId",
                        column: x => x.VendorId,
                        principalTable: "vendors",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "purchase_order_lines",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    PurchaseOrderId = table.Column<Guid>(type: "uuid", nullable: false),
                    VendorProductId = table.Column<Guid>(type: "uuid", nullable: false),
                    SkuSnapshot = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    DescriptionSnapshot = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: false),
                    unit_price_amount = table.Column<decimal>(type: "numeric(18,4)", precision: 18, scale: 4, nullable: false),
                    unit_price_currency_code = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    UnitOfMeasureSnapshot = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Quantity = table.Column<decimal>(type: "numeric(18,4)", precision: 18, scale: 4, nullable: false),
                    line_total_amount = table.Column<decimal>(type: "numeric(18,4)", precision: 18, scale: 4, nullable: false),
                    line_total_currency_code = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    Notes = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_purchase_order_lines", x => x.Id);
                    table.ForeignKey(
                        name: "FK_purchase_order_lines_vendor_products_VendorProductId",
                        column: x => x.VendorProductId,
                        principalTable: "vendor_products",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "technical_submittals",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: false),
                    SubmittalNumber = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Revision = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    Title = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    Description = table.Column<string>(type: "text", nullable: true),
                    SubmittalType = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    CurrentReviewCycle = table.Column<int>(type: "integer", nullable: false),
                    ReviewDueDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    RequiredDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    ReviewStartDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ReviewCompletionDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CurrentOverallDisposition = table.Column<int>(type: "integer", nullable: false),
                    PurchaseOrderLineId = table.Column<Guid>(type: "uuid", nullable: true),
                    ContractId = table.Column<Guid>(type: "uuid", nullable: true),
                    ProjectId = table.Column<Guid>(type: "uuid", nullable: false),
                    SpecificationSection = table.Column<string>(type: "text", nullable: true),
                    SpecificationId = table.Column<Guid>(type: "uuid", nullable: true),
                    RelatedITPReference = table.Column<string>(type: "text", nullable: true),
                    TestPlanId = table.Column<Guid>(type: "uuid", nullable: true),
                    RelatedNCRReference = table.Column<string>(type: "text", nullable: true),
                    NonConformanceReportId = table.Column<Guid>(type: "uuid", nullable: true),
                    VendorId = table.Column<Guid>(type: "uuid", nullable: true),
                    SubmittedById = table.Column<string>(type: "text", nullable: false),
                    SubmittedByUserId = table.Column<string>(type: "character varying(450)", maxLength: 450, nullable: false),
                    CurrentReviewerId = table.Column<Guid>(type: "uuid", nullable: true),
                    CycleCount = table.Column<int>(type: "integer", nullable: false, defaultValue: 1),
                    IsAsBuilt = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    IsFinalDocumentation = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    SubmittedDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    FinalSignOffById = table.Column<string>(type: "text", nullable: true),
                    SignedOffById = table.Column<Guid>(type: "uuid", nullable: true),
                    FinalSignOffDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    SignedOffDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    ResubmissionCount = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    MaxResubmissions = table.Column<int>(type: "integer", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    SubmittedDocuments = table.Column<List<DocumentLink>>(type: "jsonb", nullable: false),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_technical_submittals", x => x.Id);
                    table.ForeignKey(
                        name: "FK_technical_submittals_AspNetUsers_SubmittedByUserId",
                        column: x => x.SubmittedByUserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_technical_submittals_contracts_ContractId",
                        column: x => x.ContractId,
                        principalTable: "contracts",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_technical_submittals_projects_ProjectId",
                        column: x => x.ProjectId,
                        principalSchema: "public",
                        principalTable: "projects",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_technical_submittals_purchase_order_lines_PurchaseOrderLine~",
                        column: x => x.PurchaseOrderLineId,
                        principalTable: "purchase_order_lines",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_technical_submittals_vendors_VendorId",
                        column: x => x.VendorId,
                        principalTable: "vendors",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "submittal_reviews",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TechnicalSubmittalId = table.Column<Guid>(type: "uuid", nullable: false),
                    ReviewCycle = table.Column<int>(type: "integer", nullable: false),
                    ReviewerId = table.Column<string>(type: "text", nullable: false),
                    ReviewerGuid = table.Column<Guid>(type: "uuid", nullable: false),
                    ReviewerName = table.Column<string>(type: "text", nullable: false),
                    ReviewDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    Disposition = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Comments = table.Column<string>(type: "text", nullable: true),
                    MarkupDocument = table.Column<DocumentLink>(type: "jsonb", nullable: true),
                    TechnicalSubmittalId1 = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_submittal_reviews", x => x.Id);
                    table.ForeignKey(
                        name: "FK_submittal_reviews_AspNetUsers_ReviewerId",
                        column: x => x.ReviewerId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_submittal_reviews_technical_submittals_TechnicalSubmittalId",
                        column: x => x.TechnicalSubmittalId,
                        principalTable: "technical_submittals",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_submittal_reviews_technical_submittals_TechnicalSubmittalId1",
                        column: x => x.TechnicalSubmittalId1,
                        principalTable: "technical_submittals",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "purchase_orders",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    OrderNumber = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    OrderDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    DeliveryDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    PaymentTerms = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    CurrencyCode = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    total_amount = table.Column<decimal>(type: "numeric(18,4)", precision: 18, scale: 4, nullable: false),
                    VendorId = table.Column<Guid>(type: "uuid", nullable: false),
                    ContractId = table.Column<Guid>(type: "uuid", nullable: true),
                    RequisitionId = table.Column<Guid>(type: "uuid", nullable: true),
                    shipment_street = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    shipment_city = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    shipment_state = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    shipment_country = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    shipment_postal_code = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_purchase_orders", x => x.Id);
                    table.ForeignKey(
                        name: "FK_purchase_orders_contracts_ContractId",
                        column: x => x.ContractId,
                        principalTable: "contracts",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_purchase_orders_vendors_VendorId",
                        column: x => x.VendorId,
                        principalTable: "vendors",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "purchase_requisitions",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: false),
                    RequisitionNumber = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    RequestorName = table.Column<string>(type: "character varying(150)", maxLength: 150, nullable: false),
                    RequestorEmail = table.Column<string>(type: "character varying(254)", maxLength: 254, nullable: false),
                    RequestorUserId = table.Column<string>(type: "character varying(450)", maxLength: 450, nullable: true),
                    Department = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    RequestDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    DateNeeded = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    Justification = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: false),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    CurrencyCode = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    total_estimated_cost_amount = table.Column<decimal>(type: "numeric(18,4)", precision: 18, scale: 4, nullable: false),
                    shipping_street = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    shipping_city = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    shipping_state = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    shipping_country = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    shipping_postal_code = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    Notes = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    AssociatedPurchaseOrderId = table.Column<Guid>(type: "uuid", nullable: true),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_purchase_requisitions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_purchase_requisitions_purchase_orders_AssociatedPurchaseOrd~",
                        column: x => x.AssociatedPurchaseOrderId,
                        principalTable: "purchase_orders",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateTable(
                name: "purchase_requisition_lines",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    PurchaseRequisitionId = table.Column<Guid>(type: "uuid", nullable: false),
                    LineNumber = table.Column<int>(type: "integer", nullable: false),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: false),
                    VendorProductId = table.Column<Guid>(type: "uuid", nullable: true),
                    ProductDefinitionId = table.Column<Guid>(type: "uuid", nullable: true),
                    Description = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: false),
                    Quantity = table.Column<decimal>(type: "numeric(18,4)", precision: 18, scale: 4, nullable: false),
                    UnitOfMeasure = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    estimated_unit_price_amount = table.Column<decimal>(type: "numeric(18,4)", precision: 18, scale: 4, nullable: true),
                    estimated_unit_price_currency_code = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: true),
                    estimated_line_cost_amount = table.Column<decimal>(type: "numeric(18,4)", precision: 18, scale: 4, nullable: true),
                    estimated_line_cost_currency_code = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: true),
                    GLAccountCode = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    DateNeeded = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    SuggestedVendorId = table.Column<Guid>(type: "uuid", nullable: true),
                    Notes = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_purchase_requisition_lines", x => x.Id);
                    table.ForeignKey(
                        name: "FK_purchase_requisition_lines_product_definitions_ProductDefin~",
                        column: x => x.ProductDefinitionId,
                        principalTable: "product_definitions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_purchase_requisition_lines_purchase_requisitions_PurchaseRe~",
                        column: x => x.PurchaseRequisitionId,
                        principalTable: "purchase_requisitions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_purchase_requisition_lines_vendor_products_VendorProductId",
                        column: x => x.VendorProductId,
                        principalTable: "vendor_products",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_purchase_requisition_lines_vendors_SuggestedVendorId",
                        column: x => x.SuggestedVendorId,
                        principalTable: "vendors",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateTable(
                name: "request_for_quotes",
                schema: "public",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: false),
                    RFQNumber = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Title = table.Column<string>(type: "character varying(250)", maxLength: 250, nullable: false),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    SubmissionDeadline = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    RequiredDeliveryDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    Description = table.Column<string>(type: "text", nullable: true),
                    ScopeOfWork = table.Column<string>(type: "text", nullable: true),
                    TermsAndConditions = table.Column<string>(type: "text", nullable: true),
                    VendorReferenceInstructions = table.Column<string>(type: "text", nullable: true),
                    CurrencyCode = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    deliver_to_street = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    deliver_to_city = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    deliver_to_state = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    deliver_to_country = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    deliver_to_postal_code = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    CreatedByUserId = table.Column<string>(type: "character varying(450)", maxLength: 450, nullable: false),
                    OriginatingRequisitionId = table.Column<Guid>(type: "uuid", nullable: true),
                    AwardedVendorId = table.Column<Guid>(type: "uuid", nullable: true),
                    RelatedAgreementId = table.Column<Guid>(type: "uuid", nullable: true),
                    ContactPersonEmail = table.Column<string>(type: "character varying(254)", maxLength: 254, nullable: true),
                    CommunicationMethod = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_request_for_quotes", x => x.Id);
                    table.ForeignKey(
                        name: "FK_request_for_quotes_contracts_RelatedAgreementId",
                        column: x => x.RelatedAgreementId,
                        principalTable: "contracts",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_request_for_quotes_purchase_requisitions_OriginatingRequisi~",
                        column: x => x.OriginatingRequisitionId,
                        principalTable: "purchase_requisitions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_request_for_quotes_vendors_AwardedVendorId",
                        column: x => x.AwardedVendorId,
                        principalTable: "vendors",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "request_for_quote_lines",
                schema: "public",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    RequestForQuoteId = table.Column<Guid>(type: "uuid", nullable: false),
                    LineNumber = table.Column<int>(type: "integer", nullable: false),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: false),
                    ProductDefinitionId = table.Column<Guid>(type: "uuid", nullable: true),
                    VendorProductId = table.Column<Guid>(type: "uuid", nullable: true),
                    Description = table.Column<string>(type: "text", nullable: false),
                    Quantity = table.Column<decimal>(type: "numeric(18,4)", precision: 18, scale: 4, nullable: false),
                    UnitOfMeasure = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    target_unit_price_amount = table.Column<decimal>(type: "numeric(18,4)", precision: 18, scale: 4, nullable: true),
                    target_unit_price_currency_code = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: true),
                    AlternateItemProposal = table.Column<string>(type: "text", nullable: true),
                    est_tco_value_amount = table.Column<decimal>(type: "numeric(18,4)", precision: 18, scale: 4, nullable: true),
                    est_tco_value_currency_code = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: true),
                    TechnicalSpecifications = table.Column<string>(type: "text", nullable: true),
                    SampleRequired = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    MinimumOrderQuantity = table.Column<decimal>(type: "numeric(18,4)", precision: 18, scale: 4, nullable: true),
                    PreferredIncoterm = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: true),
                    IsSubstituteAllowed = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    Notes = table.Column<string>(type: "text", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_request_for_quote_lines", x => x.Id);
                    table.ForeignKey(
                        name: "FK_request_for_quote_lines_product_definitions_ProductDefiniti~",
                        column: x => x.ProductDefinitionId,
                        principalTable: "product_definitions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_request_for_quote_lines_request_for_quotes_RequestForQuoteId",
                        column: x => x.RequestForQuoteId,
                        principalSchema: "public",
                        principalTable: "request_for_quotes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_request_for_quote_lines_vendor_products_VendorProductId",
                        column: x => x.VendorProductId,
                        principalTable: "vendor_products",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateTable(
                name: "return_authorization_lines",
                columns: table => new
                {
                    LineNumber = table.Column<int>(type: "integer", nullable: false),
                    ReturnAuthorizationId = table.Column<Guid>(type: "uuid", nullable: false),
                    OriginalSalesOrderLineId = table.Column<Guid>(type: "uuid", nullable: true),
                    SalesOrderId = table.Column<Guid>(type: "uuid", nullable: true),
                    SalesOrderLineNumber = table.Column<int>(type: "integer", nullable: true),
                    InvoiceId = table.Column<Guid>(type: "uuid", nullable: true),
                    InvoiceLineNumber = table.Column<int>(type: "integer", nullable: true),
                    ItemCondition = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    ProductDefinitionId = table.Column<Guid>(type: "uuid", nullable: true),
                    VendorProductId = table.Column<Guid>(type: "uuid", nullable: true),
                    ProductId = table.Column<Guid>(type: "uuid", nullable: false),
                    SkuSnapshot = table.Column<string>(type: "text", nullable: false),
                    DescriptionSnapshot = table.Column<string>(type: "text", nullable: false),
                    QuantityAuthorized = table.Column<decimal>(type: "numeric(18,4)", nullable: false),
                    UnitOfMeasure = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    QuantityReceived = table.Column<decimal>(type: "numeric", nullable: false),
                    ReasonForReturn = table.Column<string>(type: "text", nullable: false),
                    RequestedAction = table.Column<int>(type: "integer", nullable: false),
                    OriginalSalesOrderLineSalesOrderId = table.Column<Guid>(type: "uuid", nullable: true),
                    OriginalSalesOrderLineLineNumber = table.Column<int>(type: "integer", nullable: true),
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_return_authorization_lines", x => new { x.ReturnAuthorizationId, x.LineNumber });
                    table.ForeignKey(
                        name: "FK_return_authorization_lines_invoice_lines_InvoiceId_InvoiceL~",
                        columns: x => new { x.InvoiceId, x.InvoiceLineNumber },
                        principalTable: "invoice_lines",
                        principalColumns: new[] { "InvoiceId", "LineNumber" },
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_return_authorization_lines_product_definitions_ProductDefin~",
                        column: x => x.ProductDefinitionId,
                        principalTable: "product_definitions",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_return_authorization_lines_products_ProductId",
                        column: x => x.ProductId,
                        principalSchema: "public",
                        principalTable: "products",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_return_authorization_lines_vendor_products_VendorProductId",
                        column: x => x.VendorProductId,
                        principalTable: "vendor_products",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "return_authorizations",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: false),
                    RmaNumber = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    RequestDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    CustomerId = table.Column<Guid>(type: "uuid", nullable: false),
                    OriginalSalesOrderId = table.Column<Guid>(type: "uuid", nullable: false),
                    SalesOrderId = table.Column<Guid>(type: "uuid", nullable: true),
                    InvoiceId = table.Column<Guid>(type: "uuid", nullable: true),
                    AuthorizationDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    ExpiryDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    ReasonForReturn = table.Column<string>(type: "text", nullable: true),
                    RequestedAction = table.Column<int>(type: "integer", nullable: false),
                    ShippingInstructions = table.Column<string>(type: "text", nullable: true),
                    Notes = table.Column<string>(type: "text", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_return_authorizations", x => x.Id);
                    table.ForeignKey(
                        name: "FK_return_authorizations_customers_CustomerId",
                        column: x => x.CustomerId,
                        principalSchema: "public",
                        principalTable: "customers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_return_authorizations_invoices_InvoiceId",
                        column: x => x.InvoiceId,
                        principalTable: "invoices",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "sales_orders",
                schema: "public",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: false),
                    OrderNumber = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    OrderDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    CustomerId = table.Column<Guid>(type: "uuid", nullable: true),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    billing_street = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    billing_city = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    billing_state = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    billing_country = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    billing_postal_code = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    shipping_street = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    shipping_city = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    shipping_state = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    shipping_country = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    shipping_postal_code = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    total_amount = table.Column<decimal>(type: "numeric(18,4)", precision: 18, scale: 4, nullable: false),
                    CurrencyCode = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    IsCreditApproved = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    AtpCheckDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    IsAtpConfirmed = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    IsDropShipment = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    SalespersonId = table.Column<string>(type: "character varying(450)", maxLength: 450, nullable: true),
                    CommissionRate = table.Column<decimal>(type: "numeric(5,4)", precision: 5, scale: 4, nullable: true),
                    RelatedReturnAuthorizationId = table.Column<Guid>(type: "uuid", nullable: true),
                    SalesTerritoryId = table.Column<Guid>(type: "uuid", nullable: true),
                    EdiTransactionReference = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    SalesTerritoryId1 = table.Column<Guid>(type: "uuid", nullable: true),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_sales_orders", x => x.Id);
                    table.ForeignKey(
                        name: "FK_sales_orders_customers_CustomerId",
                        column: x => x.CustomerId,
                        principalSchema: "public",
                        principalTable: "customers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_sales_orders_return_authorizations_RelatedReturnAuthorizati~",
                        column: x => x.RelatedReturnAuthorizationId,
                        principalTable: "return_authorizations",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_sales_orders_sales_territories_SalesTerritoryId",
                        column: x => x.SalesTerritoryId,
                        principalTable: "sales_territories",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_sales_orders_sales_territories_SalesTerritoryId1",
                        column: x => x.SalesTerritoryId1,
                        principalTable: "sales_territories",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "sales_order_lines",
                columns: table => new
                {
                    SalesOrderId = table.Column<Guid>(type: "uuid", nullable: false),
                    LineNumber = table.Column<int>(type: "integer", nullable: false),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: false),
                    VendorProductId = table.Column<Guid>(type: "uuid", nullable: false),
                    SkuSnapshot = table.Column<string>(type: "text", nullable: false),
                    DescriptionSnapshot = table.Column<string>(type: "text", nullable: false),
                    Description = table.Column<string>(type: "text", nullable: false),
                    RequestedDeliveryDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    ProductId = table.Column<Guid>(type: "uuid", nullable: false),
                    Quantity = table.Column<decimal>(type: "numeric(18,4)", nullable: false),
                    UnitOfMeasure = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    UnitPriceAmount = table.Column<decimal>(type: "numeric(18,4)", nullable: false),
                    UnitPriceCurrency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    AppliedDiscountDescription = table.Column<string>(type: "text", nullable: true),
                    DiscountAmountValue = table.Column<decimal>(type: "numeric(18,2)", nullable: true),
                    DiscountAmountCurrency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: true),
                    LineTotalAmount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    LineTotalCurrency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    QuantityBackordered = table.Column<decimal>(type: "numeric", nullable: false),
                    ReservedSerialNumbersJson = table.Column<string>(type: "text", nullable: true),
                    WarrantyEndDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsKitComponent = table.Column<bool>(type: "boolean", nullable: false),
                    ParentSalesOrderLineId = table.Column<Guid>(type: "uuid", nullable: true),
                    ProjectId = table.Column<Guid>(type: "uuid", nullable: true),
                    CostCode = table.Column<string>(type: "text", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    ParentSalesOrderLineSalesOrderId = table.Column<Guid>(type: "uuid", nullable: true),
                    ParentSalesOrderLineLineNumber = table.Column<int>(type: "integer", nullable: true),
                    ProjectId1 = table.Column<Guid>(type: "uuid", nullable: true),
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_sales_order_lines", x => new { x.SalesOrderId, x.LineNumber });
                    table.ForeignKey(
                        name: "FK_sales_order_lines_products_ProductId",
                        column: x => x.ProductId,
                        principalSchema: "public",
                        principalTable: "products",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_sales_order_lines_projects_ProjectId",
                        column: x => x.ProjectId,
                        principalSchema: "public",
                        principalTable: "projects",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_sales_order_lines_projects_ProjectId1",
                        column: x => x.ProjectId1,
                        principalSchema: "public",
                        principalTable: "projects",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_sales_order_lines_sales_order_lines_ParentSalesOrderLineSal~",
                        columns: x => new { x.ParentSalesOrderLineSalesOrderId, x.ParentSalesOrderLineLineNumber },
                        principalTable: "sales_order_lines",
                        principalColumns: new[] { "SalesOrderId", "LineNumber" });
                    table.ForeignKey(
                        name: "FK_sales_order_lines_sales_orders_SalesOrderId",
                        column: x => x.SalesOrderId,
                        principalSchema: "public",
                        principalTable: "sales_orders",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_sales_order_lines_vendor_products_VendorProductId",
                        column: x => x.VendorProductId,
                        principalTable: "vendor_products",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.InsertData(
                schema: "public",
                table: "categories",
                columns: new[] { "Id", "Code", "CreatedAt", "Description", "ModifiedAt", "Name", "ParentCategoryId", "TenantId", "UnspscCode" },
                values: new object[,]
                {
                    { new Guid("*************-2222-2222-************"), "OFF-SUP", new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "General office supplies and stationery", null, "Office Supplies", null, new Guid("********-1111-1111-1111-********1111"), "14111500" },
                    { new Guid("*************-2222-2222-************"), "IT-EQP", new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "Computer hardware and accessories", null, "IT Equipment", null, new Guid("********-1111-1111-1111-********1111"), "43210000" },
                    { new Guid("*************-2222-2222-************"), "FURN", new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "Office furniture and fixtures", null, "Furniture", null, new Guid("********-1111-1111-1111-********1111"), "56100000" },
                    { new Guid("*************-2222-2222-************"), "RAW-MAT", new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "Materials used in manufacturing", null, "Raw Materials", null, new Guid("********-1111-1111-1111-********1111"), "11000000" },
                    { new Guid("*************-2222-2222-************"), "SERV", new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "Professional and consulting services", null, "Services", null, new Guid("********-1111-1111-1111-********1111"), "80000000" }
                });

            migrationBuilder.InsertData(
                schema: "public",
                table: "departments",
                columns: new[] { "Id", "Code", "CreatedAt", "Description", "ModifiedAt", "Name", "TenantId" },
                values: new object[,]
                {
                    { new Guid("********-4444-4444-4444-********4401"), "IT", new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "Handles IT infrastructure, software development, and technical support.", null, "Information Technology", new Guid("********-1111-1111-1111-********1111") },
                    { new Guid("********-4444-4444-4444-********4402"), "FIN", new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "Manages financial operations, budgeting, and accounting.", null, "Finance", new Guid("********-1111-1111-1111-********1111") },
                    { new Guid("********-4444-4444-4444-********4403"), "HR", new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "Handles employee recruitment, training, and personnel management.", null, "Human Resources", new Guid("********-1111-1111-1111-********1111") },
                    { new Guid("********-4444-4444-4444-********4404"), "PROC", new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "Manages purchasing, vendor relationships, and supply chain operations.", null, "Procurement", new Guid("********-1111-1111-1111-********1111") },
                    { new Guid("********-4444-4444-4444-********4405"), "OPS", new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "Oversees day-to-day operational activities and logistics.", null, "Operations", new Guid("********-1111-1111-1111-********1111") },
                    { new Guid("********-4444-4444-4444-********4406"), "MKT", new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "Handles advertising, brand management, and market research.", null, "Marketing", new Guid("********-1111-1111-1111-********1111") }
                });

            migrationBuilder.InsertData(
                schema: "public",
                table: "tenants",
                columns: new[] { "Id", "AddressLine1", "City", "ContactEmail", "Country", "CreatedAt", "Identifier", "IsActive", "ModifiedAt", "Name", "PostalCode", "Settings", "SubscriptionPlan" },
                values: new object[] { new Guid("********-1111-1111-1111-********1111"), null, null, "<EMAIL>", null, new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "system", true, null, "Default System Tenant", null, null, "Standard" });

            migrationBuilder.InsertData(
                schema: "public",
                table: "categories",
                columns: new[] { "Id", "Code", "CreatedAt", "Description", "ModifiedAt", "Name", "ParentCategoryId", "TenantId", "UnspscCode" },
                values: new object[,]
                {
                    { new Guid("*************-2222-2222-************"), "PAPER", new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "Paper, notebooks, and notepads", null, "Paper Products", new Guid("*************-2222-2222-************"), new Guid("********-1111-1111-1111-********1111"), "14111500" },
                    { new Guid("*************-2222-2222-************"), "WRITE", new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "Pens, pencils, and markers", null, "Writing Instruments", new Guid("*************-2222-2222-************"), new Guid("********-1111-1111-1111-********1111"), "44121700" },
                    { new Guid("*************-2222-2222-************"), "COMP", new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "Desktops, laptops, and tablets", null, "Computers", new Guid("*************-2222-2222-************"), new Guid("********-1111-1111-1111-********1111"), "43211500" },
                    { new Guid("*************-2222-2222-************"), "PERIPH", new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "Monitors, keyboards, and mice", null, "Peripherals", new Guid("*************-2222-2222-************"), new Guid("********-1111-1111-1111-********1111"), "43211900" },
                    { new Guid("*************-2222-2222-************"), "CONSULT", new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "Business and management consulting", null, "Consulting", new Guid("*************-2222-2222-************"), new Guid("********-1111-1111-1111-********1111"), "80101500" },
                    { new Guid("*************-2222-2222-************"), "IT-SERV", new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "Software development and support", null, "IT Services", new Guid("*************-2222-2222-************"), new Guid("********-1111-1111-1111-********1111"), "81110000" }
                });

            migrationBuilder.InsertData(
                schema: "public",
                table: "products",
                columns: new[] { "Id", "CategoryId", "CreatedAt", "Description", "IsActive", "ModifiedAt", "Name", "ProductCode", "TenantId", "UnitOfMeasure" },
                values: new object[,]
                {
                    { new Guid("*************-3333-3333-************"), new Guid("*************-2222-2222-************"), new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "Premium A4 copy paper, 80gsm, 500 sheets per ream, 5 reams per box", true, null, "A4 Copy Paper", "PAPER-001", new Guid("********-1111-1111-1111-********1111"), "Box" },
                    { new Guid("*************-3333-3333-************"), new Guid("*************-2222-2222-************"), new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "A5 spiral-bound notebook, 100 pages, ruled", true, null, "Spiral Notebook", "PAPER-002", new Guid("********-1111-1111-1111-********1111"), "Each" },
                    { new Guid("*************-3333-3333-************"), new Guid("*************-2222-2222-************"), new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "Blue ballpoint pens, medium point, 12 pens per box", true, null, "Ballpoint Pen", "WRITE-001", new Guid("********-1111-1111-1111-********1111"), "Box" },
                    { new Guid("*************-3333-3333-************"), new Guid("*************-2222-2222-************"), new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "15.6\" business laptop, 16GB RAM, 512GB SSD, Intel Core i7", true, null, "Laptop Computer", "COMP-001", new Guid("********-1111-1111-1111-********1111"), "Each" },
                    { new Guid("*************-3333-3333-************"), new Guid("*************-2222-2222-************"), new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "24-inch LED monitor, 1080p, HDMI and DisplayPort", true, null, "24\" Monitor", "PERIPH-001", new Guid("********-1111-1111-1111-********1111"), "Each" },
                    { new Guid("*************-3333-3333-************"), new Guid("*************-2222-2222-************"), new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "Ergonomic wireless mouse, 2.4GHz, USB receiver", true, null, "Wireless Mouse", "PERIPH-002", new Guid("********-1111-1111-1111-********1111"), "Each" }
                });

            migrationBuilder.CreateIndex(
                name: "IX_AspNetRoleClaims_RoleId",
                table: "AspNetRoleClaims",
                column: "RoleId");

            migrationBuilder.CreateIndex(
                name: "RoleNameIndex",
                table: "AspNetRoles",
                column: "NormalizedName",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_AspNetUserClaims_UserId",
                table: "AspNetUserClaims",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_AspNetUserLogins_UserId",
                table: "AspNetUserLogins",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_AspNetUserRoles_RoleId",
                table: "AspNetUserRoles",
                column: "RoleId");

            migrationBuilder.CreateIndex(
                name: "EmailIndex",
                table: "AspNetUsers",
                column: "NormalizedEmail");

            migrationBuilder.CreateIndex(
                name: "UserNameIndex",
                table: "AspNetUsers",
                column: "NormalizedUserName",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_AuditLogs_ActionType",
                table: "AuditLogs",
                column: "ActionType");

            migrationBuilder.CreateIndex(
                name: "IX_AuditLogs_PerformingUserId",
                table: "AuditLogs",
                column: "PerformingUserId");

            migrationBuilder.CreateIndex(
                name: "IX_AuditLogs_TargetEntityType_TargetEntityId",
                table: "AuditLogs",
                columns: new[] { "TargetEntityType", "TargetEntityId" });

            migrationBuilder.CreateIndex(
                name: "IX_AuditLogs_Timestamp",
                table: "AuditLogs",
                column: "Timestamp");

            migrationBuilder.CreateIndex(
                name: "IX_AuditLogs_UserId",
                table: "AuditLogs",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_budget_allocations_BudgetId",
                schema: "public",
                table: "budget_allocations",
                column: "BudgetId");

            migrationBuilder.CreateIndex(
                name: "IX_budget_allocations_BudgetId_FiscalPeriodIdentifier",
                schema: "public",
                table: "budget_allocations",
                columns: new[] { "BudgetId", "FiscalPeriodIdentifier" });

            migrationBuilder.CreateIndex(
                name: "IX_budget_allocations_DepartmentId",
                schema: "public",
                table: "budget_allocations",
                column: "DepartmentId");

            migrationBuilder.CreateIndex(
                name: "IX_budget_allocations_FiscalPeriodIdentifier",
                schema: "public",
                table: "budget_allocations",
                column: "FiscalPeriodIdentifier");

            migrationBuilder.CreateIndex(
                name: "IX_budget_allocations_Status",
                schema: "public",
                table: "budget_allocations",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_budget_allocations_TenantId",
                schema: "public",
                table: "budget_allocations",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_budgets_ApprovedById",
                schema: "public",
                table: "budgets",
                column: "ApprovedById");

            migrationBuilder.CreateIndex(
                name: "IX_budgets_CreatedById",
                schema: "public",
                table: "budgets",
                column: "CreatedById");

            migrationBuilder.CreateIndex(
                name: "IX_budgets_EndDate",
                schema: "public",
                table: "budgets",
                column: "EndDate");

            migrationBuilder.CreateIndex(
                name: "IX_budgets_FiscalYear",
                schema: "public",
                table: "budgets",
                column: "FiscalYear");

            migrationBuilder.CreateIndex(
                name: "IX_budgets_IsDeleted",
                schema: "public",
                table: "budgets",
                column: "IsDeleted");

            migrationBuilder.CreateIndex(
                name: "IX_budgets_Name",
                schema: "public",
                table: "budgets",
                column: "Name");

            migrationBuilder.CreateIndex(
                name: "IX_budgets_StartDate",
                schema: "public",
                table: "budgets",
                column: "StartDate");

            migrationBuilder.CreateIndex(
                name: "IX_budgets_Status",
                schema: "public",
                table: "budgets",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_budgets_TenantId",
                schema: "public",
                table: "budgets",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_budgets_WorkflowInstanceId",
                schema: "public",
                table: "budgets",
                column: "WorkflowInstanceId");

            migrationBuilder.CreateIndex(
                name: "IX_CatalogProducts_CategoryId",
                schema: "catalog",
                table: "CatalogProducts",
                column: "CategoryId");

            migrationBuilder.CreateIndex(
                name: "IX_CatalogProducts_SellerOrgId",
                schema: "catalog",
                table: "CatalogProducts",
                column: "SellerOrgId");

            migrationBuilder.CreateIndex(
                name: "IX_categories_code_unique",
                schema: "public",
                table: "categories",
                column: "Code",
                unique: true,
                filter: "\"code\" IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_categories_ParentCategoryId",
                schema: "public",
                table: "categories",
                column: "ParentCategoryId");

            migrationBuilder.CreateIndex(
                name: "IX_categories_TenantId",
                schema: "public",
                table: "categories",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_categories_unspsc_code",
                schema: "public",
                table: "categories",
                column: "UnspscCode");

            migrationBuilder.CreateIndex(
                name: "IX_contracts_ContractNumber",
                table: "contracts",
                column: "ContractNumber",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_contracts_EndDate",
                table: "contracts",
                column: "EndDate");

            migrationBuilder.CreateIndex(
                name: "IX_contracts_IsDeleted",
                table: "contracts",
                column: "IsDeleted");

            migrationBuilder.CreateIndex(
                name: "IX_contracts_StartDate",
                table: "contracts",
                column: "StartDate");

            migrationBuilder.CreateIndex(
                name: "IX_contracts_Status",
                table: "contracts",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_contracts_TenantId",
                table: "contracts",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_contracts_VendorId",
                table: "contracts",
                column: "VendorId");

            migrationBuilder.CreateIndex(
                name: "IX_customers_AssignedSalesRepId",
                schema: "public",
                table: "customers",
                column: "AssignedSalesRepId");

            migrationBuilder.CreateIndex(
                name: "IX_customers_customer_type",
                schema: "public",
                table: "customers",
                column: "customer_type");

            migrationBuilder.CreateIndex(
                name: "IX_customers_is_active",
                schema: "public",
                table: "customers",
                column: "is_active");

            migrationBuilder.CreateIndex(
                name: "IX_customers_IsDeleted",
                schema: "public",
                table: "customers",
                column: "IsDeleted");

            migrationBuilder.CreateIndex(
                name: "IX_customers_name",
                schema: "public",
                table: "customers",
                column: "name");

            migrationBuilder.CreateIndex(
                name: "IX_customers_tax_identifier",
                schema: "public",
                table: "customers",
                column: "tax_identifier");

            migrationBuilder.CreateIndex(
                name: "IX_customers_TenantId",
                schema: "public",
                table: "customers",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_customers_TenantId_customer_code",
                schema: "public",
                table: "customers",
                columns: new[] { "TenantId", "customer_code" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_delivery_note_lines_ProductId",
                table: "delivery_note_lines",
                column: "ProductId");

            migrationBuilder.CreateIndex(
                name: "IX_delivery_note_lines_PurchaseOrderLineId",
                table: "delivery_note_lines",
                column: "PurchaseOrderLineId");

            migrationBuilder.CreateIndex(
                name: "IX_delivery_note_lines_SalesOrderId_SalesOrderLineNumber",
                table: "delivery_note_lines",
                columns: new[] { "SalesOrderId", "SalesOrderLineNumber" });

            migrationBuilder.CreateIndex(
                name: "IX_delivery_notes_DeliveryDate",
                table: "delivery_notes",
                column: "DeliveryDate");

            migrationBuilder.CreateIndex(
                name: "IX_delivery_notes_DeliveryNoteNumber",
                table: "delivery_notes",
                column: "DeliveryNoteNumber",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_delivery_notes_PurchaseOrderId",
                table: "delivery_notes",
                column: "PurchaseOrderId");

            migrationBuilder.CreateIndex(
                name: "IX_delivery_notes_SalesOrderId",
                table: "delivery_notes",
                column: "SalesOrderId");

            migrationBuilder.CreateIndex(
                name: "IX_delivery_notes_Status",
                table: "delivery_notes",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_delivery_notes_TenantId",
                table: "delivery_notes",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_delivery_notes_VendorId",
                table: "delivery_notes",
                column: "VendorId");

            migrationBuilder.CreateIndex(
                name: "IX_departments_Name",
                schema: "public",
                table: "departments",
                column: "Name");

            migrationBuilder.CreateIndex(
                name: "IX_departments_TenantId",
                schema: "public",
                table: "departments",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_departments_TenantId_Code",
                schema: "public",
                table: "departments",
                columns: new[] { "TenantId", "Code" },
                unique: true,
                filter: "\"code\" IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_goods_receipt_note_lines_DeliveryNoteLineDeliveryNoteId_Del~",
                table: "goods_receipt_note_lines",
                columns: new[] { "DeliveryNoteLineDeliveryNoteId", "DeliveryNoteLineLineNumber" });

            migrationBuilder.CreateIndex(
                name: "IX_goods_receipt_note_lines_ExpiryDate",
                table: "goods_receipt_note_lines",
                column: "ExpiryDate");

            migrationBuilder.CreateIndex(
                name: "IX_goods_receipt_note_lines_ProductId",
                table: "goods_receipt_note_lines",
                column: "ProductId");

            migrationBuilder.CreateIndex(
                name: "IX_goods_receipt_note_lines_PurchaseOrderLineId",
                table: "goods_receipt_note_lines",
                column: "PurchaseOrderLineId");

            migrationBuilder.CreateIndex(
                name: "IX_goods_receipt_note_lines_QualityControlStatus",
                table: "goods_receipt_note_lines",
                column: "QualityControlStatus");

            migrationBuilder.CreateIndex(
                name: "IX_goods_receipt_notes_DeliveryNoteId",
                table: "goods_receipt_notes",
                column: "DeliveryNoteId");

            migrationBuilder.CreateIndex(
                name: "IX_goods_receipt_notes_GoodsReceiptNoteNumber",
                table: "goods_receipt_notes",
                column: "GoodsReceiptNoteNumber",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_goods_receipt_notes_PurchaseOrderId",
                table: "goods_receipt_notes",
                column: "PurchaseOrderId");

            migrationBuilder.CreateIndex(
                name: "IX_goods_receipt_notes_ReceiptDate",
                table: "goods_receipt_notes",
                column: "ReceiptDate");

            migrationBuilder.CreateIndex(
                name: "IX_goods_receipt_notes_ReceivedByUserId",
                table: "goods_receipt_notes",
                column: "ReceivedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_goods_receipt_notes_Status",
                table: "goods_receipt_notes",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_goods_receipt_notes_TenantId",
                table: "goods_receipt_notes",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_goods_receipt_notes_VendorId",
                table: "goods_receipt_notes",
                column: "VendorId");

            migrationBuilder.CreateIndex(
                name: "IX_invoice_lines_ProductId",
                table: "invoice_lines",
                column: "ProductId");

            migrationBuilder.CreateIndex(
                name: "IX_invoice_lines_PurchaseOrderLineId",
                table: "invoice_lines",
                column: "PurchaseOrderLineId");

            migrationBuilder.CreateIndex(
                name: "IX_invoices_CustomerId",
                table: "invoices",
                column: "CustomerId");

            migrationBuilder.CreateIndex(
                name: "IX_invoices_DueDate",
                table: "invoices",
                column: "DueDate");

            migrationBuilder.CreateIndex(
                name: "IX_invoices_InvoiceDate",
                table: "invoices",
                column: "InvoiceDate");

            migrationBuilder.CreateIndex(
                name: "IX_invoices_PurchaseOrderId",
                table: "invoices",
                column: "PurchaseOrderId");

            migrationBuilder.CreateIndex(
                name: "IX_invoices_Status",
                table: "invoices",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_invoices_TenantId",
                table: "invoices",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_invoices_VendorId",
                table: "invoices",
                column: "VendorId");

            migrationBuilder.CreateIndex(
                name: "IX_invoices_VendorId_InvoiceNumber",
                table: "invoices",
                columns: new[] { "VendorId", "InvoiceNumber" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_P2PRoles_Name",
                table: "P2PRoles",
                column: "Name",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_P2PUsers_Email",
                table: "P2PUsers",
                column: "Email",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_P2PUsers_OrganizationId",
                table: "P2PUsers",
                column: "OrganizationId");

            migrationBuilder.CreateIndex(
                name: "IX_P2PUsers_Username",
                table: "P2PUsers",
                column: "Username",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_PasswordPolicies_TenantId",
                table: "PasswordPolicies",
                column: "TenantId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_payment_transactions_InvoiceId",
                table: "payment_transactions",
                column: "InvoiceId");

            migrationBuilder.CreateIndex(
                name: "IX_payment_transactions_PaymentDate",
                table: "payment_transactions",
                column: "PaymentDate");

            migrationBuilder.CreateIndex(
                name: "IX_payment_transactions_Status",
                table: "payment_transactions",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_payment_transactions_TenantId",
                table: "payment_transactions",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_payment_transactions_transaction_reference",
                table: "payment_transactions",
                column: "transaction_reference",
                unique: true,
                filter: "\"transaction_reference\" IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_payment_transactions_VendorId",
                table: "payment_transactions",
                column: "VendorId");

            migrationBuilder.CreateIndex(
                name: "IX_procurement_workflow_steps_ApproverRoleId",
                table: "procurement_workflow_steps",
                column: "ApproverRoleId");

            migrationBuilder.CreateIndex(
                name: "IX_procurement_workflow_steps_ApproverUserId",
                table: "procurement_workflow_steps",
                column: "ApproverUserId");

            migrationBuilder.CreateIndex(
                name: "IX_procurement_workflow_steps_ProcurementWorkflowId",
                table: "procurement_workflow_steps",
                column: "ProcurementWorkflowId");

            migrationBuilder.CreateIndex(
                name: "IX_procurement_workflow_steps_ProcurementWorkflowId_StepOrder",
                table: "procurement_workflow_steps",
                columns: new[] { "ProcurementWorkflowId", "StepOrder" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_procurement_workflow_steps_WorkflowId",
                table: "procurement_workflow_steps",
                column: "WorkflowId");

            migrationBuilder.CreateIndex(
                name: "IX_procurement_workflows_IsActive",
                table: "procurement_workflows",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_procurement_workflows_Name",
                table: "procurement_workflows",
                column: "Name");

            migrationBuilder.CreateIndex(
                name: "IX_procurement_workflows_TenantId",
                table: "procurement_workflows",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_procurement_workflows_TenantId_WorkflowType_Name",
                table: "procurement_workflows",
                columns: new[] { "TenantId", "WorkflowType", "Name" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_procurement_workflows_WorkflowType",
                table: "procurement_workflows",
                column: "WorkflowType");

            migrationBuilder.CreateIndex(
                name: "IX_product_definitions_CategoryId",
                table: "product_definitions",
                column: "CategoryId");

            migrationBuilder.CreateIndex(
                name: "IX_product_definitions_Ean",
                table: "product_definitions",
                column: "Ean",
                unique: true,
                filter: "\"ean\" IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_product_definitions_Gtin",
                table: "product_definitions",
                column: "Gtin",
                unique: true,
                filter: "\"gtin\" IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_product_definitions_IsDeleted",
                table: "product_definitions",
                column: "IsDeleted");

            migrationBuilder.CreateIndex(
                name: "IX_product_definitions_Sku",
                table: "product_definitions",
                column: "Sku",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_product_definitions_Upc",
                table: "product_definitions",
                column: "Upc",
                unique: true,
                filter: "\"upc\" IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_ProductCategories_ParentCategoryId",
                schema: "catalog",
                table: "ProductCategories",
                column: "ParentCategoryId");

            migrationBuilder.CreateIndex(
                name: "IX_products_category_id",
                schema: "public",
                table: "products",
                column: "CategoryId");

            migrationBuilder.CreateIndex(
                name: "IX_products_is_active",
                schema: "public",
                table: "products",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_products_is_deleted",
                schema: "public",
                table: "products",
                column: "IsDeleted");

            migrationBuilder.CreateIndex(
                name: "IX_products_name",
                schema: "public",
                table: "products",
                column: "Name");

            migrationBuilder.CreateIndex(
                name: "IX_products_tenant_id",
                schema: "public",
                table: "products",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_products_tenant_id_product_code_unique",
                schema: "public",
                table: "products",
                columns: new[] { "TenantId", "ProductCode" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_projects_EndDate",
                schema: "public",
                table: "projects",
                column: "EndDate");

            migrationBuilder.CreateIndex(
                name: "IX_projects_IsDeleted",
                schema: "public",
                table: "projects",
                column: "IsDeleted");

            migrationBuilder.CreateIndex(
                name: "IX_projects_Name",
                schema: "public",
                table: "projects",
                column: "Name");

            migrationBuilder.CreateIndex(
                name: "IX_projects_StartDate",
                schema: "public",
                table: "projects",
                column: "StartDate");

            migrationBuilder.CreateIndex(
                name: "IX_projects_Status",
                schema: "public",
                table: "projects",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_projects_TenantId",
                schema: "public",
                table: "projects",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_projects_TenantId_ProjectCode",
                schema: "public",
                table: "projects",
                columns: new[] { "TenantId", "ProjectCode" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_purchase_order_lines_PurchaseOrderId",
                table: "purchase_order_lines",
                column: "PurchaseOrderId");

            migrationBuilder.CreateIndex(
                name: "IX_purchase_order_lines_VendorProductId",
                table: "purchase_order_lines",
                column: "VendorProductId");

            migrationBuilder.CreateIndex(
                name: "IX_purchase_orders_ContractId",
                table: "purchase_orders",
                column: "ContractId");

            migrationBuilder.CreateIndex(
                name: "IX_purchase_orders_OrderDate",
                table: "purchase_orders",
                column: "OrderDate");

            migrationBuilder.CreateIndex(
                name: "IX_purchase_orders_OrderNumber",
                table: "purchase_orders",
                column: "OrderNumber",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_purchase_orders_RequisitionId",
                table: "purchase_orders",
                column: "RequisitionId");

            migrationBuilder.CreateIndex(
                name: "IX_purchase_orders_Status",
                table: "purchase_orders",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_purchase_orders_VendorId",
                table: "purchase_orders",
                column: "VendorId");

            migrationBuilder.CreateIndex(
                name: "IX_PurchaseOrder_DeliveryDate",
                table: "purchase_orders",
                column: "DeliveryDate");

            migrationBuilder.CreateIndex(
                name: "IX_purchase_requisition_lines_GLAccountCode",
                table: "purchase_requisition_lines",
                column: "GLAccountCode");

            migrationBuilder.CreateIndex(
                name: "IX_purchase_requisition_lines_ProductDefinitionId",
                table: "purchase_requisition_lines",
                column: "ProductDefinitionId");

            migrationBuilder.CreateIndex(
                name: "IX_purchase_requisition_lines_PurchaseRequisitionId_LineNumber",
                table: "purchase_requisition_lines",
                columns: new[] { "PurchaseRequisitionId", "LineNumber" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_purchase_requisition_lines_SuggestedVendorId",
                table: "purchase_requisition_lines",
                column: "SuggestedVendorId");

            migrationBuilder.CreateIndex(
                name: "IX_purchase_requisition_lines_TenantId",
                table: "purchase_requisition_lines",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_purchase_requisition_lines_VendorProductId",
                table: "purchase_requisition_lines",
                column: "VendorProductId");

            migrationBuilder.CreateIndex(
                name: "IX_purchase_requisitions_AssociatedPurchaseOrderId",
                table: "purchase_requisitions",
                column: "AssociatedPurchaseOrderId");

            migrationBuilder.CreateIndex(
                name: "IX_purchase_requisitions_RequestDate",
                table: "purchase_requisitions",
                column: "RequestDate");

            migrationBuilder.CreateIndex(
                name: "IX_purchase_requisitions_RequestorUserId",
                table: "purchase_requisitions",
                column: "RequestorUserId");

            migrationBuilder.CreateIndex(
                name: "IX_purchase_requisitions_Status",
                table: "purchase_requisitions",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_purchase_requisitions_TenantId",
                table: "purchase_requisitions",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_purchase_requisitions_TenantId_RequisitionNumber",
                table: "purchase_requisitions",
                columns: new[] { "TenantId", "RequisitionNumber" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_request_for_quote_lines_IsDeleted",
                schema: "public",
                table: "request_for_quote_lines",
                column: "IsDeleted");

            migrationBuilder.CreateIndex(
                name: "IX_request_for_quote_lines_ProductDefinitionId",
                schema: "public",
                table: "request_for_quote_lines",
                column: "ProductDefinitionId");

            migrationBuilder.CreateIndex(
                name: "IX_request_for_quote_lines_RequestForQuoteId_LineNumber",
                schema: "public",
                table: "request_for_quote_lines",
                columns: new[] { "RequestForQuoteId", "LineNumber" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_request_for_quote_lines_TenantId",
                schema: "public",
                table: "request_for_quote_lines",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_request_for_quote_lines_VendorProductId",
                schema: "public",
                table: "request_for_quote_lines",
                column: "VendorProductId");

            migrationBuilder.CreateIndex(
                name: "IX_request_for_quotes_AwardedVendorId",
                schema: "public",
                table: "request_for_quotes",
                column: "AwardedVendorId");

            migrationBuilder.CreateIndex(
                name: "IX_request_for_quotes_CreatedByUserId",
                schema: "public",
                table: "request_for_quotes",
                column: "CreatedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_request_for_quotes_IsDeleted",
                schema: "public",
                table: "request_for_quotes",
                column: "IsDeleted");

            migrationBuilder.CreateIndex(
                name: "IX_request_for_quotes_OriginatingRequisitionId",
                schema: "public",
                table: "request_for_quotes",
                column: "OriginatingRequisitionId");

            migrationBuilder.CreateIndex(
                name: "IX_request_for_quotes_RelatedAgreementId",
                schema: "public",
                table: "request_for_quotes",
                column: "RelatedAgreementId");

            migrationBuilder.CreateIndex(
                name: "IX_request_for_quotes_Status",
                schema: "public",
                table: "request_for_quotes",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_request_for_quotes_SubmissionDeadline",
                schema: "public",
                table: "request_for_quotes",
                column: "SubmissionDeadline");

            migrationBuilder.CreateIndex(
                name: "IX_request_for_quotes_TenantId",
                schema: "public",
                table: "request_for_quotes",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_request_for_quotes_TenantId_RFQNumber",
                schema: "public",
                table: "request_for_quotes",
                columns: new[] { "TenantId", "RFQNumber" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_requests_for_information_ProjectId",
                table: "requests_for_information",
                column: "ProjectId");

            migrationBuilder.CreateIndex(
                name: "IX_requests_for_information_ResponseDeadline",
                table: "requests_for_information",
                column: "ResponseDeadline");

            migrationBuilder.CreateIndex(
                name: "IX_requests_for_information_RfiNumber",
                table: "requests_for_information",
                column: "RfiNumber",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_requests_for_information_Status",
                table: "requests_for_information",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_requests_for_information_TenantId",
                table: "requests_for_information",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_requests_for_proposal_AwardedContractId",
                table: "requests_for_proposal",
                column: "AwardedContractId");

            migrationBuilder.CreateIndex(
                name: "IX_requests_for_proposal_AwardedVendorId",
                table: "requests_for_proposal",
                column: "AwardedVendorId");

            migrationBuilder.CreateIndex(
                name: "IX_requests_for_proposal_ProjectId",
                table: "requests_for_proposal",
                column: "ProjectId");

            migrationBuilder.CreateIndex(
                name: "IX_requests_for_proposal_RfpNumber",
                table: "requests_for_proposal",
                column: "RfpNumber",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_requests_for_proposal_Status",
                table: "requests_for_proposal",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_requests_for_proposal_SubmissionDeadline",
                table: "requests_for_proposal",
                column: "SubmissionDeadline");

            migrationBuilder.CreateIndex(
                name: "IX_requests_for_proposal_TenantId",
                table: "requests_for_proposal",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_return_authorization_lines_InvoiceId_InvoiceLineNumber",
                table: "return_authorization_lines",
                columns: new[] { "InvoiceId", "InvoiceLineNumber" });

            migrationBuilder.CreateIndex(
                name: "IX_return_authorization_lines_OriginalSalesOrderLineSalesOrder~",
                table: "return_authorization_lines",
                columns: new[] { "OriginalSalesOrderLineSalesOrderId", "OriginalSalesOrderLineLineNumber" });

            migrationBuilder.CreateIndex(
                name: "IX_return_authorization_lines_ProductDefinitionId",
                table: "return_authorization_lines",
                column: "ProductDefinitionId");

            migrationBuilder.CreateIndex(
                name: "IX_return_authorization_lines_ProductId",
                table: "return_authorization_lines",
                column: "ProductId");

            migrationBuilder.CreateIndex(
                name: "IX_return_authorization_lines_SalesOrderId_SalesOrderLineNumber",
                table: "return_authorization_lines",
                columns: new[] { "SalesOrderId", "SalesOrderLineNumber" });

            migrationBuilder.CreateIndex(
                name: "IX_return_authorization_lines_VendorProductId",
                table: "return_authorization_lines",
                column: "VendorProductId");

            migrationBuilder.CreateIndex(
                name: "IX_return_authorizations_CustomerId",
                table: "return_authorizations",
                column: "CustomerId");

            migrationBuilder.CreateIndex(
                name: "IX_return_authorizations_InvoiceId",
                table: "return_authorizations",
                column: "InvoiceId");

            migrationBuilder.CreateIndex(
                name: "IX_return_authorizations_RequestDate",
                table: "return_authorizations",
                column: "RequestDate");

            migrationBuilder.CreateIndex(
                name: "IX_return_authorizations_RmaNumber",
                table: "return_authorizations",
                column: "RmaNumber",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_return_authorizations_SalesOrderId",
                table: "return_authorizations",
                column: "SalesOrderId");

            migrationBuilder.CreateIndex(
                name: "IX_return_authorizations_Status",
                table: "return_authorizations",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_return_authorizations_TenantId",
                table: "return_authorizations",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_RolePermissions_RoleId_PermissionString",
                table: "RolePermissions",
                columns: new[] { "RoleId", "PermissionString" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_sales_order_lines_ParentSalesOrderLineSalesOrderId_ParentSa~",
                table: "sales_order_lines",
                columns: new[] { "ParentSalesOrderLineSalesOrderId", "ParentSalesOrderLineLineNumber" });

            migrationBuilder.CreateIndex(
                name: "IX_sales_order_lines_ProductId",
                table: "sales_order_lines",
                column: "ProductId");

            migrationBuilder.CreateIndex(
                name: "IX_sales_order_lines_ProjectId",
                table: "sales_order_lines",
                column: "ProjectId");

            migrationBuilder.CreateIndex(
                name: "IX_sales_order_lines_ProjectId1",
                table: "sales_order_lines",
                column: "ProjectId1");

            migrationBuilder.CreateIndex(
                name: "IX_sales_order_lines_VendorProductId",
                table: "sales_order_lines",
                column: "VendorProductId");

            migrationBuilder.CreateIndex(
                name: "IX_sales_orders_CustomerId",
                schema: "public",
                table: "sales_orders",
                column: "CustomerId");

            migrationBuilder.CreateIndex(
                name: "IX_sales_orders_EdiTransactionReference",
                schema: "public",
                table: "sales_orders",
                column: "EdiTransactionReference");

            migrationBuilder.CreateIndex(
                name: "IX_sales_orders_IsDeleted",
                schema: "public",
                table: "sales_orders",
                column: "IsDeleted");

            migrationBuilder.CreateIndex(
                name: "IX_sales_orders_OrderDate",
                schema: "public",
                table: "sales_orders",
                column: "OrderDate");

            migrationBuilder.CreateIndex(
                name: "IX_sales_orders_RelatedReturnAuthorizationId",
                schema: "public",
                table: "sales_orders",
                column: "RelatedReturnAuthorizationId");

            migrationBuilder.CreateIndex(
                name: "IX_sales_orders_SalespersonId",
                schema: "public",
                table: "sales_orders",
                column: "SalespersonId");

            migrationBuilder.CreateIndex(
                name: "IX_sales_orders_SalesTerritoryId",
                schema: "public",
                table: "sales_orders",
                column: "SalesTerritoryId");

            migrationBuilder.CreateIndex(
                name: "IX_sales_orders_SalesTerritoryId1",
                schema: "public",
                table: "sales_orders",
                column: "SalesTerritoryId1");

            migrationBuilder.CreateIndex(
                name: "IX_sales_orders_Status",
                schema: "public",
                table: "sales_orders",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_sales_orders_TenantId",
                schema: "public",
                table: "sales_orders",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_sales_orders_TenantId_OrderNumber",
                schema: "public",
                table: "sales_orders",
                columns: new[] { "TenantId", "OrderNumber" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_sales_territories_Name",
                table: "sales_territories",
                column: "Name");

            migrationBuilder.CreateIndex(
                name: "IX_sales_territories_ParentTerritoryId",
                table: "sales_territories",
                column: "ParentTerritoryId");

            migrationBuilder.CreateIndex(
                name: "IX_sales_territories_TenantId",
                table: "sales_territories",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_sales_territories_TerritoryCode",
                table: "sales_territories",
                column: "TerritoryCode",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_sales_territory_representatives_representative_id",
                table: "sales_territory_representatives",
                column: "representative_id");

            migrationBuilder.CreateIndex(
                name: "IX_SpeckleProjectLinks_P2PProjectId",
                table: "SpeckleProjectLinks",
                column: "P2PProjectId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_submittal_reviews_Disposition",
                table: "submittal_reviews",
                column: "Disposition");

            migrationBuilder.CreateIndex(
                name: "IX_submittal_reviews_ReviewDate",
                table: "submittal_reviews",
                column: "ReviewDate");

            migrationBuilder.CreateIndex(
                name: "IX_submittal_reviews_ReviewerId",
                table: "submittal_reviews",
                column: "ReviewerId");

            migrationBuilder.CreateIndex(
                name: "IX_submittal_reviews_TechnicalSubmittalId",
                table: "submittal_reviews",
                column: "TechnicalSubmittalId");

            migrationBuilder.CreateIndex(
                name: "IX_submittal_reviews_TechnicalSubmittalId1",
                table: "submittal_reviews",
                column: "TechnicalSubmittalId1");

            migrationBuilder.CreateIndex(
                name: "IX_suppliers_IsContractManufacturer",
                table: "suppliers",
                column: "IsContractManufacturer");

            migrationBuilder.CreateIndex(
                name: "IX_suppliers_RiskRating",
                table: "suppliers",
                column: "RiskRating");

            migrationBuilder.CreateIndex(
                name: "IX_suppliers_VendorId",
                table: "suppliers",
                column: "VendorId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_technical_submittals_ContractId",
                table: "technical_submittals",
                column: "ContractId");

            migrationBuilder.CreateIndex(
                name: "IX_technical_submittals_ProjectId",
                table: "technical_submittals",
                column: "ProjectId");

            migrationBuilder.CreateIndex(
                name: "IX_technical_submittals_ProjectId_SubmittalNumber_Revision",
                table: "technical_submittals",
                columns: new[] { "ProjectId", "SubmittalNumber", "Revision" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_technical_submittals_PurchaseOrderLineId",
                table: "technical_submittals",
                column: "PurchaseOrderLineId");

            migrationBuilder.CreateIndex(
                name: "IX_technical_submittals_RequiredDate",
                table: "technical_submittals",
                column: "RequiredDate");

            migrationBuilder.CreateIndex(
                name: "IX_technical_submittals_SpecificationId",
                table: "technical_submittals",
                column: "SpecificationId");

            migrationBuilder.CreateIndex(
                name: "IX_technical_submittals_Status",
                table: "technical_submittals",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_technical_submittals_SubmittalNumber",
                table: "technical_submittals",
                column: "SubmittalNumber",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_technical_submittals_SubmittedByUserId",
                table: "technical_submittals",
                column: "SubmittedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_technical_submittals_SubmittedDate",
                table: "technical_submittals",
                column: "SubmittedDate");

            migrationBuilder.CreateIndex(
                name: "IX_technical_submittals_TenantId",
                table: "technical_submittals",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_technical_submittals_VendorId",
                table: "technical_submittals",
                column: "VendorId");

            migrationBuilder.CreateIndex(
                name: "IX_tenant_products_TenantId",
                table: "tenant_products",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_tenants_identifier_unique",
                schema: "public",
                table: "tenants",
                column: "Identifier",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_vendor_products_ProductDefinitionId",
                table: "vendor_products",
                column: "ProductDefinitionId");

            migrationBuilder.CreateIndex(
                name: "IX_vendor_products_VendorId_ProductDefinitionId_UnitOfMeasure_~",
                table: "vendor_products",
                columns: new[] { "VendorId", "ProductDefinitionId", "UnitOfMeasure", "PackSize" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_vendor_products_VendorId_VendorSku",
                table: "vendor_products",
                columns: new[] { "VendorId", "VendorSku" },
                filter: "\"vendor_sku\" IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_vendor_proposals_RequestForProposalId",
                table: "vendor_proposals",
                column: "RequestForProposalId");

            migrationBuilder.CreateIndex(
                name: "IX_vendor_proposals_RequestForProposalId_VendorId",
                table: "vendor_proposals",
                columns: new[] { "RequestForProposalId", "VendorId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_vendor_proposals_Status",
                table: "vendor_proposals",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_vendor_proposals_SubmissionDate",
                table: "vendor_proposals",
                column: "SubmissionDate");

            migrationBuilder.CreateIndex(
                name: "IX_vendor_proposals_TenantId",
                table: "vendor_proposals",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_vendor_proposals_VendorId",
                table: "vendor_proposals",
                column: "VendorId");

            migrationBuilder.CreateIndex(
                name: "IX_vendors_CommercialRegistrationNumber",
                table: "vendors",
                column: "CommercialRegistrationNumber");

            migrationBuilder.CreateIndex(
                name: "IX_vendors_Name",
                table: "vendors",
                column: "Name");

            migrationBuilder.CreateIndex(
                name: "IX_vendors_Status",
                table: "vendors",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_vendors_TaxId",
                table: "vendors",
                column: "TaxId");

            migrationBuilder.CreateIndex(
                name: "IX_vendors_VatNumber",
                table: "vendors",
                column: "VatNumber");

            migrationBuilder.CreateIndex(
                name: "IX_vendors_VendorCode",
                table: "vendors",
                column: "VendorCode",
                unique: true,
                filter: "\"vendor_code\" IS NOT NULL");

            migrationBuilder.AddForeignKey(
                name: "FK_delivery_note_lines_delivery_notes_DeliveryNoteId",
                table: "delivery_note_lines",
                column: "DeliveryNoteId",
                principalTable: "delivery_notes",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_delivery_note_lines_purchase_order_lines_PurchaseOrderLineId",
                table: "delivery_note_lines",
                column: "PurchaseOrderLineId",
                principalTable: "purchase_order_lines",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_delivery_note_lines_sales_order_lines_SalesOrderId_SalesOrd~",
                table: "delivery_note_lines",
                columns: new[] { "SalesOrderId", "SalesOrderLineNumber" },
                principalTable: "sales_order_lines",
                principalColumns: new[] { "SalesOrderId", "LineNumber" },
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_delivery_notes_purchase_orders_PurchaseOrderId",
                table: "delivery_notes",
                column: "PurchaseOrderId",
                principalTable: "purchase_orders",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_delivery_notes_sales_orders_SalesOrderId",
                table: "delivery_notes",
                column: "SalesOrderId",
                principalSchema: "public",
                principalTable: "sales_orders",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_goods_receipt_note_lines_goods_receipt_notes_GoodsReceiptNo~",
                table: "goods_receipt_note_lines",
                column: "GoodsReceiptNoteId",
                principalTable: "goods_receipt_notes",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_goods_receipt_note_lines_purchase_order_lines_PurchaseOrder~",
                table: "goods_receipt_note_lines",
                column: "PurchaseOrderLineId",
                principalTable: "purchase_order_lines",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_goods_receipt_notes_purchase_orders_PurchaseOrderId",
                table: "goods_receipt_notes",
                column: "PurchaseOrderId",
                principalTable: "purchase_orders",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_invoice_lines_invoices_InvoiceId",
                table: "invoice_lines",
                column: "InvoiceId",
                principalTable: "invoices",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_invoice_lines_purchase_order_lines_PurchaseOrderLineId",
                table: "invoice_lines",
                column: "PurchaseOrderLineId",
                principalTable: "purchase_order_lines",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_invoices_purchase_orders_PurchaseOrderId",
                table: "invoices",
                column: "PurchaseOrderId",
                principalTable: "purchase_orders",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_purchase_order_lines_purchase_orders_PurchaseOrderId",
                table: "purchase_order_lines",
                column: "PurchaseOrderId",
                principalTable: "purchase_orders",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_purchase_orders_purchase_requisitions_RequisitionId",
                table: "purchase_orders",
                column: "RequisitionId",
                principalTable: "purchase_requisitions",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_return_authorization_lines_return_authorizations_ReturnAuth~",
                table: "return_authorization_lines",
                column: "ReturnAuthorizationId",
                principalTable: "return_authorizations",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_return_authorization_lines_sales_order_lines_OriginalSalesO~",
                table: "return_authorization_lines",
                columns: new[] { "OriginalSalesOrderLineSalesOrderId", "OriginalSalesOrderLineLineNumber" },
                principalTable: "sales_order_lines",
                principalColumns: new[] { "SalesOrderId", "LineNumber" });

            migrationBuilder.AddForeignKey(
                name: "FK_return_authorization_lines_sales_order_lines_SalesOrderId_S~",
                table: "return_authorization_lines",
                columns: new[] { "SalesOrderId", "SalesOrderLineNumber" },
                principalTable: "sales_order_lines",
                principalColumns: new[] { "SalesOrderId", "LineNumber" },
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_return_authorizations_sales_orders_SalesOrderId",
                table: "return_authorizations",
                column: "SalesOrderId",
                principalSchema: "public",
                principalTable: "sales_orders",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_contracts_vendors_VendorId",
                table: "contracts");

            migrationBuilder.DropForeignKey(
                name: "FK_invoices_vendors_VendorId",
                table: "invoices");

            migrationBuilder.DropForeignKey(
                name: "FK_purchase_orders_vendors_VendorId",
                table: "purchase_orders");

            migrationBuilder.DropForeignKey(
                name: "FK_invoices_purchase_orders_PurchaseOrderId",
                table: "invoices");

            migrationBuilder.DropForeignKey(
                name: "FK_purchase_requisitions_purchase_orders_AssociatedPurchaseOrd~",
                table: "purchase_requisitions");

            migrationBuilder.DropForeignKey(
                name: "FK_return_authorizations_sales_orders_SalesOrderId",
                table: "return_authorizations");

            migrationBuilder.DropTable(
                name: "AspNetRoleClaims");

            migrationBuilder.DropTable(
                name: "AspNetUserClaims");

            migrationBuilder.DropTable(
                name: "AspNetUserLogins");

            migrationBuilder.DropTable(
                name: "AspNetUserRoles");

            migrationBuilder.DropTable(
                name: "AspNetUserTokens");

            migrationBuilder.DropTable(
                name: "AuditLogs");

            migrationBuilder.DropTable(
                name: "budget_allocations",
                schema: "public");

            migrationBuilder.DropTable(
                name: "CatalogProducts",
                schema: "catalog");

            migrationBuilder.DropTable(
                name: "DocumentLink");

            migrationBuilder.DropTable(
                name: "goods_receipt_note_lines");

            migrationBuilder.DropTable(
                name: "P2PUsers");

            migrationBuilder.DropTable(
                name: "PasswordPolicies");

            migrationBuilder.DropTable(
                name: "payment_transactions");

            migrationBuilder.DropTable(
                name: "procurement_workflow_steps");

            migrationBuilder.DropTable(
                name: "purchase_requisition_lines");

            migrationBuilder.DropTable(
                name: "request_for_quote_lines",
                schema: "public");

            migrationBuilder.DropTable(
                name: "requests_for_information");

            migrationBuilder.DropTable(
                name: "return_authorization_lines");

            migrationBuilder.DropTable(
                name: "RolePermissions");

            migrationBuilder.DropTable(
                name: "sales_territory_representatives");

            migrationBuilder.DropTable(
                name: "SpeckleProjectLinks");

            migrationBuilder.DropTable(
                name: "SqlMigrationHistories");

            migrationBuilder.DropTable(
                name: "submittal_reviews");

            migrationBuilder.DropTable(
                name: "suppliers");

            migrationBuilder.DropTable(
                name: "tenant_products");

            migrationBuilder.DropTable(
                name: "tenants",
                schema: "public");

            migrationBuilder.DropTable(
                name: "test_entities");

            migrationBuilder.DropTable(
                name: "vendor_proposals");

            migrationBuilder.DropTable(
                name: "AspNetRoles");

            migrationBuilder.DropTable(
                name: "budgets",
                schema: "public");

            migrationBuilder.DropTable(
                name: "departments",
                schema: "public");

            migrationBuilder.DropTable(
                name: "ProductCategories",
                schema: "catalog");

            migrationBuilder.DropTable(
                name: "delivery_note_lines");

            migrationBuilder.DropTable(
                name: "goods_receipt_notes");

            migrationBuilder.DropTable(
                name: "procurement_workflows");

            migrationBuilder.DropTable(
                name: "request_for_quotes",
                schema: "public");

            migrationBuilder.DropTable(
                name: "invoice_lines");

            migrationBuilder.DropTable(
                name: "P2PRoles");

            migrationBuilder.DropTable(
                name: "technical_submittals");

            migrationBuilder.DropTable(
                name: "requests_for_proposal");

            migrationBuilder.DropTable(
                name: "sales_order_lines");

            migrationBuilder.DropTable(
                name: "delivery_notes");

            migrationBuilder.DropTable(
                name: "AspNetUsers");

            migrationBuilder.DropTable(
                name: "purchase_order_lines");

            migrationBuilder.DropTable(
                name: "products",
                schema: "public");

            migrationBuilder.DropTable(
                name: "projects",
                schema: "public");

            migrationBuilder.DropTable(
                name: "vendor_products");

            migrationBuilder.DropTable(
                name: "product_definitions");

            migrationBuilder.DropTable(
                name: "categories",
                schema: "public");

            migrationBuilder.DropTable(
                name: "vendors");

            migrationBuilder.DropTable(
                name: "purchase_orders");

            migrationBuilder.DropTable(
                name: "contracts");

            migrationBuilder.DropTable(
                name: "purchase_requisitions");

            migrationBuilder.DropTable(
                name: "sales_orders",
                schema: "public");

            migrationBuilder.DropTable(
                name: "return_authorizations");

            migrationBuilder.DropTable(
                name: "sales_territories");

            migrationBuilder.DropTable(
                name: "invoices");

            migrationBuilder.DropTable(
                name: "customers",
                schema: "public");
        }
    }
}
