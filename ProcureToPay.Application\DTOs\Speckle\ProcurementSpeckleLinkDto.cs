using System;

namespace ProcureToPay.Application.DTOs.Speckle
{
    public class ProcurementSpeckleLinkDto
    {
        public Guid Id { get; set; }
        public Guid SpeckleObjectMetadataId { get; set; }

        // Details from SpeckleObjectMetadata (optional, denormalized for convenience)
        public string? SpeckleObjectId { get; set; } // From linked SpeckleObjectMetadata
        public string? ObjectType { get; set; } // From linked SpeckleObjectMetadata

        public string? ProcurementItemType { get; set; }
        public Guid ProcurementItemId { get; set; }

        public Guid LinkedByUserId { get; set; }
        public string? LinkedByUserName { get; set; } // For display

        public DateTime LinkedAt { get; set; }
    }
}
