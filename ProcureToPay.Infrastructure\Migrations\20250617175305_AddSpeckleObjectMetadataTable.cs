﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ProcureToPay.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddSpeckleObjectMetadataTable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "SpeckleObjectMetadata",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    SpeckleProjectLinkId = table.Column<Guid>(type: "uuid", nullable: false),
                    SpeckleObjectId = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    RevitElementId = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    IfcGuid = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    ObjectType = table.Column<string>(type: "character varying(512)", maxLength: 512, nullable: false),
                    ParametersJson = table.Column<string>(type: "text", nullable: false),
                    LastExtractedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SpeckleObjectMetadata", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SpeckleObjectMetadata_SpeckleProjectLinks_SpeckleProjectLin~",
                        column: x => x.SpeckleProjectLinkId,
                        principalTable: "SpeckleProjectLinks",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_SpeckleObjectMetadata_IfcGuid",
                table: "SpeckleObjectMetadata",
                column: "IfcGuid");

            migrationBuilder.CreateIndex(
                name: "IX_SpeckleObjectMetadata_RevitElementId",
                table: "SpeckleObjectMetadata",
                column: "RevitElementId");

            migrationBuilder.CreateIndex(
                name: "IX_SpeckleObjectMetadata_SpeckleProjectLinkId_SpeckleObjectId",
                table: "SpeckleObjectMetadata",
                columns: new[] { "SpeckleProjectLinkId", "SpeckleObjectId" },
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "SpeckleObjectMetadata");
        }
    }
}
