using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProcureToPay.Domain.Entities;

namespace ProcureToPay.Infrastructure.Persistence.Configurations
{
    public class AuditLogConfiguration : IEntityTypeConfiguration<AuditLog>
    {
        public void Configure(EntityTypeBuilder<AuditLog> builder)
        {
            builder.HasKey(al => al.Id);

            builder.Property(al => al.Timestamp).IsRequired();
            builder.Property(al => al.UserId).IsRequired(false); // Nullable
            builder.Property(al => al.PerformingUserId).IsRequired(false); // Nullable
            builder.Property(al => al.ActionType).IsRequired().HasMaxLength(256);
            builder.Property(al => al.Success).IsRequired();
            builder.Property(al => al.TargetEntityType).HasMaxLength(100).IsRequired(false);
            builder.Property(al => al.TargetEntityId).HasMaxLength(100).IsRequired(false); // String to accommodate various ID types
            builder.Property(al => al.IpAddress).HasMaxLength(45).IsRequired(false); // Max length for IPv6
            builder.Property(al => al.Details).IsRequired(false); // Can be text or JSON

            // Indexing common query fields
            builder.HasIndex(al => al.Timestamp);
            builder.HasIndex(al => al.UserId);
            builder.HasIndex(al => al.PerformingUserId);
            builder.HasIndex(al => al.ActionType);
            builder.HasIndex(al => new { al.TargetEntityType, al.TargetEntityId });
        }
    }
}
