using System;
using System.ComponentModel.DataAnnotations;

namespace ProcureToPay.Application.DTOs.Speckle
{
    public class CreateSpeckleLinkRequest
    {
        [Required]
        public Guid P2PProjectId { get; set; }

        [Required]
        [Url]
        [StringLength(2048)]
        public string? SpeckleServerUrl { get; set; }

        [Required]
        [StringLength(1024)]
        public string? SpeckleStreamId { get; set; }

        [StringLength(1024)]
        public string? SpeckleCommitId { get; set; } // Optional at creation

        [Required]
        // SECURITY NOTE: Transmitting tokens in requests needs care.
        // Ensure HTTPS. Consider if this should be a one-time setup token
        // exchanged for a server-managed one.
        [StringLength(2048)]
        public string? SpeckleAuthToken { get; set; }
    }
}
