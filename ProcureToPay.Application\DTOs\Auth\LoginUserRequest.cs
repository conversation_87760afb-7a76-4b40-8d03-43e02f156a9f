using System.ComponentModel.DataAnnotations;

namespace ProcureToPay.Application.DTOs.Auth
{
    public class LoginUserRequest
    {
        [Required]
        public string Username { get; set; } = string.Empty;

        [Required]
        public string Password { get; set; } = string.Empty;

        [StringLength(6, MinimumLength = 6)] // TOTP codes are typically 6 digits
        public string? MfaCode { get; set; } // Optional, for users with MFA enabled
    }
}
