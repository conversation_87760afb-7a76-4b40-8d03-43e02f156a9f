using Microsoft.EntityFrameworkCore;
using ProcureToPay.Application.DTOs.Speckle;
using ProcureToPay.Domain.Entities;
using ProcureToPay.Infrastructure.Persistence;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;

namespace ProcureToPay.Application.Services.Speckle
{
    public class BomService : IBomService
    {
        private readonly ApplicationDbContext _context;

        public BomService(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<BillOfMaterialDto> GenerateBomAsync(BOMGenerationRequestDto request)
        {
            var metadataItems = await _context.SpeckleObjectMetadata
                .Where(m => m.SpeckleProjectLinkId == request.SpeckleProjectLinkId)
                .ToListAsync();

            // TODO: Implement filtering based on request.FilterJson if provided.
            // For now, all metadataItems for the project link are processed.

            var groupedObjects = new Dictionary<string, List<SpeckleObjectMetadata>>();
            var groupKeyValuesCache = new Dictionary<string, Dictionary<string, string?>>();


            foreach (var item in metadataItems)
            {
                var parameters = JsonSerializer.Deserialize<Dictionary<string, JsonElement>>(item.ParametersJson ?? "{}");
                var currentItemKeyParts = new List<string>();
                var currentItemKeyValues = new Dictionary<string, string?>();

                foreach (var propName in request.GroupByProperties)
                {
                    string? valueString = null;
                    if (propName.Equals("ObjectType", StringComparison.OrdinalIgnoreCase))
                    {
                        valueString = item.ObjectType;
                    }
                    else if (propName.Equals("RevitElementId", StringComparison.OrdinalIgnoreCase))
                    {
                        valueString = item.RevitElementId;
                    }
                    else if (propName.Equals("IfcGuid", StringComparison.OrdinalIgnoreCase))
                    {
                        valueString = item.IfcGuid;
                    }
                    else if (parameters != null && parameters.TryGetValue(propName, out var jsonElement))
                    {
                        valueString = JsonElementToString(jsonElement);
                    }

                    currentItemKeyParts.Add(valueString ?? "N/A");
                    currentItemKeyValues[propName] = valueString ?? "N/A";
                }

                string groupKey = string.Join("|", currentItemKeyParts);

                if (!groupedObjects.ContainsKey(groupKey))
                {
                    groupedObjects[groupKey] = new List<SpeckleObjectMetadata>();
                    groupKeyValuesCache[groupKey] = currentItemKeyValues;
                }
                groupedObjects[groupKey].Add(item);
            }

            var bomItems = new List<BillOfMaterialItemDto>();
            var headers = new HashSet<string>(request.GroupByProperties);
            headers.UnionWith(request.IncludeParametersInBom); // Add distinct included parameters
            headers.Add("Quantity"); // Ensure Quantity is a header

            foreach (var group in groupedObjects)
            {
                var firstItemInGroup = group.Value.First(); // Used to extract common parameters
                var firstItemParams = JsonSerializer.Deserialize<Dictionary<string, JsonElement>>(firstItemInGroup.ParametersJson ?? "{}");

                var bomItemProperties = new Dictionary<string, string?>();

                // Populate properties based on GroupByProperties (already known via groupKeyValuesCache)
                foreach(var propName in request.GroupByProperties)
                {
                    bomItemProperties[propName] = groupKeyValuesCache[group.Key][propName];
                }

                // Populate properties based on IncludeParametersInBom
                foreach (var paramName in request.IncludeParametersInBom)
                {
                    if (!bomItemProperties.ContainsKey(paramName)) // Avoid duplicating if already a group-by prop
                    {
                        string? valueString = null;
                         if (paramName.Equals("ObjectType", StringComparison.OrdinalIgnoreCase))
                        {
                            valueString = firstItemInGroup.ObjectType;
                        }
                        else if (paramName.Equals("RevitElementId", StringComparison.OrdinalIgnoreCase))
                        {
                            valueString = firstItemInGroup.RevitElementId;
                        }
                        else if (paramName.Equals("IfcGuid", StringComparison.OrdinalIgnoreCase))
                        {
                            valueString = firstItemInGroup.IfcGuid;
                        }
                        else if (firstItemParams != null && firstItemParams.TryGetValue(paramName, out var jsonElement))
                        {
                           valueString = JsonElementToString(jsonElement);
                        }
                        bomItemProperties[paramName] = valueString ?? "N/A";
                    }
                }

                bomItems.Add(new BillOfMaterialItemDto
                {
                    GroupKey = group.Key,
                    Properties = bomItemProperties,
                    Quantity = group.Value.Count,
                    SpeckleObjectIds = group.Value.Select(m => m.SpeckleObjectId).ToList()
                });
            }

            // Ensure headers are in a predictable order: GroupBy first, then Included, then Quantity
            var finalHeaders = request.GroupByProperties
                                .Concat(request.IncludeParametersInBom.Where(p => !request.GroupByProperties.Contains(p)))
                                .Distinct()
                                .ToList();
            finalHeaders.Add("Quantity");


            return new BillOfMaterialDto
            {
                SpeckleProjectLinkId = request.SpeckleProjectLinkId,
                GeneratedAt = DateTime.UtcNow,
                GroupByProperties = request.GroupByProperties,
                Headers = finalHeaders,
                Items = bomItems
            };
        }

        private string? JsonElementToString(JsonElement element)
        {
            return element.ValueKind switch
            {
                JsonValueKind.String => element.GetString(),
                JsonValueKind.Number => element.GetRawText(), // Get as string to preserve formatting
                JsonValueKind.True => "true",
                JsonValueKind.False => "false",
                JsonValueKind.Null => null,
                _ => element.ToString() // For arrays, objects, etc., might need specific handling or just use ToString()
            };
        }
    }
}
