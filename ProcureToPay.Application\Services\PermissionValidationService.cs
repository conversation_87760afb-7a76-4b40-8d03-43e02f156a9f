using Microsoft.EntityFrameworkCore;
using ProcureToPay.Domain.Entities; // For User, Role, RolePermission etc.
using ProcureToPay.Infrastructure.Persistence;
using System.Linq;
using System.Threading.Tasks;
using System;

namespace ProcureToPay.Application.Services
{
    public interface IPermissionValidationService
    {
        Task<bool> HasPermissionAsync(Guid userId, string permissionName);
        // Adding a version that might take an IP address for more complete logging, if needed by the service itself
        Task<bool> HasPermissionAsync(Guid userId, string permissionName, string? ipAddress);
    }

    public class PermissionValidationService : IPermissionValidationService
    {
        private readonly ApplicationDbContext _context;
        private readonly IAuthenticationService _authenticationService; // For logging

        public PermissionValidationService(ApplicationDbContext context, IAuthenticationService authenticationService)
        {
            _context = context;
            _authenticationService = authenticationService;
        }

        public async Task<bool> HasPermissionAsync(Guid userId, string permissionName)
        {
            return await HasPermissionAsync(userId, permissionName, null); // Call the more detailed version
        }

        public async Task<bool> HasPermissionAsync(Guid userId, string permissionName, string? ipAddress)
        {
            var user = await _context.P2PUsers.FindAsync(userId);
            if (user == null)
            {
                await _authenticationService.LogAuditEvent(
                    actionType: "PermissionCheck_UserNotFound",
                    success: false,
                    userId: userId, // The userId that was attempted
                    performingUserId: null, // System or unknown context for this direct check
                    ipAddress: ipAddress,
                    details: $"User not found. Permission '{permissionName}' denied.");
                return false;
            }

            var roleName = "SystemRole_" + user.UserRole.ToString(); // Corrected from user.Role to user.UserRole

            var role = await _context.P2PRoles // Use P2PRoles as per previous corrections
                                     .AsNoTracking()
                                     .FirstOrDefaultAsync(r => r.Name == roleName);

            if (role == null)
            {
                await _authenticationService.LogAuditEvent(
                    actionType: "PermissionCheck_RoleNotFound",
                    success: false,
                    userId: userId,
                    performingUserId: null,
                    ipAddress: ipAddress,
                    details: $"Role '{roleName}' (derived from User.UserRole enum) not found in P2PRoles table for user '{userId}'. Permission '{permissionName}' denied.");
                return false;
            }

            var hasPermission = await _context.RolePermissions
                                              .AsNoTracking()
                                              .AnyAsync(rp => rp.RoleId == role.Id && rp.PermissionString == permissionName);

            // Log access decision (can be verbose, consider sampling or conditional logging for high-traffic permissions)
            await _authenticationService.LogAuditEvent(
                actionType: hasPermission ? "PermissionCheck_Granted" : "PermissionCheck_Denied",
                success: hasPermission,
                userId: userId, // User whose permission is being checked
                performingUserId: userId, // Assuming self-check or context where UserId is performer
                targetEntityType: "Permission",
                targetEntityId: permissionName,
                ipAddress: ipAddress,
                details: $"User '{userId}' (Role: {roleName}) attempt to access permission '{permissionName}'. Granted: {hasPermission}");

            return hasPermission;
        }
    }
}
