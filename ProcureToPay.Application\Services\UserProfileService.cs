using Microsoft.EntityFrameworkCore;
using ProcureToPay.Application.DTOs.Profile;
using ProcureToPay.Domain.Entities;
using ProcureToPay.Infrastructure.Persistence;
using System;
using System.Linq;
using System.Threading.Tasks;
using System.Collections.Generic; // For List

namespace ProcureToPay.Application.Services
{
    public interface IUserProfileService
    {
        Task<UserProfileDto?> GetUserProfileAsync(Guid userId);
        Task<(bool Success, IEnumerable<string> Errors)> UpdateUserProfileAsync(Guid userId, UpdateUserProfileRequest request, string? ipAddress);
        Task<(bool Success, IEnumerable<string> Errors)> ChangePasswordAsync(Guid userId, ChangePasswordRequest request, string? ipAddress);
        Task<MfaSettingsDto?> GetMfaSettingsAsync(Guid userId, string appName, string accountNameBase); // appName and accountNameBase for QR code generation if MFA not set up
        Task<(bool Success, IEnumerable<string> Errors)> EnableMfaAsync(Guid userId, string tempMfaSecret, string totpCode, string? ipAddress);
        Task<(bool Success, IEnumerable<string> Errors)> DisableMfaAsync(Guid userId, string totpCode, string? ipAddress);
    }

    public class UserProfileService : IUserProfileService
    {
        private readonly ApplicationDbContext _context;
        private readonly IPasswordService _passwordService;
        private readonly IAuthenticationService _authenticationService; // For ValidatePassword & LogAuditEvent (Consider refactoring LogAuditEvent to a shared service)
        private readonly IMfaService _mfaService;


        public UserProfileService(
            ApplicationDbContext context,
            IPasswordService passwordService,
            IAuthenticationService authenticationService,
            IMfaService mfaService)
        {
            _context = context;
            _passwordService = passwordService;
            _authenticationService = authenticationService;
            _mfaService = mfaService;
        }

        public async Task<UserProfileDto?> GetUserProfileAsync(Guid userId)
        {
            var user = await _context.P2PUsers.FindAsync(userId);
            if (user == null) return null;

            return new UserProfileDto
            {
                Username = user.Username,
                Email = user.Email,
                IsEmailVerified = user.IsEmailVerified,
                FullName = user.FullName,
                PhoneNumber = user.PhoneNumber,
                PreferredLanguage = user.PreferredLanguage,
                NotificationPreferences = user.NotificationPreferences,
                UserRole = user.UserRole.ToString()
            };
        }

        public async Task<(bool Success, IEnumerable<string> Errors)> UpdateUserProfileAsync(Guid userId, UpdateUserProfileRequest request, string? ipAddress)
        {
            var user = await _context.P2PUsers.FindAsync(userId);
            if (user == null) return (false, new List<string> { "User not found." });

            var errors = new List<string>();
            bool changed = false;

            if (request.FullName != null && user.FullName != request.FullName)
            {
                user.FullName = request.FullName;
                changed = true;
            }
            if (request.PhoneNumber != null && user.PhoneNumber != request.PhoneNumber)
            {
                user.PhoneNumber = request.PhoneNumber;
                changed = true;
            }
            if (request.PreferredLanguage != null && user.PreferredLanguage != request.PreferredLanguage)
            {
                user.PreferredLanguage = request.PreferredLanguage;
                changed = true;
            }
            if (request.NotificationPreferences != null && user.NotificationPreferences != request.NotificationPreferences)
            {
                user.NotificationPreferences = request.NotificationPreferences;
                changed = true;
            }

            if (request.Email != null && !string.Equals(user.Email, request.Email, StringComparison.OrdinalIgnoreCase))
            {
                // Check if new email is already in use by another user
                if (await _context.P2PUsers.AnyAsync(u => u.Email == request.Email && u.Id != userId))
                {
                    errors.Add("The new email address is already in use by another account.");
                    await _authenticationService.LogAuditEvent(actionType: "UpdateProfileAttempt_EmailInUse", success: false, userId: userId, performingUserId: userId, targetEntityType:"User", targetEntityId: userId.ToString(), ipAddress: ipAddress, details: $"Failed to update email for user {user.Username}: New email {request.Email} already in use.");
                    return (false, errors);
                }

                string oldEmail = user.Email; // Capture old email for logging
                user.Email = request.Email;
                user.IsEmailVerified = false;
                changed = true;
                Console.WriteLine($"User {userId} changed email to {request.Email}. Verification required. Old email: {oldEmail}");
                await _authenticationService.LogAuditEvent(actionType: "EmailChanged_VerificationRequired", success: true, userId: userId, performingUserId: userId, targetEntityType:"User", targetEntityId: userId.ToString(), ipAddress: ipAddress, details: $"User changed email to {request.Email}. Old email was {oldEmail}. Verification pending.");
            }

            if (!changed && !errors.Any()) return (true, Enumerable.Empty<string>());
            if (errors.Any()) return (false, errors);


            await _context.SaveChangesAsync();
            await _authenticationService.LogAuditEvent(actionType: "UpdateProfile_Success", success: true, userId: userId, performingUserId: userId, targetEntityType:"User", targetEntityId: userId.ToString(), ipAddress: ipAddress, details: "User profile updated successfully.");
            return (true, Enumerable.Empty<string>());
        }

        public async Task<(bool Success, IEnumerable<string> Errors)> ChangePasswordAsync(Guid userId, ChangePasswordRequest request, string? ipAddress)
        {
            var user = await _context.P2PUsers.FindAsync(userId);
            if (user == null) return (false, new List<string> { "User not found." });

            // If password hash is null (should not happen for an active user), or if old password doesn't match
            if (user.PasswordHash == null || !_passwordService.VerifyPassword(request.OldPassword, user.PasswordHash))
            {
                await _authenticationService.LogAuditEvent(actionType: "ChangePasswordAttempt_IncorrectOldPassword", success: false, userId: userId, performingUserId: userId, targetEntityType:"User", targetEntityId: userId.ToString(), ipAddress: ipAddress, details: "Failed: Old password incorrect or user has no password hash.");
                return (false, new List<string> { "Incorrect old password." });
            }

            var passwordPolicy = await _context.PasswordPolicies.FirstOrDefaultAsync();
            if (passwordPolicy == null)
            {
                 await _authenticationService.LogAuditEvent(actionType: "ChangePasswordAttempt_NoPolicy", success: false, userId: userId, performingUserId: userId, targetEntityType:"User", targetEntityId: userId.ToString(), ipAddress: ipAddress, details: "Failed: Password policy not configured.");
                return (false, new List<string> { "Password policy not configured. Cannot change password." });
            }

            var validationErrors = _authenticationService.ValidatePassword(request.NewPassword, passwordPolicy, user.PasswordHistory, _passwordService);
            if (validationErrors.Any())
            {
                await _authenticationService.LogAuditEvent(actionType: "ChangePasswordAttempt_PolicyViolation", success: false, userId: userId, performingUserId: userId, targetEntityType:"User", targetEntityId: userId.ToString(), ipAddress: ipAddress, details: $"Failed: New password failed policy validation: {string.Join(", ", validationErrors)}");
                return (false, validationErrors);
            }

            var oldPasswordHash = user.PasswordHash; // Keep this for history, even if it was null (though logic above would prevent this line if null)
            user.PasswordHash = _passwordService.HashPassword(request.NewPassword);
            user.PasswordLastChangedDate = DateTime.UtcNow;

            // Manage password history
            if(oldPasswordHash != null) // Only add to history if there was an old hash
            {
                user.PasswordHistory.Add(oldPasswordHash);
            }
            if (user.PasswordHistory.Count > passwordPolicy.PasswordHistoryCount && passwordPolicy.PasswordHistoryCount > 0)
            {
                user.PasswordHistory = user.PasswordHistory.Skip(user.PasswordHistory.Count - passwordPolicy.PasswordHistoryCount).ToList();
            } else if (passwordPolicy.PasswordHistoryCount == 0) {
                 user.PasswordHistory.Clear(); // If policy is 0, don't store history
            }


            await _context.SaveChangesAsync();
            await _authenticationService.LogAuditEvent(actionType: "PasswordChanged_Success", success: true, userId: userId, performingUserId: userId, targetEntityType:"User", targetEntityId: userId.ToString(), ipAddress: ipAddress, details: "Password changed successfully.");
            return (true, Enumerable.Empty<string>());
        }

        public async Task<MfaSettingsDto?> GetMfaSettingsAsync(Guid userId, string appName, string accountNameBase)
        {
            var user = await _context.P2PUsers.FindAsync(userId);
            if (user == null) return null;

            if (user.IsMfaEnabled)
            {
                return new MfaSettingsDto { IsMfaEnabled = true };
            }
            else
            {
                // User wants to set up MFA, generate new setup details
                var mfaSetupDetails = await _mfaService.GenerateMfaSetupAsync(userId, appName, $"{accountNameBase}:{user.Username}");
                if (mfaSetupDetails == null) return null; // Could not generate details

                return new MfaSettingsDto
                {
                    IsMfaEnabled = false,
                    ManualEntryKey = mfaSetupDetails.ManualEntryKey, // For display to user
                    QrCodeSetupImageUrl = mfaSetupDetails.QrCodeSetupImageUrl, // For display to user
                    // TempSecretKey is implicitly part of setupDetails and handled by EnableMfaAsync call
                };
            }
        }

        public async Task<(bool Success, IEnumerable<string> Errors)> EnableMfaAsync(Guid userId, string tempMfaSecret, string totpCode, string? ipAddress)
        {
            // Note: tempMfaSecret is the one generated during GetMfaSettingsAsync and shown to user (ManualEntryKey from GoogleAuthenticator setup)
            // It's not directly on the user record yet.
            var success = await _mfaService.EnableMfaAsync(userId, tempMfaSecret, totpCode);
            if (success)
            {
                await _authenticationService.LogAuditEvent(actionType: "MfaEnabled_Success", success: true, userId: userId, performingUserId: userId, targetEntityType:"User", targetEntityId: userId.ToString(), ipAddress: ipAddress, details: "MFA enabled by user.");
                return (true, Enumerable.Empty<string>());
            }
            await _authenticationService.LogAuditEvent(actionType: "MfaEnableAttempt_Failed", success: false, userId: userId, performingUserId: userId, targetEntityType:"User", targetEntityId: userId.ToString(), ipAddress: ipAddress, details: "Failed to enable MFA, invalid TOTP code or secret.");
            return (false, new List<string> { "Failed to enable MFA. Invalid code or secret." });
        }

        public async Task<(bool Success, IEnumerable<string> Errors)> DisableMfaAsync(Guid userId, string totpCode, string? ipAddress)
        {
            var user = await _context.P2PUsers.FindAsync(userId);
            if (user == null || !user.IsMfaEnabled)
            {
                await _authenticationService.LogAuditEvent(actionType: "MfaDisableAttempt_NotEnabledOrUserNotFound", success: false, userId: userId, performingUserId: userId, targetEntityType:"User", targetEntityId: userId.ToString(), ipAddress: ipAddress, details: "MFA not enabled or user not found.");
                return (false, new List<string> { "MFA is not enabled or user not found." });
            }

            var success = await _mfaService.DisableMfaAsync(userId, totpCode);
            if (success)
            {
                await _authenticationService.LogAuditEvent(actionType: "MfaDisabled_Success", success: true, userId: userId, performingUserId: userId, targetEntityType:"User", targetEntityId: userId.ToString(), ipAddress: ipAddress, details: "MFA disabled by user.");
                return (true, Enumerable.Empty<string>());
            }
            await _authenticationService.LogAuditEvent(actionType: "MfaDisableAttempt_Failed", success: false, userId: userId, performingUserId: userId, targetEntityType:"User", targetEntityId: userId.ToString(), ipAddress: ipAddress, details: "Failed to disable MFA, invalid TOTP code.");
            return (false, new List<string> { "Failed to disable MFA. Invalid code." });
        }
    }
}
