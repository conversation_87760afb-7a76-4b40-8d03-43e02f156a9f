using ProcureToPay.Domain.Entities; // Assuming BaseEntity<Guid> is directly in here
using System.Collections.Generic;
using System;

namespace ProcureToPay.Domain.Entities.Catalog
{
    public class ProductCategory : BaseEntity<Guid>
    {
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
        public Guid? ParentCategoryId { get; set; }
        public virtual ProductCategory? ParentCategory { get; set; }
        public virtual ICollection<ProductCategory> Subcategories { get; set; } = new List<ProductCategory>();
        public bool IsActive { get; set; } = true;
        public virtual ICollection<CatalogProduct> CatalogProducts { get; set; } = new List<CatalogProduct>();

        // Parameterless constructor for EF Core
        private ProductCategory() : base(Guid.NewGuid()) {}

        // Constructor for application code - EF Core can also use this if no parameterless is found,
        // but it's good practice to have a parameterless one for EF Core.
        // The prompt has no explicit constructor, so EF would use the parameterless one.
        // Adding one for completeness based on typical entity design.
        public ProductCategory(Guid id, string name) : base(id == Guid.Empty ? Guid.NewGuid() : id)
        {
            Name = name;
        }
    }
}
