using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProcureToPay.Domain.Entities;
using ProcureToPay.Domain.Enums;
using ProcureToPay.Domain.ValueObjects; // For Money VO

namespace ProcureToPay.Infrastructure.Persistence.Configurations
{
    /// <summary>
    /// EF Core Configuration for the PurchaseOrderLine entity.
    /// Configures mapping for the refactored entity including snapshots and Value Objects.
    /// </summary>
    public class PurchaseOrderLineConfiguration : IEntityTypeConfiguration<PurchaseOrderLine>
    {

        public void Configure(EntityTypeBuilder<PurchaseOrderLine> builder)
        {
            builder.ToTable("purchase_order_lines");

            // Assuming PurchaseOrderLine inherits from BaseEntity<Guid>
            builder.HasKey(pol => pol.Id);

            // --- Properties ---
            builder.Property(pol => pol.SkuSnapshot)
                .IsRequired()
                .HasMaxLength(100); // Match ProductDefinition.Sku or VendorProduct.VendorSku length

            builder.Property(pol => pol.DescriptionSnapshot)
                .IsRequired()
                .HasMaxLength(2000); // Match ProductDefinition.Description length

            builder.Property(pol => pol.Quantity)
                .IsRequired()
                .HasPrecision(18, 4); // Precision for decimal quantity

            // Configure UnitOfMeasureSnapshot Enum conversion
            builder.Property(pol => pol.UnitOfMeasureSnapshot)
                .IsRequired()
                .HasConversion<string>() // Store as string
                .HasMaxLength(50); // Max length for enum string representation

            builder.Property(pol => pol.Notes)
                .HasMaxLength(1000); // Max length for notes


            // --- Value Object Mapping (Money for UnitPriceSnapshot and LineTotal) ---

            // Configure UnitPriceSnapshot (Money VO)
            builder.OwnsOne(pol => pol.UnitPriceSnapshot, price =>
            {
                // Map properties of the Money VO to columns
                price.Property(m => m.Amount)
                    .HasColumnName("unit_price_amount") // Explicit column name
                    .HasPrecision(18, 4) // Consistent precision
                    .IsRequired();

                price.Property(m => m.CurrencyCode)
                    .HasColumnName("unit_price_currency_code") // Explicit column name
                    .HasMaxLength(3) // Standard ISO code length
                    .IsRequired();
            });
            // Ensure the owned entity itself is required
            builder.Navigation(pol => pol.UnitPriceSnapshot).IsRequired();

            // Configure LineTotal (Money VO)
            builder.OwnsOne(pol => pol.LineTotal, total =>
            {
                // Map properties of the Money VO to columns
                total.Property(m => m.Amount)
                    .HasColumnName("line_total_amount") // Explicit column name
                    .HasPrecision(18, 4) // Consistent precision
                    .IsRequired();

                total.Property(m => m.CurrencyCode)
                    .HasColumnName("line_total_currency_code") // Explicit column name
                    .HasMaxLength(3) // Standard ISO code length
                    .IsRequired();
            });
            // Ensure the owned entity itself is required
            builder.Navigation(pol => pol.LineTotal).IsRequired();


            // --- Relationships ---

            // Relationship to PurchaseOrder (Many Lines to One PO)
            // Defined from the PO side as well (HasMany)
            builder.HasOne(pol => pol.PurchaseOrder)
                   .WithMany(po => po.Lines) // Assumes PurchaseOrder has ICollection<PurchaseOrderLine> Lines
                   .HasForeignKey(pol => pol.PurchaseOrderId) // FK property on PurchaseOrderLine
                   .IsRequired()
                   .OnDelete(DeleteBehavior.Cascade); // Deleting PO deletes its lines

            // Relationship to VendorProduct (Many Lines to One VendorProduct)
            // Defined from the VendorProduct side as well (HasMany)
            builder.HasOne(pol => pol.VendorProduct) // Navigation property on PurchaseOrderLine
                   .WithMany(vp => vp.PurchaseOrderLines) // Inverse navigation property on VendorProduct
                   .HasForeignKey(pol => pol.VendorProductId) // Foreign key property on PurchaseOrderLine
                   .IsRequired()
                   .OnDelete(DeleteBehavior.Restrict); // Prevent deleting VendorProduct if referenced by PO lines


            // --- Indexes ---
            builder.HasIndex(pol => pol.PurchaseOrderId); // Index FK for joining performance
            builder.HasIndex(pol => pol.VendorProductId); // Index FK for joining performance


            // --- Tenant Isolation ---
            // Note: Tenant filtering should be implemented at the DbContext level
            // Example:
            // builder.HasQueryFilter(pol => pol.TenantId == currentTenantId);
            // Where currentTenantId is obtained from the DbContext or a tenant provider service

        }
    }
}

