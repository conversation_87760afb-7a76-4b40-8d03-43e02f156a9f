using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProcureToPay.Domain.Entities;
using System;
using static ProcureToPay.Infrastructure.Persistence.Configurations.TenantConfiguration; // Import DefaultTenantId

namespace ProcureToPay.Infrastructure.Persistence.Configurations
{
    /// <summary>
    /// EF Core Configuration for the Department entity targeting PostgreSQL.
    /// </summary>
    public class DepartmentConfiguration : IEntityTypeConfiguration<Department>
    {
        // Define static GUIDs for departments to ensure consistent references
        public static readonly Guid ITDepartmentId = Guid.Parse("*************-4444-4444-************");
        public static readonly Guid FinanceDepartmentId = Guid.Parse("*************-4444-4444-************");
        public static readonly Guid HRDepartmentId = Guid.Parse("*************-4444-4444-************");
        public static readonly Guid ProcurementDepartmentId = Guid.Parse("*************-4444-4444-************");
        public static readonly Guid OperationsDepartmentId = Guid.Parse("*************-4444-4444-************");
        public static readonly Guid MarketingDepartmentId = Guid.Parse("*************-4444-4444-************");

        // Fixed date for seeding to ensure consistency
        private static readonly DateTime SeedDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc);

        // Example schema, adjust if needed (e.g., "shared", "organizations")
        private const string DefaultSchema = "public";

        public void Configure(EntityTypeBuilder<Department> builder)
        {
            // --- Table Mapping ---
            builder.ToTable("departments", DefaultSchema); // Use snake_case and schema

            // --- Primary Key ---
            // Assuming Department inherits from BaseEntity<Guid>
            builder.HasKey(d => d.Id);
            builder.Property(d => d.Id).ValueGeneratedNever(); // For seeded entities with predefined IDs

            // --- Concurrency Control (PostgreSQL xmin - EF Core 7+) ---
            builder.Property<uint>("xmin")
                   .HasColumnType("xid")
                   .ValueGeneratedOnAddOrUpdate()
                   .IsConcurrencyToken();

            // --- Properties ---
            builder.Property(d => d.TenantId)
                   .IsRequired();
            builder.HasIndex(d => d.TenantId); // Index for tenant filtering

            builder.Property(d => d.Name)
                   .IsRequired()
                   .HasMaxLength(200);
            builder.HasIndex(d => d.Name); // Index for lookups/sorting

            builder.Property(d => d.Code)
                   .HasMaxLength(50); // Optional code
            // Unique index on Code within a tenant, only for non-null codes (PostgreSQL syntax)
            builder.HasIndex(d => new { d.TenantId, d.Code })
                   .IsUnique()
       .HasFilter(@"""code"" IS NOT NULL");

            builder.Property(d => d.Description)
                   .HasColumnType("text"); // Use 'text' for potentially long descriptions

            // Audit fields from BaseEntity
            builder.Property("CreatedAt").IsRequired();
            builder.Property("ModifiedAt").IsRequired(false);

            // --- Relationships ---

            // Relationship to BudgetAllocations (One Department to Many Allocations)
            builder.HasMany(d => d.BudgetAllocations)
                   .WithOne(ba => ba.Department) // Assumes BudgetAllocation has Department nav prop
                   .HasForeignKey(ba => ba.DepartmentId) // Assumes BudgetAllocation has DepartmentId FK
                   .IsRequired() // Allocation must belong to a Department
                   .OnDelete(DeleteBehavior.Restrict); // Prevent deleting Department if BudgetAllocations exist

            // --- Seeding ---
            // Seed departments using anonymous types for HasData
            builder.HasData(
                new
                {
                    Id = ITDepartmentId,
                    TenantId = DefaultTenantId,
                    Name = "Information Technology",
                    Code = "IT",
                    Description = "Handles IT infrastructure, software development, and technical support.",
                    CreatedAt = SeedDate,
                    ModifiedAt = (DateTime?)null
                },
                new
                {
                    Id = FinanceDepartmentId,
                    TenantId = DefaultTenantId,
                    Name = "Finance",
                    Code = "FIN",
                    Description = "Manages financial operations, budgeting, and accounting.",
                    CreatedAt = SeedDate,
                    ModifiedAt = (DateTime?)null
                },
                new
                {
                    Id = HRDepartmentId,
                    TenantId = DefaultTenantId,
                    Name = "Human Resources",
                    Code = "HR",
                    Description = "Handles employee recruitment, training, and personnel management.",
                    CreatedAt = SeedDate,
                    ModifiedAt = (DateTime?)null
                },
                new
                {
                    Id = ProcurementDepartmentId,
                    TenantId = DefaultTenantId,
                    Name = "Procurement",
                    Code = "PROC",
                    Description = "Manages purchasing, vendor relationships, and supply chain operations.",
                    CreatedAt = SeedDate,
                    ModifiedAt = (DateTime?)null
                },
                new
                {
                    Id = OperationsDepartmentId,
                    TenantId = DefaultTenantId,
                    Name = "Operations",
                    Code = "OPS",
                    Description = "Oversees day-to-day operational activities and logistics.",
                    CreatedAt = SeedDate,
                    ModifiedAt = (DateTime?)null
                },
                new
                {
                    Id = MarketingDepartmentId,
                    TenantId = DefaultTenantId,
                    Name = "Marketing",
                    Code = "MKT",
                    Description = "Handles advertising, brand management, and market research.",
                    CreatedAt = SeedDate,
                    ModifiedAt = (DateTime?)null
                }
            );
        }
    }
}

