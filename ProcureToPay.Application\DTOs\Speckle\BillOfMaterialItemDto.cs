using System;
using System.Collections.Generic;

namespace ProcureToPay.Application.DTOs.Speckle
{
    public class BillOfMaterialItemDto
    {
        /// <summary>
        /// A concatenated key representing the group (e.g., "Wall_Concrete_200mm").
        /// This is generated by the service based on GroupByProperties.
        /// </summary>
        public string GroupKey { get; set; } = string.Empty;

        /// <summary>
        /// Dictionary holding the values for properties defined in
        /// BOMGenerationRequestDto.GroupByProperties and BOMGenerationRequestDto.IncludeParametersInBom.
        /// Keys are property names, values are the corresponding string values.
        /// </summary>
        public Dictionary<string, string?> Properties { get; set; } = new Dictionary<string, string?>();

        /// <summary>
        /// The number of Speckle objects that fall into this group.
        /// </summary>
        public int Quantity { get; set; }

        /// <summary>
        /// List of SpeckleObjectIds that belong to this BOM item group.
        /// </summary>
        public List<string> SpeckleObjectIds { get; set; } = new List<string>();
    }
}
