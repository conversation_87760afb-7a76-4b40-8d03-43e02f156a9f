﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<Nullable>enable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
		<EFCore_MigrationsOutputDirectory>Persistence\Migrations</EFCore_MigrationsOutputDirectory>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.3.0" />
		<PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="9.0.5" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.5">
		  <PrivateAssets>all</PrivateAssets>
		  <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.5">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.Extensions.Configuration.EnvironmentVariables" Version="9.0.5" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.5" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="9.0.5" />
		<PackageReference Include="Microsoft.Extensions.Configuration.UserSecrets" Version="9.0.5" />
		<PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.4" />
		<PackageReference Include="Polly" Version="8.5.2" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\ProcureToPay.Domain\ProcureToPay.Domain.csproj" />
	</ItemGroup>

	<ItemGroup>
		<Content Include="appsettings.json">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
			<ExcludeFromSingleFile>true</ExcludeFromSingleFile>
			<CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
		</Content>
	</ItemGroup>

	<ItemGroup>
	  <Folder Include="Persistence\Migrations\" />
	</ItemGroup>

</Project>
