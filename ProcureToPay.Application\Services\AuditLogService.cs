using Microsoft.EntityFrameworkCore;
using ProcureToPay.Application.DTOs.Audit;
using ProcureToPay.Domain.Entities;
using ProcureToPay.Infrastructure.Persistence;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace ProcureToPay.Application.Services
{
    public interface IAuditLogService
    {
        Task<PaginatedAuditLogDto> GetAuditLogsAsync(AuditLogQueryParameters queryParams);
    }

    public class AuditLogService : IAuditLogService
    {
        private readonly ApplicationDbContext _context;

        public AuditLogService(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<PaginatedAuditLogDto> GetAuditLogsAsync(AuditLogQueryParameters queryParams)
        {
            var query = _context.AuditLogs.AsNoTracking();

            if (queryParams.UserId.HasValue)
                query = query.Where(al => al.UserId == queryParams.UserId.Value);
            if (queryParams.PerformingUserId.HasValue)
                query = query.Where(al => al.PerformingUserId == queryParams.PerformingUserId.Value);
            if (!string.IsNullOrWhiteSpace(queryParams.ActionType))
                query = query.Where(al => al.ActionType.Contains(queryParams.ActionType)); // Contains for partial match
            if (queryParams.DateFrom.HasValue)
                query = query.Where(al => al.Timestamp >= queryParams.DateFrom.Value);
            if (queryParams.DateTo.HasValue)
                query = query.Where(al => al.Timestamp <= queryParams.DateTo.Value);
            if (!string.IsNullOrWhiteSpace(queryParams.TargetEntityType))
                query = query.Where(al => al.TargetEntityType == queryParams.TargetEntityType);
            if (!string.IsNullOrWhiteSpace(queryParams.TargetEntityId))
                query = query.Where(al => al.TargetEntityId == queryParams.TargetEntityId);
            if (queryParams.Success.HasValue)
                query = query.Where(al => al.Success == queryParams.Success.Value);

            query = query.OrderByDescending(al => al.Timestamp); // Most recent first

            var totalCount = await query.CountAsync();
            var items = await query
                .Skip((queryParams.PageNumber - 1) * queryParams.PageSize)
                .Take(queryParams.PageSize)
                .Select(al => new AuditLogDto // Manual projection to avoid fetching usernames here for simplicity
                {                                 // A more advanced version would join with Users table for usernames
                    Id = al.Id,
                    Timestamp = al.Timestamp,
                    UserId = al.UserId,
                    PerformingUserId = al.PerformingUserId,
                    ActionType = al.ActionType,
                    Success = al.Success,
                    TargetEntityType = al.TargetEntityType,
                    TargetEntityId = al.TargetEntityId,
                    IpAddress = al.IpAddress,
                    Details = al.Details
                })
                .ToListAsync();

            // Populate usernames if needed (example, can be more optimized)
            var userIds = items.Where(i => i.UserId.HasValue).Select(i => i.UserId!.Value)
                .Union(items.Where(i => i.PerformingUserId.HasValue).Select(i => i.PerformingUserId!.Value))
                .Distinct().ToList();

            if (userIds.Any())
            {
                var users = await _context.P2PUsers
                    .Where(u => userIds.Contains(u.Id))
                    .ToDictionaryAsync(u => u.Id, u => u.Username);

                foreach (var item in items)
                {
                    if (item.UserId.HasValue && users.TryGetValue(item.UserId.Value, out var username))
                        item.Username = username;
                    if (item.PerformingUserId.HasValue && users.TryGetValue(item.PerformingUserId.Value, out var performingUsername))
                        item.PerformingUsername = performingUsername;
                }
            }


            return new PaginatedAuditLogDto
            {
                Items = items,
                PageNumber = queryParams.PageNumber,
                PageSize = queryParams.PageSize,
                TotalCount = totalCount,
                TotalPages = (int)Math.Ceiling(totalCount / (double)queryParams.PageSize)
            };
        }
    }
}
