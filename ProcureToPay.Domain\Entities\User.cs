using ProcureToPay.Domain.Enums;
using System;
using System.Collections.Generic;

namespace ProcureToPay.Domain.Entities
{
    public class User : BaseEntity<Guid>
    {
        public string Username { get; set; }
        public string? PasswordHash { get; set; }
        public string Email { get; set; }
        public UserRole UserRole { get; set; }
        public bool IsMfaEnabled { get; set; }
        public string? MfaSecretKey { get; set; }
        public string? SsoProvider { get; set; }
        public string? SsoUserId { get; set; }
        public DateTime? LastLoginDate { get; set; }
        public int FailedLoginAttempts { get; set; }
        public DateTime? LockoutEndDateUtc { get; set; }
        public DateTime PasswordLastChangedDate { get; set; }
        public List<string> PasswordHistory { get; set; } = new List<string>();

        // Profile information
        public string? FullName { get; set; }
        public string? PhoneNumber { get; set; }
        public string? PreferredLanguage { get; set; } // e.g., "en-US", "fr-CA"
        public string? NotificationPreferences { get; set; } // Could be JSON serialized object
        public bool IsEmailVerified { get; set; }

        // Fields for User Administration (FR-USR-004)
        public Guid? OrganizationId { get; set; } // Nullable for MarketplaceAdmin or users not tied to a specific org
        public bool IsActive { get; set; } = true; // Default to active
        public string? PasswordResetTokenHash { get; set; }
        public DateTime? PasswordResetTokenExpiryUtc { get; set; }


        public User(Guid id, string username, string email, UserRole userRole) : base(id == Guid.Empty ? Guid.NewGuid() : id)
        {
            Username = username ?? throw new ArgumentNullException(nameof(username));
            Email = email ?? throw new ArgumentNullException(nameof(email));
            IsEmailVerified = false; // Default to false on creation
            IsActive = true; // Default to active on creation
            UserRole = userRole;
            PasswordLastChangedDate = DateTime.UtcNow;
        }

        // Parameterless constructor for EF Core
        private User() : base(Guid.NewGuid())
        {
            // Initialize collections and required properties for EF Core, if not nullable
            Username = string.Empty;
            // PasswordHash is now nullable, no explicit init to empty needed here unless desired
            Email = string.Empty;
            PasswordHistory = new List<string>();
            IsEmailVerified = false;
            IsActive = true;
        }
    }
}
