using System;
using System.ComponentModel.DataAnnotations;

namespace ProcureToPay.Application.DTOs.Catalog
{
    public class CreateCategoryRequest
    {
        [Required]
        [StringLength(100, MinimumLength = 3)]
        public string Name { get; set; } = string.Empty;

        [StringLength(500)]
        public string? Description { get; set; }

        public Guid? ParentCategoryId { get; set; } // Optional, for creating subcategories
    }
}
