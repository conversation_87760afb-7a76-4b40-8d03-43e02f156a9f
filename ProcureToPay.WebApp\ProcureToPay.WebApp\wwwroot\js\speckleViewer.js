// wwwroot/js/speckleViewer.js
// Conceptual Speckle Viewer Integration

/*
// Full ES6 Module example (requires a bundler or type="module" in script tag and modern browser)
// Ensure you have the @speckle/viewer package installed via npm or yarn if using this approach.
import { Viewer, DefaultViewerParams, SpeckleLoader } from '@speckle/viewer';

async function initViewer(containerId, serverUrl, streamId, objectIdOrCommitId, token) {
    const container = document.getElementById(containerId);
    if (!container) {
        console.error(`Viewer container element #${containerId} not found.`);
        container.innerHTML = `<p style="color:red;">Error: Container element #${containerId} not found.</p>`;
        return;
    }
    container.innerHTML = ""; // Clear any placeholder text

    console.log("Initializing Speckle Viewer with params:", { containerId, serverUrl, streamId, objectIdOrCommitId, token });

    try {
        const viewer = new Viewer(container, DefaultViewerParams);
        await viewer.init();

        // Add a loader for the object/commit
        const loader = new SpeckleLoader(viewer.getWorldTree(), objectIdOrCommitId, token, serverUrl, streamId);
        // viewer.loadObject(loader, true); // The 'true' might be for 'fitToView'

        // Simpler loadObject call if using a direct URL (less common for private streams with token)
        // The resourceURL should be constructed based on Speckle server conventions.
        // For commits: `${serverUrl}/streams/${streamId}/commits/${objectIdOrCommitId}`
        // For specific objects: `${serverUrl}/streams/${streamId}/objects/${objectIdOrCommitId}`
        // The viewer or loader needs to be configured with the token for auth.
        // This often involves setting up a Speckle account manager or directly providing the token to the loader/client.

        // For @speckle/viewer, authentication is typically handled by providing the token to the SpeckleLoader
        // or by having the user log in via Speckle's auth methods if a full Speckle client is used.

        // Construct the object URL
        // Note: objectIdOrCommitId could be a commit ID or a specific object ID.
        // If it's 'latest', you might need to resolve that to a specific commit ID first,
        // or the viewer/loader might support 'latest' directly (less common for specific object loaders).
        // For simplicity, assuming objectIdOrCommitId is a specific commit or object ID.
        let resourceUrl = `${serverUrl}/streams/${streamId}/objects/${objectIdOrCommitId}`;
        if (objectIdOrCommitId.length < 15 && objectIdOrCommitId !== "latest") { // Heuristic: commit IDs are usually longer hashes than 'main' or 'latest' branch names.
             // This logic might need refinement based on what objectIdOrCommitId truly represents (branch, commit, specific object)
             // For now, assume if not a typical hash length, it might be a commit on main or 'latest' needs resolving.
             // The example uses /objects/ which expects a specific object hash.
             // For loading a whole commit, it's often `${serverUrl}/streams/${streamId}/commits/${commitId}`
             // Let's assume for this example `objectIdOrCommitId` is a specific object hash from the commit root.
        }


        // The SpeckleLoader approach is more robust for auth
        await viewer.loadObject(loader, true);

        console.log(`Speckle Viewer loaded: ${resourceUrl}`);

    } catch (error) {
        console.error('Error initializing or loading Speckle Viewer:', error);
        container.innerHTML = `<p style="color:red;">Error initializing viewer: ${error.message}. Check console.</p>`;
    }
}

// Expose the init function to the global window object so Blazor can call it
window.speckleViewer = {
    init: initViewer
};

*/

// Simplified placeholder for the subtask, as full JS viewer setup is complex and requires live testing.
// This version will just display the parameters received.
window.speckleViewer = {
    init: function(containerId, serverUrl, streamId, objectIdOrCommitId, token) {
        const container = document.getElementById(containerId);
        if (!container) {
            console.error("Viewer container not found:", containerId);
            // Try to create a message in the body if container is not found
            const bodyMessage = document.createElement('p');
            bodyMessage.style.color = 'red';
            bodyMessage.innerText = `Error: Viewer container element #${containerId} not found.`;
            document.body.appendChild(bodyMessage);
            return;
        }

        // Clear previous content and display info
        container.innerHTML = `
            <div style="padding: 10px; text-align: left;">
                <h4>Conceptual Speckle Viewer Initialized</h4>
                <p><strong>Container ID:</strong> ${containerId}</p>
                <p><strong>Server URL:</strong> ${serverUrl}</p>
                <p><strong>Stream ID:</strong> ${streamId}</p>
                <p><strong>Object/Commit ID:</strong> ${objectIdOrCommitId}</p>
                <p><strong>Token:</strong> ${token ? 'Provided (see console)' : 'Not Provided'}</p>
                <hr>
                <p><em>In a real application, the Speckle 3D Viewer would be rendered here, loading the specified model.
                   This placeholder confirms that the Blazor component is successfully calling this JavaScript function
                   and passing the correct parameters. The actual viewer integration requires the
                   <code>@speckle/viewer</code> library and more detailed setup.</em></p>
                <p>Check the browser console for the passed token (if any) and other details.</p>
            </div>`;

        console.log("Speckle Viewer Initialized (Conceptual) with:",
            { containerId, serverUrl, streamId, objectIdOrCommitId, token: token ? "Token Provided (details follow)" : "Token Not Provided" }
        );
        if (token) {
            // For security reasons, avoid logging the full token in a real production app's console
            // unless for specific debugging purposes by authorized personnel.
            console.log("Token Value (first 10 chars for verification):", token.substring(0, 10) + "...");
        }
    },

    applyObjectStatuses: function(objectStatuses) {
        if (!objectStatuses) {
            console.warn("applyObjectStatuses called with null or undefined data.");
            return;
        }
        console.log("Received object statuses for visual override:", objectStatuses);
        // In a real implementation, this function would iterate through objectStatuses:
        // 1. For each status DTO, get the SpeckleObjectId.
        // 2. Use the Speckle Viewer API to find the object in the scene by its ID.
        //    (e.g., viewer.getWorldTree().findFirst((node) => node.model.id === speckleObjectId))
        // 3. Based on statusDto.VisualOverrideKey, determine a color or material.
        //    (e.g., if "LINKED_PO", color = blue; if "NOT_LINKED", color = default/grey)
        // 4. Apply the visual override using viewer API functions like:
        //    viewer.setUserObjectMaterial(objectNode, material); or viewer.setColor(objectNode, color);
        //    viewer.ensureVisibility(objectNode); // If object might be hidden
        // This often involves creating material instances (e.g., new MeshStandardMaterial({color: 0xff0000}))
        // and managing them. Resetting materials is also important (viewer.resetMaterial(objectNode)).

        const container = document.getElementById('speckle-viewer-container'); // Assuming this is the main container
        if (container) {
            let statusSummaryHtml = '<h5>Object Statuses (Conceptual):</h5><ul>';
            if (objectStatuses.length === 0) {
                statusSummaryHtml += '<li>No object statuses received.</li>';
            } else {
                objectStatuses.slice(0, 5).forEach(status => { // Display first 5 for brevity
                    statusSummaryHtml += `<li>ID: ${status.speckleObjectId} - VisualKey: ${status.visualOverrideKey} (Linked: ${status.hasActiveLink})</li>`;
                });
                if (objectStatuses.length > 5) {
                    statusSummaryHtml += `<li>...and ${objectStatuses.length - 5} more.</li>`;
                }
            }
            statusSummaryHtml += '</ul><p><em>Actual viewer would color-code objects based on these statuses.</em></p>';

            // Find or create a div to show this summary
            let summaryDiv = document.getElementById('speckle-status-summary');
            if (!summaryDiv) {
                summaryDiv = document.createElement('div');
                summaryDiv.id = 'speckle-status-summary';
                summaryDiv.style.position = 'absolute';
                summaryDiv.style.top = '10px';
                summaryDiv.style.left = '10px';
                summaryDiv.style.backgroundColor = 'rgba(255,255,255,0.8)'; // Corrected typo here
                summaryDiv.style.padding = '10px';
                summaryDiv.style.border = '1px solid #ccc';
                summaryDiv.style.maxWidth = '300px';
                container.appendChild(summaryDiv);
            }
            summaryDiv.innerHTML = statusSummaryHtml;
        }
        console.log("Conceptual: Applied visual overrides based on statuses.");
    }
};
