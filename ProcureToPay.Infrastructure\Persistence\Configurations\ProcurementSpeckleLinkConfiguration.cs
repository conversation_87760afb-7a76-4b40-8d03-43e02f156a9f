using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProcureToPay.Domain.Entities;

namespace ProcureToPay.Infrastructure.Persistence.Configurations
{
    public class ProcurementSpeckleLinkConfiguration : IEntityTypeConfiguration<ProcurementSpeckleLink>
    {
        public void Configure(EntityTypeBuilder<ProcurementSpeckleLink> builder)
        {
            builder.ToTable("ProcurementSpeckleLinks");

            // Assuming BaseEntity<Guid> configures Id as PK.

            builder.Property(psl => psl.ProcurementItemType)
                .IsRequired()
                .HasMaxLength(100); // E.g., "PurchaseOrderLine", "RequisitionLine"

            builder.Property(psl => psl.ProcurementItemId)
                .IsRequired();

            builder.Property(psl => psl.LinkedAt)
                .IsRequired();

            // Configure the foreign key relationship to SpeckleObjectMetadata
            builder.HasOne(psl => psl.SpeckleObjectMetadata)
                   .WithMany() // Assuming SpeckleObjectMetadata does not have a direct navigation collection for these links.
                   .HasForeignKey(psl => psl.SpeckleObjectMetadataId)
                   .IsRequired()
                   .OnDelete(DeleteBehavior.Cascade); // Deleting metadata also deletes these specific links.

            // Configure the foreign key relationship to User (LinkedByUser)
            // Note: The User entity might be named ApplicationUser in Identity context,
            // but the domain entity is ProcureToPay.Domain.Entities.User.
            builder.HasOne(psl => psl.LinkedByUser)
                   .WithMany() // Assuming User does not have a direct navigation collection for these links.
                   .HasForeignKey(psl => psl.LinkedByUserId)
                   .IsRequired()
                   .OnDelete(DeleteBehavior.Restrict); // Prevent deletion of user if they have links, or set to NoAction/SetNull depending on policy.

            // Indexes
            // Index for finding links by procurement item (type and ID)
            builder.HasIndex(psl => new { psl.ProcurementItemType, psl.ProcurementItemId });

            // Index for finding links by SpeckleObjectMetadataId
            builder.HasIndex(psl => psl.SpeckleObjectMetadataId);

            // Index for finding links by User
            builder.HasIndex(psl => psl.LinkedByUserId);

            // If ProcurementSpeckleLink implements ITenantEntity, ensure TenantId is configured.
            // (Assuming BaseEntity<Guid> handles TenantId if it's an ITenantEntity)
        }
    }
}
