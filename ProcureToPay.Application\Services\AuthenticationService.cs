using Microsoft.EntityFrameworkCore;
using ProcureToPay.Application.DTOs.Auth;
using ProcureToPay.Domain.Entities;
using ProcureToPay.Domain.Enums; // Required for UserRole
using ProcureToPay.Infrastructure.Persistence; // Required for ApplicationDbContext
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration; // For IConfiguration
using Microsoft.IdentityModel.Tokens; // For SymmetricSecurityKey, TokenDescriptor, etc.
using System.IdentityModel.Tokens.Jwt; // For JwtSecurityTokenHandler
using System.Security.Claims; // For Claims
using System.Text; // For Encoding

namespace ProcureToPay.Application.Services
{
    public interface IAuthenticationService
    {
        Task<(bool Success, User? User, IEnumerable<string> Errors)> RegisterUserAsync(RegisterUserRequest request, string? ipAddress);
        Task<LoginUserResponse> LoginUserAsync(LoginUserRequest request, string? ipAddress);
        List<string> ValidatePassword(string password, PasswordPolicy policy, List<string> passwordHistory, IPasswordService passwordService); // Made public
        Task LogAuditEvent(
            string actionType,
            bool success,
            Guid? userId = null,
            Guid? performingUserId = null,
            string? targetEntityType = null,
            string? targetEntityId = null,
            string? ipAddress = null,
            string? details = null);
    }

    public class AuthenticationService : IAuthenticationService
    {
        private readonly ApplicationDbContext _context;
        private readonly IPasswordService _passwordService;
        private readonly IMfaService _mfaService;
        private readonly IConfiguration _configuration; // Added for JWT settings

        public AuthenticationService(
            ApplicationDbContext context,
            IPasswordService passwordService,
            IMfaService mfaService,
            IConfiguration configuration) // Added IConfiguration
        {
            _context = context;
            _passwordService = passwordService;
            _mfaService = mfaService;
            _configuration = configuration; // Added
        }

        // LoginUserResponse is now in its own file: ProcureToPay.Application/DTOs/Auth/LoginUserResponse.cs

        public async Task<(bool Success, User? User, IEnumerable<string> Errors)> RegisterUserAsync(RegisterUserRequest request, string? ipAddress)
        {
            var errors = new List<string>();

            // 1. Check if user already exists
            if (await _context.P2PUsers.AnyAsync(u => u.Username == request.Username))
            {
                errors.Add("Username already exists.");
            }
            if (await _context.P2PUsers.AnyAsync(u => u.Email == request.Email))
            {
                errors.Add("Email already exists.");
            }

            if (errors.Any())
            {
                await LogAuditEvent(actionType: "UserRegistrationAttempt", success: false, ipAddress: ipAddress, details: $"Failed registration for {request.Username}: {string.Join(", ", errors)}");
                return (false, null, errors);
            }

            // 2. Retrieve PasswordPolicy (fetch the first one, or a specific one based on TenantId if applicable)
            //    For now, we assume a single global policy or take the first available.
            //    A robust implementation would involve getting policy for the current tenant.
            var passwordPolicy = await _context.PasswordPolicies.FirstOrDefaultAsync();
            if (passwordPolicy == null)
            {
                // If no policy is found, we could use hardcoded defaults or prevent registration.
                // For now, let's assume a default policy should have been seeded.
                errors.Add("Password policy not configured.");
                await LogAuditEvent(actionType: "UserRegistrationAttempt", success: false, ipAddress: ipAddress, details: "Password policy not configured.");
                return (false, null, errors);
            }

            // 3. Validate password against the policy
            // For registration, password history is not checked against the user's own history (as they don't have one yet)
            // but it's good practice to ensure the ValidatePassword method can handle it for password changes later.
            var passwordValidationErrors = ValidatePassword(request.Password, passwordPolicy, new List<string>(), _passwordService);
            if (passwordValidationErrors.Any())
            {
                errors.AddRange(passwordValidationErrors);
                await LogAuditEvent(actionType: "UserRegistrationAttempt", success: false, ipAddress: ipAddress, details: $"Failed registration for {request.Username} due to password policy: {string.Join(", ", errors)}");
                return (false, null, errors);
            }

            // 4. Hash password
            var passwordHash = _passwordService.HashPassword(request.Password);

            // 5. Create User entity
            //    Default role for self-registration might be 'Buyer' or 'Seller'
            //    Admin roles should be assigned through a separate process.
            var user = new User(Guid.NewGuid(), request.Username, request.Email, UserRole.Buyer) // Default to Buyer for now
            {
                PasswordHash = passwordHash,
                PasswordLastChangedDate = DateTime.UtcNow,
                // PasswordHistory can be updated here if the policy requires immediate history tracking
            };
            user.PasswordHistory.Add(passwordHash); // Add initial hash to history

            // 6. Save user to database
            _context.P2PUsers.Add(user);
            await _context.SaveChangesAsync();

            // 7. Log audit event
            await LogAuditEvent(actionType: "UserRegistered", success: true, userId: user.Id, performingUserId: user.Id, targetEntityType: "User", targetEntityId: user.Id.ToString(), ipAddress: ipAddress, details: $"User {user.Username} registered successfully.");

            return (true, user, Enumerable.Empty<string>());
        }

        public List<string> ValidatePassword(string password, PasswordPolicy policy, List<string> passwordHistory, IPasswordService passwordService) // Made public
        {
            var errors = new List<string>();

            if (string.IsNullOrEmpty(password) || password.Length < policy.MinLength)
                errors.Add($"Password must be at least {policy.MinLength} characters long.");
            if (policy.RequireUppercase && !Regex.IsMatch(password, "[A-Z]"))
                errors.Add("Password must contain at least one uppercase letter.");
            if (policy.RequireLowercase && !Regex.IsMatch(password, "[a-z]"))
                errors.Add("Password must contain at least one lowercase letter.");
            if (policy.RequireDigit && !Regex.IsMatch(password, "[0-9]"))
                errors.Add("Password must contain at least one digit.");
            if (policy.RequireSpecialCharacter && !Regex.IsMatch(password, "[^a-zA-Z0-9]")) // Basic special char check
                errors.Add("Password must contain at least one special character.");

            // Password History Check
            if (policy.PasswordHistoryCount > 0 && passwordHistory.Any())
            {
                // Only check history if the policy requires it and history exists
                // We need to check the new plain password against previous *hashed* passwords.
                // So, we hash the new password and compare, or verify the new plain password against each old hash.
                // The latter is usually simpler: verify(new_plain_password, old_hashed_password)
                var recentPasswords = passwordHistory.TakeLast(policy.PasswordHistoryCount);
                foreach (var oldHashedPassword in recentPasswords)
                {
                    if (passwordService.VerifyPassword(password, oldHashedPassword))
                    {
                        errors.Add($"Cannot reuse one of the last {policy.PasswordHistoryCount} passwords.");
                        break;
                    }
                }
            }
            return errors;
        }

        // Made public to be available to UserProfileService, also added to interface
        public async Task LogAuditEvent(
            string actionType,
            bool success,
            Guid? userId = null,
            Guid? performingUserId = null,
            string? targetEntityType = null,
            string? targetEntityId = null,
            string? ipAddress = null,
            string? details = null)
        {
            var auditLog = new AuditLog(actionType, success, userId, performingUserId, targetEntityType, targetEntityId, ipAddress, details);
            _context.AuditLogs.Add(auditLog);
            try
            {
                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                // Log this failure to a more persistent, out-of-band logger (e.g., Serilog, NLog to console/file)
                // This is critical because if audit logging fails, we need to know.
                Console.WriteLine($"Failed to save audit log: {ex.Message}");
                // Depending on policy, you might re-throw or handle. For now, we consume it to not fail the primary operation.
            }
        } // <<<< Added closing brace for LogAuditEvent method

        public async Task<LoginUserResponse> LoginUserAsync(LoginUserRequest request, string? ipAddress)
        {
            var user = await _context.P2PUsers.FirstOrDefaultAsync(u => u.Username == request.Username);

            if (user == null)
            {
                await LogAuditEvent(actionType: "LoginAttempt", success: false, ipAddress: ipAddress, details: $"Login failed for username: {request.Username}. Reason: User not found.");
                return new LoginUserResponse { Success = false, Message = "Invalid username or password." };
            }

            // Check if account is locked
            if (user.LockoutEndDateUtc.HasValue && user.LockoutEndDateUtc.Value > DateTime.UtcNow)
            {
                await LogAuditEvent(actionType: "LoginAttempt", success: false, userId: user.Id, performingUserId: user.Id, targetEntityType: "User", targetEntityId: user.Id.ToString(), ipAddress: ipAddress, details: $"Login failed for user {user.Username}. Reason: Account locked until {user.LockoutEndDateUtc.Value}.");
                return new LoginUserResponse { Success = false, Message = $"Account locked. Try again later." };
            }

            // Verify password
            if (user.PasswordHash == null || !_passwordService.VerifyPassword(request.Password, user.PasswordHash))
            {
                user.FailedLoginAttempts++;
                var passwordPolicy = await _context.PasswordPolicies.FirstOrDefaultAsync(); // Assuming single global policy for now

                if (passwordPolicy != null && user.FailedLoginAttempts >= passwordPolicy.MaxFailedLoginAttempts)
                {
                    user.LockoutEndDateUtc = DateTime.UtcNow.AddMinutes(passwordPolicy.AccountLockoutDurationMinutes);
                    await LogAuditEvent(actionType: "AccountLocked", success: false, userId: user.Id, performingUserId: user.Id, targetEntityType: "User", targetEntityId: user.Id.ToString(), ipAddress: ipAddress, details: $"User {user.Username} account locked due to too many failed login attempts.");
                }

                await _context.SaveChangesAsync();
                await LogAuditEvent(actionType: "LoginAttempt", success: false, userId: user.Id, performingUserId: user.Id, targetEntityType: "User", targetEntityId: user.Id.ToString(), ipAddress: ipAddress, details: $"Login failed for user {user.Username}. Reason: Invalid password.");
                return new LoginUserResponse { Success = false, Message = "Invalid username or password." };
            }

            // Password verification successful
            user.FailedLoginAttempts = 0;
            user.LockoutEndDateUtc = null; // Clear any previous lockout
            user.LastLoginDate = DateTime.UtcNow;

            // Placeholder for Password Expiration Check
            bool requiresPasswordChange = false;
            var currentPolicy = await _context.PasswordPolicies.FirstOrDefaultAsync(); // Assuming single global policy
            if (currentPolicy != null && currentPolicy.PasswordExpirationDays > 0)
            {
                if ((DateTime.UtcNow - user.PasswordLastChangedDate).TotalDays > currentPolicy.PasswordExpirationDays)
                {
                    requiresPasswordChange = true;
                    // Actual enforcement (forcing change) will be in FR-USR-002
                }
            }

            // Placeholder for MFA Check
            if (user.IsMfaEnabled)
            {
                if (string.IsNullOrWhiteSpace(request.MfaCode))
                {
                    // MFA code is required but not provided.
                    // This response should prompt the user for MFA code.
                    // For now, we'll just flag it. Actual MFA validation is next.
                    await LogAuditEvent(actionType: "LoginAttempt_MfaRequired", success: false, userId: user.Id, performingUserId: user.Id, targetEntityType: "User", targetEntityId: user.Id.ToString(), ipAddress: ipAddress, details: $"Login attempt for user {user.Username}. Reason: MFA code required but not provided.");
                    // In a real scenario, you might return a specific status/message indicating MFA is needed.
                    // For this step, we'll treat it as pending MFA.
                    return new LoginUserResponse { Success = false, Message = "MFA code required.", UserId = user.Id.ToString(), Username = user.Username };
                }
                else
                {
                    // Validate MFA code
                    bool isValidMfa = await _mfaService.ValidateMfaCodeAsync(user.Id, request.MfaCode);
                    if (!isValidMfa)
                    {
                        // Failed MFA validation is critical, but typically doesn't increment the same FailedLoginAttempts counter as password
                        // as that could lock out users too easily if they mistype an MFA code.
                        // However, repeated MFA failures should also be logged and potentially rate-limited or alerted.
                        await LogAuditEvent(actionType: "MfaValidationAttempt", success: false, userId: user.Id, performingUserId: user.Id, targetEntityType: "User", targetEntityId: user.Id.ToString(), ipAddress: ipAddress, details: $"MFA validation failed for user {user.Username}.");
                        // Do not increment FailedLoginAttempts here or lock the account for MFA failure in this basic setup.
                        // await _context.SaveChangesAsync(); // No state change on user object for failed MFA to save.
                        return new LoginUserResponse { Success = false, Message = "Invalid MFA code." };
                    }
                    // MFA code is valid
                    await LogAuditEvent(actionType: "MfaValidationAttempt", success: true, userId: user.Id, performingUserId: user.Id, targetEntityType: "User", targetEntityId: user.Id.ToString(), ipAddress: ipAddress, details: $"MFA validation successful for user {user.Username}.");
                }
            }

            // Mandatory MFA check for Admins
            bool requiresMfaSetupForAdmin = false;
            if (user.UserRole == UserRole.BuyerAdmin || user.UserRole == UserRole.SellerAdmin || user.UserRole == UserRole.MarketplaceAdmin)
            {
                if (!user.IsMfaEnabled)
                {
                    requiresMfaSetupForAdmin = true;
                }
            }

            await _context.SaveChangesAsync();
            await LogAuditEvent(actionType: "LoginSuccessful", success: true, userId: user.Id, performingUserId: user.Id, targetEntityType: "User", targetEntityId: user.Id.ToString(), ipAddress: ipAddress, details: $"User {user.Username} logged in successfully.");

            return new LoginUserResponse
            {
                Success = true,
                Message = "Login successful.",
                UserId = user.Id.ToString(),
                Username = user.Username,
                RequiresMfaSetup = requiresMfaSetupForAdmin,
                RequiresPasswordChange = requiresPasswordChange,
                Token = GenerateJwtToken(user) // Generate and include token
            };
        }

        private string GenerateJwtToken(User user)
        {
            // Using hardcoded values as workaround for appsettings.json tool failure
            var secretKey = _configuration["JwtSettings:SecretKey"] ?? "THIS_IS_A_SUPER_SECRET_KEY_REPLACE_IT_LATER_WITH_ENV_VAR_OR_KMS_0123456789ABCDEF_MIN_32_CHARS_LONG";
            var issuer = _configuration["JwtSettings:Issuer"] ?? "ProcureToPay.Auth";
            var audience = _configuration["JwtSettings:Audience"] ?? "ProcureToPay.Users";
            var expiryMinutes = _configuration.GetValue<int?>("JwtSettings:TokenExpiryMinutes") ?? 60;


            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.ASCII.GetBytes(secretKey);

            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.NameIdentifier, user.Id.ToString()),
                new Claim(ClaimTypes.Name, user.Username),
                new Claim(ClaimTypes.Email, user.Email),
                new Claim(ClaimTypes.Role, user.UserRole.ToString())
                // Add other claims as needed, e.g., OrganizationId
            };
            if(user.OrganizationId.HasValue)
            {
                claims.Add(new Claim("OrganizationId", user.OrganizationId.Value.ToString()));
            }


            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(claims),
                Expires = DateTime.UtcNow.AddMinutes(expiryMinutes),
                Issuer = issuer,
                Audience = audience,
                SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature)
            };
            var token = tokenHandler.CreateToken(tokenDescriptor);
            return tokenHandler.WriteToken(token);
        }
        // Removed one extra closing brace that was here after fixing the above.
    }
}
