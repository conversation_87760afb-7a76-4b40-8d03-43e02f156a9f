﻿// <auto-generated />
using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using ProcureToPay.Domain.ValueObjects;
using ProcureToPay.Infrastructure.Persistence;

#nullable disable

namespace ProcureToPay.Infrastructure.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    partial class ApplicationDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.5")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRole", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("NormalizedName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedName")
                        .IsUnique()
                        .HasDatabaseName("RoleNameIndex");

                    b.ToTable("AspNetRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("text");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("text");

                    b.Property<string>("RoleId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetRoleClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("text");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.Property<string>("LoginProvider")
                        .HasColumnType("text");

                    b.Property<string>("ProviderKey")
                        .HasColumnType("text");

                    b.Property<string>("ProviderDisplayName")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("LoginProvider", "ProviderKey");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserLogins", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.Property<string>("RoleId")
                        .HasColumnType("text");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetUserRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.Property<string>("LoginProvider")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<string>("Value")
                        .HasColumnType("text");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("AspNetUserTokens", (string)null);
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.AuditLog", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("ActionType")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Details")
                        .HasColumnType("text");

                    b.Property<string>("IpAddress")
                        .HasMaxLength(45)
                        .HasColumnType("character varying(45)");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("PerformingUserId")
                        .HasColumnType("uuid");

                    b.Property<bool>("Success")
                        .HasColumnType("boolean");

                    b.Property<string>("TargetEntityId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("TargetEntityType")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("Timestamp")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("ActionType");

                    b.HasIndex("PerformingUserId");

                    b.HasIndex("Timestamp");

                    b.HasIndex("UserId");

                    b.HasIndex("TargetEntityType", "TargetEntityId");

                    b.ToTable("AuditLogs");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.Budget", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("AllocationRulesJson")
                        .HasColumnType("jsonb");

                    b.Property<string>("ApprovedById")
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedById")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .HasMaxLength(3)
                        .HasColumnType("character varying(3)");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<DateTime>("EndDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<int>("FiscalYear")
                        .HasColumnType("integer");

                    b.Property<string>("ForecastPeriodsJson")
                        .HasColumnType("jsonb");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<bool>("IsRollingForecast")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<int>("Version")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(1);

                    b.Property<Guid?>("WorkflowInstanceId")
                        .HasColumnType("uuid");

                    b.Property<uint>("xmin")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("ApprovedById");

                    b.HasIndex("CreatedById");

                    b.HasIndex("EndDate");

                    b.HasIndex("FiscalYear");

                    b.HasIndex("IsDeleted");

                    b.HasIndex("Name");

                    b.HasIndex("StartDate");

                    b.HasIndex("Status");

                    b.HasIndex("TenantId");

                    b.HasIndex("WorkflowInstanceId");

                    b.ToTable("budgets", "public");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.BudgetAllocation", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("AllocationDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid>("BudgetId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("DepartmentId")
                        .HasColumnType("uuid");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<string>("FiscalPeriodIdentifier")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<uint>("xmin")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("BudgetId");

                    b.HasIndex("DepartmentId");

                    b.HasIndex("FiscalPeriodIdentifier");

                    b.HasIndex("Status");

                    b.HasIndex("TenantId");

                    b.HasIndex("BudgetId", "FiscalPeriodIdentifier");

                    b.ToTable("budget_allocations", "public");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.Catalog.CatalogProduct", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("ApprovedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("ApprovedByUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("AvailableDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("CategoryId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .HasMaxLength(4000)
                        .HasColumnType("character varying(4000)");

                    b.Property<string>("InventoryStatus")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<decimal>("ListPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)");

                    b.Property<string>("ProductStatus")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("QuantityBasedPricingTiersJson")
                        .HasColumnType("text");

                    b.Property<string>("RejectionReason")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<Guid>("SellerOrgId")
                        .HasColumnType("uuid");

                    b.Property<int?>("StandardLeadTimeDays")
                        .HasColumnType("integer");

                    b.Property<int?>("StockQuantity")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("SubmittedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UnitOfMeasure")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int>("Version")
                        .IsConcurrencyToken()
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("CategoryId");

                    b.HasIndex("SellerOrgId");

                    b.ToTable("CatalogProducts", "catalog");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.Catalog.ProductCategory", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<Guid?>("ParentCategoryId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("ParentCategoryId");

                    b.ToTable("ProductCategories", "catalog");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.Category", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<Guid?>("ParentCategoryId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("UnspscCode")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique()
                        .HasDatabaseName("IX_categories_code_unique")
                        .HasFilter("\"code\" IS NOT NULL");

                    b.HasIndex("ParentCategoryId");

                    b.HasIndex("TenantId");

                    b.HasIndex("UnspscCode")
                        .HasDatabaseName("IX_categories_unspsc_code");

                    b.ToTable("categories", "public");

                    b.HasData(
                        new
                        {
                            Id = new Guid("*************-2222-2222-************"),
                            Code = "OFF-SUP",
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "General office supplies and stationery",
                            Name = "Office Supplies",
                            TenantId = new Guid("********-1111-1111-1111-********1111"),
                            UnspscCode = "14111500"
                        },
                        new
                        {
                            Id = new Guid("*************-2222-2222-************"),
                            Code = "IT-EQP",
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "Computer hardware and accessories",
                            Name = "IT Equipment",
                            TenantId = new Guid("********-1111-1111-1111-********1111"),
                            UnspscCode = "43210000"
                        },
                        new
                        {
                            Id = new Guid("*************-2222-2222-************"),
                            Code = "FURN",
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "Office furniture and fixtures",
                            Name = "Furniture",
                            TenantId = new Guid("********-1111-1111-1111-********1111"),
                            UnspscCode = "56100000"
                        },
                        new
                        {
                            Id = new Guid("*************-2222-2222-************"),
                            Code = "RAW-MAT",
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "Materials used in manufacturing",
                            Name = "Raw Materials",
                            TenantId = new Guid("********-1111-1111-1111-********1111"),
                            UnspscCode = "11000000"
                        },
                        new
                        {
                            Id = new Guid("*************-2222-2222-************"),
                            Code = "SERV",
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "Professional and consulting services",
                            Name = "Services",
                            TenantId = new Guid("********-1111-1111-1111-********1111"),
                            UnspscCode = "80000000"
                        },
                        new
                        {
                            Id = new Guid("*************-2222-2222-************"),
                            Code = "PAPER",
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "Paper, notebooks, and notepads",
                            Name = "Paper Products",
                            ParentCategoryId = new Guid("*************-2222-2222-************"),
                            TenantId = new Guid("********-1111-1111-1111-********1111"),
                            UnspscCode = "14111500"
                        },
                        new
                        {
                            Id = new Guid("*************-2222-2222-************"),
                            Code = "WRITE",
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "Pens, pencils, and markers",
                            Name = "Writing Instruments",
                            ParentCategoryId = new Guid("*************-2222-2222-************"),
                            TenantId = new Guid("********-1111-1111-1111-********1111"),
                            UnspscCode = "44121700"
                        },
                        new
                        {
                            Id = new Guid("*************-2222-2222-************"),
                            Code = "COMP",
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "Desktops, laptops, and tablets",
                            Name = "Computers",
                            ParentCategoryId = new Guid("*************-2222-2222-************"),
                            TenantId = new Guid("********-1111-1111-1111-********1111"),
                            UnspscCode = "43211500"
                        },
                        new
                        {
                            Id = new Guid("*************-2222-2222-************"),
                            Code = "PERIPH",
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "Monitors, keyboards, and mice",
                            Name = "Peripherals",
                            ParentCategoryId = new Guid("*************-2222-2222-************"),
                            TenantId = new Guid("********-1111-1111-1111-********1111"),
                            UnspscCode = "43211900"
                        },
                        new
                        {
                            Id = new Guid("*************-2222-2222-************"),
                            Code = "CONSULT",
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "Business and management consulting",
                            Name = "Consulting",
                            ParentCategoryId = new Guid("*************-2222-2222-************"),
                            TenantId = new Guid("********-1111-1111-1111-********1111"),
                            UnspscCode = "80101500"
                        },
                        new
                        {
                            Id = new Guid("*************-2222-2222-************"),
                            Code = "IT-SERV",
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "Software development and support",
                            Name = "IT Services",
                            ParentCategoryId = new Guid("*************-2222-2222-************"),
                            TenantId = new Guid("********-1111-1111-1111-********1111"),
                            UnspscCode = "81110000"
                        });
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.Contract", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("ComplianceDocumentLinksJson")
                        .HasColumnType("jsonb");

                    b.Property<string>("ContractNumber")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("ContractType")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("EndDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsAutoRenew")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<string>("MilestonesJson")
                        .HasColumnType("jsonb");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("PaymentTerms")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("RenewalTerms")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("SlaDetailsJson")
                        .HasColumnType("jsonb");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("TerminationPenaltyTerms")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("TermsAndConditions")
                        .HasColumnType("text");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)");

                    b.Property<Guid>("VendorId")
                        .HasColumnType("uuid");

                    b.Property<decimal?>("VendorPerformanceScoreSnapshot")
                        .HasPrecision(5, 2)
                        .HasColumnType("numeric(5,2)");

                    b.Property<int>("Version")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(1);

                    b.Property<uint>("xmin")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("ContractNumber")
                        .IsUnique();

                    b.HasIndex("EndDate");

                    b.HasIndex("IsDeleted");

                    b.HasIndex("StartDate");

                    b.HasIndex("Status");

                    b.HasIndex("TenantId");

                    b.HasIndex("VendorId");

                    b.ToTable("contracts", (string)null);
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.Customer", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("AssignedSalesRepId")
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CustomerCode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("customer_code");

                    b.Property<string>("CustomerType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("customer_type");

                    b.Property<string>("DefaultCurrencyCode")
                        .HasMaxLength(3)
                        .HasColumnType("character varying(3)")
                        .HasColumnName("default_currency_code");

                    b.Property<string>("DefaultPaymentTerms")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("default_payment_terms");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true)
                        .HasColumnName("is_active");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<string>("LegalName")
                        .HasColumnType("text")
                        .HasColumnName("legal_name");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("name");

                    b.Property<string>("TaxIdentifier")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("tax_identifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("Website")
                        .HasColumnType("text")
                        .HasColumnName("website");

                    b.Property<uint>("xmin")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("AssignedSalesRepId");

                    b.HasIndex("CustomerType");

                    b.HasIndex("IsActive");

                    b.HasIndex("IsDeleted");

                    b.HasIndex("Name");

                    b.HasIndex("TaxIdentifier");

                    b.HasIndex("TenantId");

                    b.HasIndex("TenantId", "CustomerCode")
                        .IsUnique();

                    b.ToTable("customers", "public");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.DeliveryNote", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("DeliveryDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("DeliveryNoteNumber")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Notes")
                        .HasColumnType("text");

                    b.Property<Guid?>("PurchaseOrderId")
                        .HasColumnType("uuid");

                    b.Property<string>("ReceivedBy")
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)");

                    b.Property<Guid?>("SalesOrderId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("ShipmentDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("VendorId")
                        .HasColumnType("uuid");

                    b.Property<uint>("xmin")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("DeliveryDate");

                    b.HasIndex("DeliveryNoteNumber")
                        .IsUnique();

                    b.HasIndex("PurchaseOrderId");

                    b.HasIndex("SalesOrderId");

                    b.HasIndex("Status");

                    b.HasIndex("TenantId");

                    b.HasIndex("VendorId");

                    b.ToTable("delivery_notes", (string)null);
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.DeliveryNoteLine", b =>
                {
                    b.Property<Guid>("DeliveryNoteId")
                        .HasColumnType("uuid");

                    b.Property<int>("LineNumber")
                        .HasColumnType("integer");

                    b.Property<string>("BatchNumber")
                        .HasMaxLength(100)
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Notes")
                        .HasColumnType("text");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uuid");

                    b.Property<string>("ProductSkuSnapshot")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<Guid?>("PurchaseOrderLineId")
                        .HasColumnType("uuid");

                    b.Property<decimal>("QuantityShipped")
                        .HasColumnType("numeric(18, 4)");

                    b.Property<Guid?>("SalesOrderId")
                        .HasColumnType("uuid");

                    b.Property<int?>("SalesOrderLineNumber")
                        .HasColumnType("integer");

                    b.Property<string>("SerialNumber")
                        .HasMaxLength(100)
                        .HasColumnType("text");

                    b.Property<string>("UnitOfMeasure")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.HasKey("DeliveryNoteId", "LineNumber");

                    b.HasIndex("ProductId");

                    b.HasIndex("PurchaseOrderLineId");

                    b.HasIndex("SalesOrderId", "SalesOrderLineNumber");

                    b.ToTable("delivery_note_lines", (string)null);
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.Department", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<uint>("xmin")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("Name");

                    b.HasIndex("TenantId");

                    b.HasIndex("TenantId", "Code")
                        .IsUnique()
                        .HasFilter("\"code\" IS NOT NULL");

                    b.ToTable("departments", "public");

                    b.HasData(
                        new
                        {
                            Id = new Guid("********-4444-4444-4444-********4401"),
                            Code = "IT",
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "Handles IT infrastructure, software development, and technical support.",
                            Name = "Information Technology",
                            TenantId = new Guid("********-1111-1111-1111-********1111")
                        },
                        new
                        {
                            Id = new Guid("********-4444-4444-4444-********4402"),
                            Code = "FIN",
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "Manages financial operations, budgeting, and accounting.",
                            Name = "Finance",
                            TenantId = new Guid("********-1111-1111-1111-********1111")
                        },
                        new
                        {
                            Id = new Guid("********-4444-4444-4444-********4403"),
                            Code = "HR",
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "Handles employee recruitment, training, and personnel management.",
                            Name = "Human Resources",
                            TenantId = new Guid("********-1111-1111-1111-********1111")
                        },
                        new
                        {
                            Id = new Guid("********-4444-4444-4444-********4404"),
                            Code = "PROC",
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "Manages purchasing, vendor relationships, and supply chain operations.",
                            Name = "Procurement",
                            TenantId = new Guid("********-1111-1111-1111-********1111")
                        },
                        new
                        {
                            Id = new Guid("********-4444-4444-4444-************"),
                            Code = "OPS",
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "Oversees day-to-day operational activities and logistics.",
                            Name = "Operations",
                            TenantId = new Guid("********-1111-1111-1111-********1111")
                        },
                        new
                        {
                            Id = new Guid("********-4444-4444-4444-********4406"),
                            Code = "MKT",
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "Handles advertising, brand management, and market research.",
                            Name = "Marketing",
                            TenantId = new Guid("********-1111-1111-1111-********1111")
                        });
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.GoodsReceiptNote", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("DeliveryNoteId")
                        .HasColumnType("uuid");

                    b.Property<string>("GoodsReceiptNoteNumber")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("GrnNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("InspectionDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Notes")
                        .HasColumnType("text");

                    b.Property<Guid>("PurchaseOrderId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("ReceiptDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("ReceivedByName")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)");

                    b.Property<string>("ReceivedByUserId")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.Property<string>("ReceivingLocation")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("VendorId")
                        .HasColumnType("uuid");

                    b.Property<uint>("xmin")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("DeliveryNoteId");

                    b.HasIndex("GoodsReceiptNoteNumber")
                        .IsUnique();

                    b.HasIndex("PurchaseOrderId");

                    b.HasIndex("ReceiptDate");

                    b.HasIndex("ReceivedByUserId");

                    b.HasIndex("Status");

                    b.HasIndex("TenantId");

                    b.HasIndex("VendorId");

                    b.ToTable("goods_receipt_notes", (string)null);
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.GoodsReceiptNoteLine", b =>
                {
                    b.Property<Guid>("GoodsReceiptNoteId")
                        .HasColumnType("uuid");

                    b.Property<int>("LineNumber")
                        .HasColumnType("integer");

                    b.Property<string>("BatchNumber")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("DeliveryNoteLineDeliveryNoteId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("DeliveryNoteLineId")
                        .HasColumnType("uuid");

                    b.Property<int?>("DeliveryNoteLineLineNumber")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("ExpiryDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<bool>("InspectionCompleted")
                        .HasColumnType("boolean");

                    b.Property<string>("LotNumber")
                        .HasColumnType("text");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Notes")
                        .HasColumnType("text");

                    b.Property<string>("ProductDescriptionSnapshot")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uuid");

                    b.Property<string>("ProductSkuSnapshot")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<Guid>("PurchaseOrderLineId")
                        .HasColumnType("uuid");

                    b.Property<string>("PutAwayLocation")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("QualityControlStatus")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<decimal>("QuantityAccepted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("numeric(18, 4)")
                        .HasDefaultValue(0m);

                    b.Property<decimal>("QuantityReceived")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("numeric(18, 4)")
                        .HasDefaultValue(0m);

                    b.Property<decimal>("QuantityRejected")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("numeric(18, 4)")
                        .HasDefaultValue(0m);

                    b.Property<string>("RejectionReason")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("UnitOfMeasure")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.HasKey("GoodsReceiptNoteId", "LineNumber");

                    b.HasIndex("ExpiryDate");

                    b.HasIndex("ProductId");

                    b.HasIndex("PurchaseOrderLineId");

                    b.HasIndex("QualityControlStatus");

                    b.HasIndex("DeliveryNoteLineDeliveryNoteId", "DeliveryNoteLineLineNumber");

                    b.ToTable("goods_receipt_note_lines", (string)null);
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.Invoice", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<decimal>("AmountPaid")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .HasMaxLength(3)
                        .HasColumnType("character varying(3)");

                    b.Property<Guid?>("CustomerId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("DueDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<DateTime>("InvoiceDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("InvoiceNumber")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Notes")
                        .HasColumnType("text");

                    b.Property<DateTime?>("PaymentDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("PaymentTerms")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<Guid?>("PurchaseOrderId")
                        .HasColumnType("uuid");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<decimal>("SubTotal")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("Subtotal")
                        .HasColumnType("numeric(18, 2)");

                    b.Property<decimal>("TaxAmount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("numeric(18, 2)")
                        .HasDefaultValue(0m);

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("numeric(18, 2)");

                    b.Property<Guid>("VendorId")
                        .HasColumnType("uuid");

                    b.Property<uint>("xmin")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("CustomerId");

                    b.HasIndex("DueDate");

                    b.HasIndex("InvoiceDate");

                    b.HasIndex("PurchaseOrderId");

                    b.HasIndex("Status");

                    b.HasIndex("TenantId");

                    b.HasIndex("VendorId");

                    b.HasIndex("VendorId", "InvoiceNumber")
                        .IsUnique();

                    b.ToTable("invoices", (string)null);
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.InvoiceLine", b =>
                {
                    b.Property<Guid>("InvoiceId")
                        .HasColumnType("uuid");

                    b.Property<int>("LineNumber")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("text");

                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<decimal>("LineTotal")
                        .HasColumnType("numeric(18, 2)");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Notes")
                        .HasColumnType("text");

                    b.Property<Guid?>("ProductId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("PurchaseOrderLineId")
                        .HasColumnType("uuid");

                    b.Property<decimal>("Quantity")
                        .HasColumnType("numeric(18, 4)");

                    b.Property<decimal?>("TaxAmount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("numeric(18, 2)")
                        .HasDefaultValue(0m);

                    b.Property<decimal?>("TaxRate")
                        .HasColumnType("numeric(5, 4)");

                    b.Property<string>("UnitOfMeasure")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<decimal>("UnitPrice")
                        .HasColumnType("numeric(18, 4)");

                    b.HasKey("InvoiceId", "LineNumber");

                    b.HasIndex("ProductId");

                    b.HasIndex("PurchaseOrderLineId");

                    b.ToTable("invoice_lines", (string)null);
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.PasswordPolicy", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("AccountLockoutDurationMinutes")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("MaxFailedLoginAttempts")
                        .HasColumnType("integer");

                    b.Property<int>("MinLength")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("PasswordExpirationDays")
                        .HasColumnType("integer");

                    b.Property<int>("PasswordHistoryCount")
                        .HasColumnType("integer");

                    b.Property<bool>("RequireDigit")
                        .HasColumnType("boolean");

                    b.Property<bool>("RequireLowercase")
                        .HasColumnType("boolean");

                    b.Property<bool>("RequireSpecialCharacter")
                        .HasColumnType("boolean");

                    b.Property<bool>("RequireUppercase")
                        .HasColumnType("boolean");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("TenantId")
                        .IsUnique();

                    b.ToTable("PasswordPolicies");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.PaymentTransaction", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<decimal>("Amount")
                        .HasColumnType("numeric(18, 2)");

                    b.Property<string>("BankReference")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .HasMaxLength(3)
                        .HasColumnType("character varying(3)");

                    b.Property<Guid?>("InvoiceId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Notes")
                        .HasColumnType("text");

                    b.Property<DateTime>("PaymentDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("PaymentMethod")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("TransactionReference")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("transaction_reference");

                    b.Property<Guid>("VendorId")
                        .HasColumnType("uuid");

                    b.Property<uint>("xmin")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("InvoiceId");

                    b.HasIndex("PaymentDate");

                    b.HasIndex("Status");

                    b.HasIndex("TenantId");

                    b.HasIndex("TransactionReference")
                        .IsUnique()
                        .HasFilter("\"transaction_reference\" IS NOT NULL");

                    b.HasIndex("VendorId");

                    b.ToTable("payment_transactions", (string)null);
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.ProcurementSpeckleLink", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("LinkedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("LinkedByUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("ProcurementItemId")
                        .HasColumnType("uuid");

                    b.Property<string>("ProcurementItemType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<Guid>("SpeckleObjectMetadataId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("LinkedByUserId");

                    b.HasIndex("SpeckleObjectMetadataId");

                    b.HasIndex("ProcurementItemType", "ProcurementItemId");

                    b.ToTable("ProcurementSpeckleLinks", (string)null);
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.ProcurementWorkflow", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CompletedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("CurrentStatus")
                        .HasColumnType("integer");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("text");

                    b.Property<string>("InitiatedByName")
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)");

                    b.Property<string>("InitiatedByUserId")
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)");

                    b.Property<DateTime>("StartedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("SubjectDocumentId")
                        .HasColumnType("uuid");

                    b.Property<string>("SubjectDocumentType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<int?>("Version")
                        .HasMaxLength(20)
                        .HasColumnType("integer");

                    b.Property<string>("WorkflowName")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)");

                    b.Property<string>("WorkflowType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("IsActive");

                    b.HasIndex("Name");

                    b.HasIndex("TenantId");

                    b.HasIndex("WorkflowType");

                    b.HasIndex("TenantId", "WorkflowType", "Name")
                        .IsUnique();

                    b.ToTable("procurement_workflows", (string)null);
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.ProcurementWorkflowStep", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("ActionDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("ApproverRoleId")
                        .HasColumnType("uuid");

                    b.Property<string>("ApproverUserId")
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.Property<DateTime?>("AssignedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("AssigneeName")
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)");

                    b.Property<string>("AssigneeUserId")
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.Property<string>("Comments")
                        .HasColumnType("text");

                    b.Property<string>("ConditionExpression")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("ProcurementWorkflowId")
                        .HasColumnType("uuid");

                    b.Property<int>("SequenceOrder")
                        .HasColumnType("integer");

                    b.Property<TimeSpan?>("SlaDuration")
                        .HasColumnType("interval");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("StepName")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)");

                    b.Property<int>("StepOrder")
                        .HasColumnType("integer");

                    b.Property<Guid>("WorkflowId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("ApproverRoleId");

                    b.HasIndex("ApproverUserId");

                    b.HasIndex("ProcurementWorkflowId");

                    b.HasIndex("WorkflowId");

                    b.HasIndex("ProcurementWorkflowId", "StepOrder")
                        .IsUnique();

                    b.ToTable("procurement_workflow_steps", (string)null);
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.Product", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("CategoryId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("ProductCode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("UnitOfMeasure")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.HasKey("Id");

                    b.HasIndex("CategoryId")
                        .HasDatabaseName("IX_products_category_id");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_products_is_active");

                    b.HasIndex("IsDeleted")
                        .HasDatabaseName("IX_products_is_deleted");

                    b.HasIndex("Name")
                        .HasDatabaseName("IX_products_name");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("IX_products_tenant_id");

                    b.HasIndex("TenantId", "ProductCode")
                        .IsUnique()
                        .HasDatabaseName("IX_products_tenant_id_product_code_unique");

                    b.ToTable("products", "public");

                    b.HasData(
                        new
                        {
                            Id = new Guid("*************-3333-3333-************"),
                            CategoryId = new Guid("*************-2222-2222-************"),
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "Premium A4 copy paper, 80gsm, 500 sheets per ream, 5 reams per box",
                            IsActive = true,
                            IsDeleted = false,
                            Name = "A4 Copy Paper",
                            ProductCode = "PAPER-001",
                            TenantId = new Guid("********-1111-1111-1111-********1111"),
                            UnitOfMeasure = "Box"
                        },
                        new
                        {
                            Id = new Guid("*************-3333-3333-************"),
                            CategoryId = new Guid("*************-2222-2222-************"),
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "A5 spiral-bound notebook, 100 pages, ruled",
                            IsActive = true,
                            IsDeleted = false,
                            Name = "Spiral Notebook",
                            ProductCode = "PAPER-002",
                            TenantId = new Guid("********-1111-1111-1111-********1111"),
                            UnitOfMeasure = "Each"
                        },
                        new
                        {
                            Id = new Guid("*************-3333-3333-************"),
                            CategoryId = new Guid("*************-2222-2222-************"),
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "Blue ballpoint pens, medium point, 12 pens per box",
                            IsActive = true,
                            IsDeleted = false,
                            Name = "Ballpoint Pen",
                            ProductCode = "WRITE-001",
                            TenantId = new Guid("********-1111-1111-1111-********1111"),
                            UnitOfMeasure = "Box"
                        },
                        new
                        {
                            Id = new Guid("*************-3333-3333-************"),
                            CategoryId = new Guid("*************-2222-2222-************"),
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "15.6\" business laptop, 16GB RAM, 512GB SSD, Intel Core i7",
                            IsActive = true,
                            IsDeleted = false,
                            Name = "Laptop Computer",
                            ProductCode = "COMP-001",
                            TenantId = new Guid("********-1111-1111-1111-********1111"),
                            UnitOfMeasure = "Each"
                        },
                        new
                        {
                            Id = new Guid("*************-3333-3333-************"),
                            CategoryId = new Guid("*************-2222-2222-************"),
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "24-inch LED monitor, 1080p, HDMI and DisplayPort",
                            IsActive = true,
                            IsDeleted = false,
                            Name = "24\" Monitor",
                            ProductCode = "PERIPH-001",
                            TenantId = new Guid("********-1111-1111-1111-********1111"),
                            UnitOfMeasure = "Each"
                        },
                        new
                        {
                            Id = new Guid("*************-3333-3333-************"),
                            CategoryId = new Guid("*************-2222-2222-************"),
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "Ergonomic wireless mouse, 2.4GHz, USB receiver",
                            IsActive = true,
                            IsDeleted = false,
                            Name = "Wireless Mouse",
                            ProductCode = "PERIPH-002",
                            TenantId = new Guid("********-1111-1111-1111-********1111"),
                            UnitOfMeasure = "Each"
                        });
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.ProductDefinition", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("AttributesJson")
                        .HasColumnType("jsonb");

                    b.Property<Guid?>("CategoryId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("Ean")
                        .HasMaxLength(13)
                        .HasColumnType("character varying(13)");

                    b.Property<string>("Gtin")
                        .HasMaxLength(14)
                        .HasColumnType("character varying(14)");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<string>("LifecycleState")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasDefaultValue("Active");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)");

                    b.Property<string>("Sku")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("Upc")
                        .HasMaxLength(12)
                        .HasColumnType("character varying(12)");

                    b.Property<int>("Version")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(1);

                    b.HasKey("Id");

                    b.HasIndex("CategoryId");

                    b.HasIndex("Ean")
                        .IsUnique()
                        .HasFilter("\"ean\" IS NOT NULL");

                    b.HasIndex("Gtin")
                        .IsUnique()
                        .HasFilter("\"gtin\" IS NOT NULL");

                    b.HasIndex("IsDeleted");

                    b.HasIndex("Sku")
                        .IsUnique();

                    b.HasIndex("Upc")
                        .IsUnique()
                        .HasFilter("\"upc\" IS NOT NULL");

                    b.ToTable("product_definitions", (string)null);
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.Project", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<DateTime?>("EndDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("ProjectCode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("StartDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<uint>("xmin")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("EndDate");

                    b.HasIndex("IsDeleted");

                    b.HasIndex("Name");

                    b.HasIndex("StartDate");

                    b.HasIndex("Status");

                    b.HasIndex("TenantId");

                    b.HasIndex("TenantId", "ProjectCode")
                        .IsUnique();

                    b.ToTable("projects", "public");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.PurchaseOrder", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("ContractId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .HasMaxLength(3)
                        .HasColumnType("character varying(3)");

                    b.Property<DateTime?>("DeliveryDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("OrderDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("OrderNumber")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("PaymentTerms")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<Guid?>("RequisitionId")
                        .HasColumnType("uuid");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<Guid>("VendorId")
                        .HasColumnType("uuid");

                    b.Property<uint>("xmin")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("ContractId");

                    b.HasIndex("DeliveryDate")
                        .HasDatabaseName("IX_PurchaseOrder_DeliveryDate");

                    b.HasIndex("OrderDate");

                    b.HasIndex("OrderNumber")
                        .IsUnique();

                    b.HasIndex("RequisitionId");

                    b.HasIndex("Status");

                    b.HasIndex("VendorId");

                    b.ToTable("purchase_orders", (string)null);

                    b.UseTptMappingStrategy();
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.PurchaseOrderLine", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DescriptionSnapshot")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Notes")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<Guid>("PurchaseOrderId")
                        .HasColumnType("uuid");

                    b.Property<decimal>("Quantity")
                        .HasPrecision(18, 4)
                        .HasColumnType("numeric(18,4)");

                    b.Property<string>("SkuSnapshot")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("UnitOfMeasureSnapshot")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<Guid>("VendorProductId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("PurchaseOrderId");

                    b.HasIndex("VendorProductId");

                    b.ToTable("purchase_order_lines", (string)null);
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.PurchaseRequisition", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("AssociatedPurchaseOrderId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .HasMaxLength(3)
                        .HasColumnType("character varying(3)");

                    b.Property<DateTime?>("DateNeeded")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Department")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Justification")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Notes")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<DateTime>("RequestDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("RequestorEmail")
                        .IsRequired()
                        .HasMaxLength(254)
                        .HasColumnType("character varying(254)");

                    b.Property<string>("RequestorName")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)");

                    b.Property<string>("RequestorUserId")
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.Property<string>("RequisitionNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<uint>("xmin")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("AssociatedPurchaseOrderId");

                    b.HasIndex("RequestDate");

                    b.HasIndex("RequestorUserId");

                    b.HasIndex("Status");

                    b.HasIndex("TenantId");

                    b.HasIndex("TenantId", "RequisitionNumber")
                        .IsUnique();

                    b.ToTable("purchase_requisitions", (string)null);
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.PurchaseRequisitionLine", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("DateNeeded")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("GLAccountCode")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int>("LineNumber")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Notes")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<Guid?>("ProductDefinitionId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("PurchaseRequisitionId")
                        .HasColumnType("uuid");

                    b.Property<decimal>("Quantity")
                        .HasPrecision(18, 4)
                        .HasColumnType("numeric(18,4)");

                    b.Property<Guid?>("SuggestedVendorId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("UnitOfMeasure")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<Guid?>("VendorProductId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("GLAccountCode");

                    b.HasIndex("ProductDefinitionId");

                    b.HasIndex("SuggestedVendorId");

                    b.HasIndex("TenantId");

                    b.HasIndex("VendorProductId");

                    b.HasIndex("PurchaseRequisitionId", "LineNumber")
                        .IsUnique();

                    b.ToTable("purchase_requisition_lines", (string)null);
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.RequestForInformation", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<DateTime>("IssueDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("IssuedByName")
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)");

                    b.Property<string>("IssuedByUserId")
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.Property<DateTime?>("IssuedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Notes")
                        .HasColumnType("text");

                    b.Property<Guid?>("ProjectId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("ResponseDeadline")
                        .HasColumnType("timestamp without time zone");

                    b.Property<DateTime>("ResponseDueDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("RfiNumber")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("TargetAudienceDescription")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<uint>("xmin")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("ProjectId");

                    b.HasIndex("ResponseDeadline");

                    b.HasIndex("RfiNumber")
                        .IsUnique();

                    b.HasIndex("Status");

                    b.HasIndex("TenantId");

                    b.ToTable("requests_for_information", (string)null);
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.RequestForProposal", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("AwardedContractId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("AwardedVendorId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CompletedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("DecisionDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Department")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("EvaluationCriteria")
                        .HasColumnType("jsonb");

                    b.Property<string>("ExpectedContractDuration")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("ExpectedContractStartDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("IssueDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("IssuedByName")
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)");

                    b.Property<string>("IssuedByUserId")
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.Property<DateTime?>("IssuedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("ProjectId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("QuestionDeadline")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("RfpNumber")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("ScopeOfWork")
                        .HasColumnType("text");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("SubmissionDeadline")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<uint>("xmin")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("AwardedContractId");

                    b.HasIndex("AwardedVendorId");

                    b.HasIndex("ProjectId");

                    b.HasIndex("RfpNumber")
                        .IsUnique();

                    b.HasIndex("Status");

                    b.HasIndex("SubmissionDeadline");

                    b.HasIndex("TenantId");

                    b.ToTable("requests_for_proposal", (string)null);
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.RequestForQuote", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("AwardedVendorId")
                        .HasColumnType("uuid");

                    b.Property<string>("CommunicationMethod")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("ContactPersonEmail")
                        .HasMaxLength(254)
                        .HasColumnType("character varying(254)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedByUserId")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .HasMaxLength(3)
                        .HasColumnType("character varying(3)");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("OriginatingRequisitionId")
                        .HasColumnType("uuid");

                    b.Property<string>("RFQNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<Guid?>("RelatedAgreementId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("RequiredDeliveryDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("ScopeOfWork")
                        .HasColumnType("text");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("SubmissionDeadline")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("TermsAndConditions")
                        .HasColumnType("text");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)");

                    b.Property<string>("VendorReferenceInstructions")
                        .HasColumnType("text");

                    b.Property<uint>("xmin")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("AwardedVendorId");

                    b.HasIndex("CreatedByUserId");

                    b.HasIndex("IsDeleted");

                    b.HasIndex("OriginatingRequisitionId");

                    b.HasIndex("RelatedAgreementId");

                    b.HasIndex("Status");

                    b.HasIndex("SubmissionDeadline");

                    b.HasIndex("TenantId");

                    b.HasIndex("TenantId", "RFQNumber")
                        .IsUnique();

                    b.ToTable("request_for_quotes", "public");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.RequestForQuoteLine", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("AlternateItemProposal")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<bool>("IsSubstituteAllowed")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<int>("LineNumber")
                        .HasColumnType("integer");

                    b.Property<decimal?>("MinimumOrderQuantity")
                        .HasPrecision(18, 4)
                        .HasColumnType("numeric(18,4)");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Notes")
                        .HasColumnType("text");

                    b.Property<string>("PreferredIncoterm")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<Guid?>("ProductDefinitionId")
                        .HasColumnType("uuid");

                    b.Property<decimal>("Quantity")
                        .HasPrecision(18, 4)
                        .HasColumnType("numeric(18,4)");

                    b.Property<Guid>("RequestForQuoteId")
                        .HasColumnType("uuid");

                    b.Property<bool>("SampleRequired")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<string>("TechnicalSpecifications")
                        .HasColumnType("text");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("UnitOfMeasure")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<Guid?>("VendorProductId")
                        .HasColumnType("uuid");

                    b.Property<uint>("xmin")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("IsDeleted");

                    b.HasIndex("ProductDefinitionId");

                    b.HasIndex("TenantId");

                    b.HasIndex("VendorProductId");

                    b.HasIndex("RequestForQuoteId", "LineNumber")
                        .IsUnique();

                    b.ToTable("request_for_quote_lines", "public");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.ReturnAuthorization", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("AuthorizationDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("CustomerId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("ExpiryDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid?>("InvoiceId")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Notes")
                        .HasColumnType("text");

                    b.Property<Guid>("OriginalSalesOrderId")
                        .HasColumnType("uuid");

                    b.Property<string>("ReasonForReturn")
                        .HasColumnType("text");

                    b.Property<DateTime>("RequestDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<int>("RequestedAction")
                        .HasColumnType("integer");

                    b.Property<string>("RmaNumber")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<Guid?>("SalesOrderId")
                        .HasColumnType("uuid");

                    b.Property<string>("ShippingInstructions")
                        .HasColumnType("text");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<uint>("xmin")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("CustomerId");

                    b.HasIndex("InvoiceId");

                    b.HasIndex("RequestDate");

                    b.HasIndex("RmaNumber")
                        .IsUnique();

                    b.HasIndex("SalesOrderId");

                    b.HasIndex("Status");

                    b.HasIndex("TenantId");

                    b.ToTable("return_authorizations", (string)null);
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.ReturnAuthorizationLine", b =>
                {
                    b.Property<Guid>("ReturnAuthorizationId")
                        .HasColumnType("uuid");

                    b.Property<int>("LineNumber")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DescriptionSnapshot")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("InvoiceId")
                        .HasColumnType("uuid");

                    b.Property<int?>("InvoiceLineNumber")
                        .HasColumnType("integer");

                    b.Property<string>("ItemCondition")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("OriginalSalesOrderLineId")
                        .HasColumnType("uuid");

                    b.Property<int?>("OriginalSalesOrderLineLineNumber")
                        .HasColumnType("integer");

                    b.Property<Guid?>("OriginalSalesOrderLineSalesOrderId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("ProductDefinitionId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uuid");

                    b.Property<decimal>("QuantityAuthorized")
                        .HasColumnType("numeric(18, 4)");

                    b.Property<decimal>("QuantityReceived")
                        .HasColumnType("numeric");

                    b.Property<string>("ReasonForReturn")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("RequestedAction")
                        .HasColumnType("integer");

                    b.Property<Guid?>("SalesOrderId")
                        .HasColumnType("uuid");

                    b.Property<int?>("SalesOrderLineNumber")
                        .HasColumnType("integer");

                    b.Property<string>("SkuSnapshot")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("UnitOfMeasure")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<Guid?>("VendorProductId")
                        .HasColumnType("uuid");

                    b.HasKey("ReturnAuthorizationId", "LineNumber");

                    b.HasIndex("ProductDefinitionId");

                    b.HasIndex("ProductId");

                    b.HasIndex("VendorProductId");

                    b.HasIndex("InvoiceId", "InvoiceLineNumber");

                    b.HasIndex("OriginalSalesOrderLineSalesOrderId", "OriginalSalesOrderLineLineNumber");

                    b.HasIndex("SalesOrderId", "SalesOrderLineNumber");

                    b.ToTable("return_authorization_lines", (string)null);
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.Role", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("P2PRoles");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.RolePermission", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("PermissionString")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("RoleId", "PermissionString")
                        .IsUnique();

                    b.ToTable("RolePermissions");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.SalesOrder", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("AtpCheckDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<decimal?>("CommissionRate")
                        .HasPrecision(5, 4)
                        .HasColumnType("numeric(5,4)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .HasMaxLength(3)
                        .HasColumnType("character varying(3)");

                    b.Property<Guid?>("CustomerId")
                        .HasColumnType("uuid");

                    b.Property<string>("EdiTransactionReference")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<bool>("IsAtpConfirmed")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<bool>("IsCreditApproved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<bool>("IsDropShipment")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("OrderDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("OrderNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<Guid?>("RelatedReturnAuthorizationId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("SalesTerritoryId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("SalesTerritoryId1")
                        .HasColumnType("uuid");

                    b.Property<string>("SalespersonId")
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<uint>("xmin")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("CustomerId");

                    b.HasIndex("EdiTransactionReference");

                    b.HasIndex("IsDeleted");

                    b.HasIndex("OrderDate");

                    b.HasIndex("RelatedReturnAuthorizationId");

                    b.HasIndex("SalesTerritoryId");

                    b.HasIndex("SalesTerritoryId1");

                    b.HasIndex("SalespersonId");

                    b.HasIndex("Status");

                    b.HasIndex("TenantId");

                    b.HasIndex("TenantId", "OrderNumber")
                        .IsUnique();

                    b.ToTable("sales_orders", "public");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.SalesOrderLine", b =>
                {
                    b.Property<Guid>("SalesOrderId")
                        .HasColumnType("uuid");

                    b.Property<int>("LineNumber")
                        .HasColumnType("integer");

                    b.Property<string>("AppliedDiscountDescription")
                        .HasColumnType("text");

                    b.Property<string>("CostCode")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("DescriptionSnapshot")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("DiscountAmountCurrency")
                        .HasMaxLength(3)
                        .HasColumnType("character varying(3)");

                    b.Property<decimal?>("DiscountAmountValue")
                        .HasColumnType("numeric(18, 2)");

                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsKitComponent")
                        .HasColumnType("boolean");

                    b.Property<decimal>("LineTotalAmount")
                        .HasColumnType("numeric(18, 2)");

                    b.Property<string>("LineTotalCurrency")
                        .IsRequired()
                        .HasMaxLength(3)
                        .HasColumnType("character varying(3)");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("ParentSalesOrderLineId")
                        .HasColumnType("uuid");

                    b.Property<int?>("ParentSalesOrderLineLineNumber")
                        .HasColumnType("integer");

                    b.Property<Guid?>("ParentSalesOrderLineSalesOrderId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("ProjectId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("ProjectId1")
                        .HasColumnType("uuid");

                    b.Property<decimal>("Quantity")
                        .HasColumnType("numeric(18, 4)");

                    b.Property<decimal>("QuantityBackordered")
                        .HasColumnType("numeric");

                    b.Property<DateTime?>("RequestedDeliveryDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("ReservedSerialNumbersJson")
                        .HasColumnType("text");

                    b.Property<string>("SkuSnapshot")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("UnitOfMeasure")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<decimal>("UnitPriceAmount")
                        .HasColumnType("numeric(18, 4)");

                    b.Property<string>("UnitPriceCurrency")
                        .IsRequired()
                        .HasMaxLength(3)
                        .HasColumnType("character varying(3)");

                    b.Property<Guid>("VendorProductId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("WarrantyEndDate")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("SalesOrderId", "LineNumber");

                    b.HasIndex("ProductId");

                    b.HasIndex("ProjectId");

                    b.HasIndex("ProjectId1");

                    b.HasIndex("VendorProductId");

                    b.HasIndex("ParentSalesOrderLineSalesOrderId", "ParentSalesOrderLineLineNumber");

                    b.ToTable("sales_order_lines", (string)null);
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.SalesTerritory", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)");

                    b.Property<Guid?>("ParentTerritoryId")
                        .HasColumnType("uuid");

                    b.Property<string>("PrimarySalespersonId")
                        .HasColumnType("text");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("TerritoryCode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("Name");

                    b.HasIndex("ParentTerritoryId");

                    b.HasIndex("TenantId");

                    b.HasIndex("TerritoryCode")
                        .IsUnique();

                    b.ToTable("sales_territories", (string)null);
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.SpeckleObjectMetadata", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("IfcGuid")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<DateTime>("LastExtractedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ObjectType")
                        .IsRequired()
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)");

                    b.Property<string>("ParametersJson")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("RevitElementId")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("SpeckleObjectId")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<Guid>("SpeckleProjectLinkId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("IfcGuid");

                    b.HasIndex("RevitElementId");

                    b.HasIndex("SpeckleProjectLinkId", "SpeckleObjectId")
                        .IsUnique();

                    b.ToTable("SpeckleObjectMetadata", (string)null);
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.SpeckleProjectLink", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("LastRefreshedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("LinkedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("P2PProjectId")
                        .HasColumnType("uuid");

                    b.Property<string>("SpeckleAuthToken")
                        .IsRequired()
                        .HasMaxLength(2048)
                        .HasColumnType("character varying(2048)");

                    b.Property<string>("SpeckleCommitId")
                        .HasMaxLength(1024)
                        .HasColumnType("character varying(1024)");

                    b.Property<string>("SpeckleServerUrl")
                        .IsRequired()
                        .HasMaxLength(2048)
                        .HasColumnType("character varying(2048)");

                    b.Property<string>("SpeckleStreamId")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("character varying(1024)");

                    b.HasKey("Id");

                    b.HasIndex("P2PProjectId")
                        .IsUnique();

                    b.ToTable("SpeckleProjectLinks", (string)null);
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.SubmittalReview", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Comments")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Disposition")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DocumentLink>("MarkupDocument")
                        .HasColumnType("jsonb");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("ReviewCycle")
                        .HasColumnType("integer");

                    b.Property<DateTime>("ReviewDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp without time zone")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<Guid>("ReviewerGuid")
                        .HasColumnType("uuid");

                    b.Property<string>("ReviewerId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ReviewerName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("TechnicalSubmittalId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("TechnicalSubmittalId1")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("Disposition");

                    b.HasIndex("ReviewDate");

                    b.HasIndex("ReviewerId");

                    b.HasIndex("TechnicalSubmittalId");

                    b.HasIndex("TechnicalSubmittalId1");

                    b.ToTable("submittal_reviews", (string)null);
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.Supplier", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int?>("AverageLeadTimeDays")
                        .HasColumnType("integer");

                    b.Property<decimal?>("CapacityUtilizationPercent")
                        .HasPrecision(5, 2)
                        .HasColumnType("numeric(5,2)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsContractManufacturer")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<bool>("IsCsrCompliant")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("RiskRating")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Status")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasDefaultValue("Active");

                    b.Property<string>("SupplierName")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)");

                    b.Property<decimal?>("SustainabilityScore")
                        .HasPrecision(5, 2)
                        .HasColumnType("numeric(5,2)");

                    b.Property<Guid>("VendorId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("IsContractManufacturer");

                    b.HasIndex("RiskRating");

                    b.HasIndex("VendorId")
                        .IsUnique();

                    b.ToTable("suppliers", (string)null);
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.TechnicalSubmittal", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("ContractId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("CurrentOverallDisposition")
                        .HasColumnType("integer");

                    b.Property<int>("CurrentReviewCycle")
                        .HasColumnType("integer");

                    b.Property<Guid?>("CurrentReviewerId")
                        .HasColumnType("uuid");

                    b.Property<int>("CycleCount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(1);

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<string>("FinalSignOffById")
                        .HasColumnType("text");

                    b.Property<DateTime?>("FinalSignOffDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsAsBuilt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsFinalDocumentation")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<int?>("MaxResubmissions")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("NonConformanceReportId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("ProjectId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("PurchaseOrderLineId")
                        .HasColumnType("uuid");

                    b.Property<string>("RelatedITPReference")
                        .HasColumnType("text");

                    b.Property<string>("RelatedNCRReference")
                        .HasColumnType("text");

                    b.Property<DateTime?>("RequiredDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<int>("ResubmissionCount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<DateTime?>("ReviewCompletionDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("ReviewDueDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("ReviewStartDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Revision")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<Guid?>("SignedOffById")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("SignedOffDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<Guid?>("SpecificationId")
                        .HasColumnType("uuid");

                    b.Property<string>("SpecificationSection")
                        .HasColumnType("text");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("SubmittalNumber")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("SubmittalType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("SubmittedById")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("SubmittedByUserId")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.Property<DateTime>("SubmittedDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<List<DocumentLink>>("SubmittedDocuments")
                        .IsRequired()
                        .HasColumnType("jsonb");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("TestPlanId")
                        .HasColumnType("uuid");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<Guid?>("VendorId")
                        .HasColumnType("uuid");

                    b.Property<uint>("xmin")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("ContractId");

                    b.HasIndex("ProjectId");

                    b.HasIndex("PurchaseOrderLineId");

                    b.HasIndex("RequiredDate");

                    b.HasIndex("SpecificationId");

                    b.HasIndex("Status");

                    b.HasIndex("SubmittalNumber")
                        .IsUnique();

                    b.HasIndex("SubmittedByUserId");

                    b.HasIndex("SubmittedDate");

                    b.HasIndex("TenantId");

                    b.HasIndex("VendorId");

                    b.HasIndex("ProjectId", "SubmittalNumber", "Revision")
                        .IsUnique();

                    b.ToTable("technical_submittals", (string)null);
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.Tenant", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<string>("AddressLine1")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("City")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("ContactEmail")
                        .HasMaxLength(254)
                        .HasColumnType("character varying(254)");

                    b.Property<string>("Country")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Identifier")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("PostalCode")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("Settings")
                        .HasColumnType("jsonb");

                    b.Property<string>("SubscriptionPlan")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("Identifier")
                        .IsUnique()
                        .HasDatabaseName("IX_tenants_identifier_unique");

                    b.ToTable("tenants", "public");

                    b.HasData(
                        new
                        {
                            Id = new Guid("********-1111-1111-1111-********1111"),
                            ContactEmail = "<EMAIL>",
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Identifier = "system",
                            IsActive = true,
                            Name = "Default System Tenant",
                            SubscriptionPlan = "Standard"
                        });
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.TenantProduct", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<decimal>("Price")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("SKU")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("tenant_products", (string)null);
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.TestEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.ToTable("test_entities", (string)null);
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.User", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<int>("FailedLoginAttempts")
                        .HasColumnType("integer");

                    b.Property<string>("FullName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsEmailVerified")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsMfaEnabled")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastLoginDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("LockoutEndDateUtc")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("MfaSecretKey")
                        .HasColumnType("text");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("NotificationPreferences")
                        .HasColumnType("text");

                    b.Property<Guid?>("OrganizationId")
                        .HasColumnType("uuid");

                    b.Property<string>("PasswordHash")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("PasswordHistory")
                        .IsRequired()
                        .HasColumnType("jsonb");

                    b.Property<DateTime>("PasswordLastChangedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("PasswordResetTokenExpiryUtc")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("PasswordResetTokenHash")
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)");

                    b.Property<string>("PhoneNumber")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("PreferredLanguage")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<string>("SsoProvider")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("SsoUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<int>("UserRole")
                        .HasColumnType("integer");

                    b.Property<string>("Username")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.HasIndex("Email")
                        .IsUnique();

                    b.HasIndex("OrganizationId");

                    b.HasIndex("Username")
                        .IsUnique();

                    b.ToTable("P2PUsers");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.Vendor", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CommercialRegistrationNumber")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("ContactEmail")
                        .HasMaxLength(254)
                        .HasColumnType("character varying(254)");

                    b.Property<string>("ContactName")
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)");

                    b.Property<string>("PhoneNumber")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Status")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasDefaultValue("Pending");

                    b.Property<string>("TaxId")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("VatNumber")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("VendorCode")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Website")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<uint>("xmin")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("CommercialRegistrationNumber");

                    b.HasIndex("Name");

                    b.HasIndex("Status");

                    b.HasIndex("TaxId");

                    b.HasIndex("VatNumber");

                    b.HasIndex("VendorCode")
                        .IsUnique()
                        .HasFilter("\"vendor_code\" IS NOT NULL");

                    b.ToTable("vendors", (string)null);
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.VendorProduct", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<int?>("LeadTimeDays")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<decimal?>("PackSize")
                        .HasPrecision(18, 4)
                        .HasColumnType("numeric(18,4)");

                    b.Property<Guid>("ProductDefinitionId")
                        .HasColumnType("uuid");

                    b.Property<string>("UnitOfMeasure")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<Guid>("VendorId")
                        .HasColumnType("uuid");

                    b.Property<string>("VendorSku")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.HasKey("Id");

                    b.HasIndex("ProductDefinitionId");

                    b.HasIndex("VendorId", "VendorSku")
                        .HasFilter("\"vendor_sku\" IS NOT NULL");

                    b.HasIndex("VendorId", "ProductDefinitionId", "UnitOfMeasure", "PackSize")
                        .IsUnique();

                    b.ToTable("vendor_products", (string)null);
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.VendorProposal", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("AlternatePaymentTerms")
                        .HasColumnType("text");

                    b.Property<string>("Comments")
                        .HasColumnType("text");

                    b.Property<string>("ComplianceCertificationsJson")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("PerformanceGuarantees")
                        .HasColumnType("text");

                    b.Property<Guid>("RequestForProposalId")
                        .HasColumnType("uuid");

                    b.Property<string>("RiskSharingClauses")
                        .HasColumnType("text");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("SubcontractorDisclosures")
                        .HasColumnType("text");

                    b.Property<DateTime>("SubmissionDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("SustainabilityCommitments")
                        .HasColumnType("text");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<decimal?>("TotalProposedValue")
                        .HasColumnType("numeric(18, 2)");

                    b.Property<DateTime?>("ValidityEndDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<int>("ValidityPeriodDays")
                        .HasColumnType("integer");

                    b.Property<string>("ValueAddedServices")
                        .HasColumnType("text");

                    b.Property<Guid>("VendorId")
                        .HasColumnType("uuid");

                    b.Property<int>("Version")
                        .HasColumnType("integer");

                    b.Property<uint>("xmin")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("RequestForProposalId");

                    b.HasIndex("Status");

                    b.HasIndex("SubmissionDate");

                    b.HasIndex("TenantId");

                    b.HasIndex("VendorId");

                    b.HasIndex("RequestForProposalId", "VendorId")
                        .IsUnique();

                    b.ToTable("vendor_proposals", (string)null);
                });

            modelBuilder.Entity("ProcureToPay.Domain.ValueObjects.DocumentLink", b =>
                {
                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("DocumentType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("Url")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.ToTable("DocumentLink");
                });

            modelBuilder.Entity("ProcureToPay.Infrastructure.Identity.ApplicationUser", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("integer");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("text");

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("boolean");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("boolean");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("NormalizedEmail")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("NormalizedUserName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("text");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("text");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("boolean");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("text");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("boolean");

                    b.Property<string>("UserName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedEmail")
                        .HasDatabaseName("EmailIndex");

                    b.HasIndex("NormalizedUserName")
                        .IsUnique()
                        .HasDatabaseName("UserNameIndex");

                    b.ToTable("AspNetUsers", (string)null);
                });

            modelBuilder.Entity("ProcureToPay.Infrastructure.Persistence.Entities.SqlMigrationHistory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("AppliedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ScriptName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("SqlMigrationHistories");
                });

            modelBuilder.Entity("sales_territory_representatives", b =>
                {
                    b.Property<Guid>("sales_territory_id")
                        .HasColumnType("uuid");

                    b.Property<string>("representative_id")
                        .HasColumnType("text");

                    b.HasKey("sales_territory_id", "representative_id");

                    b.HasIndex("representative_id");

                    b.ToTable("sales_territory_representatives", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.HasOne("ProcureToPay.Infrastructure.Identity.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.HasOne("ProcureToPay.Infrastructure.Identity.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ProcureToPay.Infrastructure.Identity.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.HasOne("ProcureToPay.Infrastructure.Identity.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.Budget", b =>
                {
                    b.OwnsOne("ProcureToPay.Domain.ValueObjects.Money", "BaselineAmount", b1 =>
                        {
                            b1.Property<Guid>("BudgetId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("numeric(18, 4)")
                                .HasColumnName("baseline_amount");

                            b1.HasKey("BudgetId");

                            b1.ToTable("budgets", "public");

                            b1.WithOwner()
                                .HasForeignKey("BudgetId");
                        });

                    b.OwnsOne("ProcureToPay.Domain.ValueObjects.Money", "ForecastAmount", b1 =>
                        {
                            b1.Property<Guid>("BudgetId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("numeric(18, 4)")
                                .HasColumnName("forecast_amount");

                            b1.HasKey("BudgetId");

                            b1.ToTable("budgets", "public");

                            b1.WithOwner()
                                .HasForeignKey("BudgetId");
                        });

                    b.Navigation("BaselineAmount")
                        .IsRequired();

                    b.Navigation("ForecastAmount");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.BudgetAllocation", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.Budget", "Budget")
                        .WithMany("BudgetAllocations")
                        .HasForeignKey("BudgetId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ProcureToPay.Domain.Entities.Department", "Department")
                        .WithMany("BudgetAllocations")
                        .HasForeignKey("DepartmentId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.OwnsOne("ProcureToPay.Domain.ValueObjects.Money", "AllocatedAmount", b1 =>
                        {
                            b1.Property<Guid>("BudgetAllocationId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("numeric(18, 2)")
                                .HasColumnName("allocated_amount");

                            b1.Property<string>("CurrencyCode")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("allocated_currency_code");

                            b1.HasKey("BudgetAllocationId");

                            b1.ToTable("budget_allocations", "public");

                            b1.WithOwner()
                                .HasForeignKey("BudgetAllocationId");
                        });

                    b.OwnsOne("ProcureToPay.Domain.ValueObjects.Money", "ConsumedAmount", b1 =>
                        {
                            b1.Property<Guid>("BudgetAllocationId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("numeric(18, 2)")
                                .HasDefaultValue(0m)
                                .HasColumnName("consumed_amount");

                            b1.Property<string>("CurrencyCode")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("consumed_currency_code");

                            b1.HasKey("BudgetAllocationId");

                            b1.ToTable("budget_allocations", "public");

                            b1.WithOwner()
                                .HasForeignKey("BudgetAllocationId");
                        });

                    b.Navigation("AllocatedAmount")
                        .IsRequired();

                    b.Navigation("Budget");

                    b.Navigation("ConsumedAmount")
                        .IsRequired();

                    b.Navigation("Department");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.Catalog.CatalogProduct", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.Catalog.ProductCategory", "Category")
                        .WithMany("CatalogProducts")
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Category");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.Catalog.ProductCategory", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.Catalog.ProductCategory", "ParentCategory")
                        .WithMany("Subcategories")
                        .HasForeignKey("ParentCategoryId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("ParentCategory");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.Category", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.Category", "ParentCategory")
                        .WithMany("ChildCategories")
                        .HasForeignKey("ParentCategoryId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("ParentCategory");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.Contract", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.Vendor", "Vendor")
                        .WithMany("Contracts")
                        .HasForeignKey("VendorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.OwnsOne("ProcureToPay.Domain.ValueObjects.Money", "TotalContractValue", b1 =>
                        {
                            b1.Property<Guid>("ContractId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasPrecision(18, 4)
                                .HasColumnType("numeric(18,4)")
                                .HasColumnName("total_contract_value_amount");

                            b1.Property<string>("CurrencyCode")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("total_contract_value_currency_code");

                            b1.HasKey("ContractId");

                            b1.ToTable("contracts");

                            b1.WithOwner()
                                .HasForeignKey("ContractId");
                        });

                    b.Navigation("TotalContractValue");

                    b.Navigation("Vendor");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.Customer", b =>
                {
                    b.OwnsOne("ProcureToPay.Domain.ValueObjects.Address", "BillingAddress", b1 =>
                        {
                            b1.Property<Guid>("CustomerId")
                                .HasColumnType("uuid");

                            b1.Property<string>("City")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("billing_city");

                            b1.Property<string>("Country")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("billing_country");

                            b1.Property<string>("PostalCode")
                                .IsRequired()
                                .HasMaxLength(20)
                                .HasColumnType("character varying(20)")
                                .HasColumnName("billing_postal_code");

                            b1.Property<string>("State")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("billing_state");

                            b1.Property<string>("Street")
                                .IsRequired()
                                .HasMaxLength(255)
                                .HasColumnType("character varying(255)")
                                .HasColumnName("billing_street");

                            b1.HasKey("CustomerId");

                            b1.ToTable("customers", "public");

                            b1.WithOwner()
                                .HasForeignKey("CustomerId");
                        });

                    b.OwnsOne("ProcureToPay.Domain.ValueObjects.Money", "CreditLimit", b1 =>
                        {
                            b1.Property<Guid>("CustomerId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("numeric(18, 2)")
                                .HasColumnName("credit_limit_amount");

                            b1.Property<string>("CurrencyCode")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("credit_limit_currency_code");

                            b1.HasKey("CustomerId");

                            b1.ToTable("customers", "public");

                            b1.WithOwner()
                                .HasForeignKey("CustomerId");
                        });

                    b.OwnsOne("ProcureToPay.Domain.ValueObjects.ContactPerson", "PrimaryContact", b1 =>
                        {
                            b1.Property<Guid>("CustomerId")
                                .HasColumnType("uuid");

                            b1.Property<string>("Email")
                                .HasMaxLength(254)
                                .HasColumnType("character varying(254)")
                                .HasColumnName("primary_contact_email");

                            b1.Property<string>("Name")
                                .IsRequired()
                                .HasMaxLength(150)
                                .HasColumnType("character varying(150)")
                                .HasColumnName("primary_contact_name");

                            b1.Property<string>("Phone")
                                .HasMaxLength(50)
                                .HasColumnType("character varying(50)")
                                .HasColumnName("primary_contact_phone");

                            b1.Property<string>("Role")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("primary_contact_role");

                            b1.HasKey("CustomerId");

                            b1.ToTable("customers", "public");

                            b1.WithOwner()
                                .HasForeignKey("CustomerId");
                        });

                    b.OwnsOne("ProcureToPay.Domain.ValueObjects.Address", "ShippingAddress", b1 =>
                        {
                            b1.Property<Guid>("CustomerId")
                                .HasColumnType("uuid");

                            b1.Property<string>("City")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("shipping_city");

                            b1.Property<string>("Country")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("shipping_country");

                            b1.Property<string>("PostalCode")
                                .IsRequired()
                                .HasMaxLength(20)
                                .HasColumnType("character varying(20)")
                                .HasColumnName("shipping_postal_code");

                            b1.Property<string>("State")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("shipping_state");

                            b1.Property<string>("Street")
                                .IsRequired()
                                .HasMaxLength(255)
                                .HasColumnType("character varying(255)")
                                .HasColumnName("shipping_street");

                            b1.HasKey("CustomerId");

                            b1.ToTable("customers", "public");

                            b1.WithOwner()
                                .HasForeignKey("CustomerId");
                        });

                    b.Navigation("BillingAddress")
                        .IsRequired();

                    b.Navigation("CreditLimit");

                    b.Navigation("PrimaryContact")
                        .IsRequired();

                    b.Navigation("ShippingAddress")
                        .IsRequired();
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.DeliveryNote", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.PurchaseOrder", "PurchaseOrder")
                        .WithMany("DeliveryNotes")
                        .HasForeignKey("PurchaseOrderId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ProcureToPay.Domain.Entities.SalesOrder", "SalesOrder")
                        .WithMany()
                        .HasForeignKey("SalesOrderId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ProcureToPay.Domain.Entities.Vendor", "Vendor")
                        .WithMany()
                        .HasForeignKey("VendorId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsOne("ProcureToPay.Domain.ValueObjects.Address", "BillingAddress", b1 =>
                        {
                            b1.Property<Guid>("DeliveryNoteId")
                                .HasColumnType("uuid");

                            b1.Property<string>("City")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("billing_city");

                            b1.Property<string>("Country")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("billing_country");

                            b1.Property<string>("PostalCode")
                                .HasMaxLength(20)
                                .HasColumnType("character varying(20)")
                                .HasColumnName("billing_postal_code");

                            b1.Property<string>("State")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("billing_state");

                            b1.Property<string>("Street")
                                .HasMaxLength(200)
                                .HasColumnType("character varying(200)")
                                .HasColumnName("billing_street");

                            b1.HasKey("DeliveryNoteId");

                            b1.ToTable("delivery_notes");

                            b1.WithOwner()
                                .HasForeignKey("DeliveryNoteId");
                        });

                    b.OwnsOne("ProcureToPay.Domain.ValueObjects.Address", "ShippingAddress", b1 =>
                        {
                            b1.Property<Guid>("DeliveryNoteId")
                                .HasColumnType("uuid");

                            b1.Property<string>("City")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("shipping_city");

                            b1.Property<string>("Country")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("shipping_country");

                            b1.Property<string>("PostalCode")
                                .HasMaxLength(20)
                                .HasColumnType("character varying(20)")
                                .HasColumnName("shipping_postal_code");

                            b1.Property<string>("State")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("shipping_state");

                            b1.Property<string>("Street")
                                .HasMaxLength(200)
                                .HasColumnType("character varying(200)")
                                .HasColumnName("shipping_street");

                            b1.HasKey("DeliveryNoteId");

                            b1.ToTable("delivery_notes");

                            b1.WithOwner()
                                .HasForeignKey("DeliveryNoteId");
                        });

                    b.OwnsOne("ProcureToPay.Domain.ValueObjects.CarrierInformation", "CarrierInfo", b1 =>
                        {
                            b1.Property<Guid>("DeliveryNoteId")
                                .HasColumnType("uuid");

                            b1.Property<string>("Name")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("carrier_name");

                            b1.Property<string>("TrackingNumber")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("tracking_number");

                            b1.HasKey("DeliveryNoteId");

                            b1.ToTable("delivery_notes");

                            b1.WithOwner()
                                .HasForeignKey("DeliveryNoteId");
                        });

                    b.Navigation("BillingAddress");

                    b.Navigation("CarrierInfo");

                    b.Navigation("PurchaseOrder");

                    b.Navigation("SalesOrder");

                    b.Navigation("ShippingAddress");

                    b.Navigation("Vendor");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.DeliveryNoteLine", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.DeliveryNote", "DeliveryNote")
                        .WithMany("Lines")
                        .HasForeignKey("DeliveryNoteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ProcureToPay.Domain.Entities.ProductDefinition", "Product")
                        .WithMany()
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ProcureToPay.Domain.Entities.PurchaseOrderLine", "PurchaseOrderLine")
                        .WithMany("DeliveryNoteLines")
                        .HasForeignKey("PurchaseOrderLineId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ProcureToPay.Domain.Entities.SalesOrderLine", "SalesOrderLine")
                        .WithMany()
                        .HasForeignKey("SalesOrderId", "SalesOrderLineNumber")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("DeliveryNote");

                    b.Navigation("Product");

                    b.Navigation("PurchaseOrderLine");

                    b.Navigation("SalesOrderLine");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.GoodsReceiptNote", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.DeliveryNote", "DeliveryNote")
                        .WithMany()
                        .HasForeignKey("DeliveryNoteId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("ProcureToPay.Domain.Entities.PurchaseOrder", "PurchaseOrder")
                        .WithMany("GoodsReceiptNotes")
                        .HasForeignKey("PurchaseOrderId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ProcureToPay.Infrastructure.Identity.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("ReceivedByUserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ProcureToPay.Domain.Entities.Vendor", "Vendor")
                        .WithMany()
                        .HasForeignKey("VendorId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DeliveryNote");

                    b.Navigation("PurchaseOrder");

                    b.Navigation("Vendor");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.GoodsReceiptNoteLine", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.GoodsReceiptNote", "GoodsReceiptNote")
                        .WithMany("Lines")
                        .HasForeignKey("GoodsReceiptNoteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ProcureToPay.Domain.Entities.ProductDefinition", "Product")
                        .WithMany()
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ProcureToPay.Domain.Entities.PurchaseOrderLine", "PurchaseOrderLine")
                        .WithMany("GoodsReceiptNoteLines")
                        .HasForeignKey("PurchaseOrderLineId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ProcureToPay.Domain.Entities.DeliveryNoteLine", "DeliveryNoteLine")
                        .WithMany()
                        .HasForeignKey("DeliveryNoteLineDeliveryNoteId", "DeliveryNoteLineLineNumber");

                    b.Navigation("DeliveryNoteLine");

                    b.Navigation("GoodsReceiptNote");

                    b.Navigation("Product");

                    b.Navigation("PurchaseOrderLine");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.Invoice", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.Customer", "Customer")
                        .WithMany("Invoices")
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ProcureToPay.Domain.Entities.PurchaseOrder", "PurchaseOrder")
                        .WithMany("Invoices")
                        .HasForeignKey("PurchaseOrderId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ProcureToPay.Domain.Entities.Vendor", "Vendor")
                        .WithMany("Invoices")
                        .HasForeignKey("VendorId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Customer");

                    b.Navigation("PurchaseOrder");

                    b.Navigation("Vendor");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.InvoiceLine", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.Invoice", "Invoice")
                        .WithMany("Lines")
                        .HasForeignKey("InvoiceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ProcureToPay.Domain.Entities.ProductDefinition", "Product")
                        .WithMany()
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ProcureToPay.Domain.Entities.PurchaseOrderLine", "PurchaseOrderLine")
                        .WithMany("InvoiceLines")
                        .HasForeignKey("PurchaseOrderLineId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Invoice");

                    b.Navigation("Product");

                    b.Navigation("PurchaseOrderLine");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.PaymentTransaction", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.Invoice", "Invoice")
                        .WithMany("PaymentTransactions")
                        .HasForeignKey("InvoiceId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("ProcureToPay.Domain.Entities.Vendor", "Vendor")
                        .WithMany("PaymentTransactions")
                        .HasForeignKey("VendorId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Invoice");

                    b.Navigation("Vendor");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.ProcurementSpeckleLink", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.User", "LinkedByUser")
                        .WithMany()
                        .HasForeignKey("LinkedByUserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ProcureToPay.Domain.Entities.SpeckleObjectMetadata", "SpeckleObjectMetadata")
                        .WithMany()
                        .HasForeignKey("SpeckleObjectMetadataId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("LinkedByUser");

                    b.Navigation("SpeckleObjectMetadata");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.ProcurementWorkflowStep", b =>
                {
                    b.HasOne("ProcureToPay.Infrastructure.Identity.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("ApproverUserId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ProcureToPay.Domain.Entities.ProcurementWorkflow", "ProcurementWorkflow")
                        .WithMany("Steps")
                        .HasForeignKey("ProcurementWorkflowId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ProcureToPay.Domain.Entities.ProcurementWorkflow", "Workflow")
                        .WithMany()
                        .HasForeignKey("WorkflowId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ProcurementWorkflow");

                    b.Navigation("Workflow");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.Product", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.Category", "Category")
                        .WithMany("Products")
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Category");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.ProductDefinition", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.Category", "Category")
                        .WithMany("ProductDefinitions")
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Category");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.Project", b =>
                {
                    b.OwnsOne("ProcureToPay.Domain.ValueObjects.Money", "BudgetAmount", b1 =>
                        {
                            b1.Property<Guid>("ProjectId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasPrecision(18, 2)
                                .HasColumnType("numeric(18,2)")
                                .HasColumnName("budget_amount");

                            b1.Property<string>("CurrencyCode")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("budget_currency_code");

                            b1.HasKey("ProjectId");

                            b1.ToTable("projects", "public");

                            b1.WithOwner()
                                .HasForeignKey("ProjectId");
                        });

                    b.Navigation("BudgetAmount");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.PurchaseOrder", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.Contract", "Contract")
                        .WithMany("PurchaseOrders")
                        .HasForeignKey("ContractId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("ProcureToPay.Domain.Entities.PurchaseRequisition", "Requisition")
                        .WithMany("PurchaseOrders")
                        .HasForeignKey("RequisitionId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("ProcureToPay.Domain.Entities.Vendor", "Vendor")
                        .WithMany("PurchaseOrders")
                        .HasForeignKey("VendorId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.OwnsOne("ProcureToPay.Domain.ValueObjects.Address", "ShipmentAddress", b1 =>
                        {
                            b1.Property<Guid>("PurchaseOrderId")
                                .HasColumnType("uuid");

                            b1.Property<string>("City")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("shipment_city");

                            b1.Property<string>("Country")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("shipment_country");

                            b1.Property<string>("PostalCode")
                                .IsRequired()
                                .HasMaxLength(20)
                                .HasColumnType("character varying(20)")
                                .HasColumnName("shipment_postal_code");

                            b1.Property<string>("State")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("shipment_state");

                            b1.Property<string>("Street")
                                .IsRequired()
                                .HasMaxLength(200)
                                .HasColumnType("character varying(200)")
                                .HasColumnName("shipment_street");

                            b1.HasKey("PurchaseOrderId");

                            b1.ToTable("purchase_orders");

                            b1.WithOwner()
                                .HasForeignKey("PurchaseOrderId");
                        });

                    b.OwnsOne("ProcureToPay.Domain.ValueObjects.Money", "TotalAmount", b1 =>
                        {
                            b1.Property<Guid>("PurchaseOrderId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasPrecision(18, 4)
                                .HasColumnType("numeric(18,4)")
                                .HasColumnName("total_amount");

                            b1.HasKey("PurchaseOrderId");

                            b1.ToTable("purchase_orders");

                            b1.WithOwner()
                                .HasForeignKey("PurchaseOrderId");
                        });

                    b.Navigation("Contract");

                    b.Navigation("Requisition");

                    b.Navigation("ShipmentAddress")
                        .IsRequired();

                    b.Navigation("TotalAmount")
                        .IsRequired();

                    b.Navigation("Vendor");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.PurchaseOrderLine", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.PurchaseOrder", "PurchaseOrder")
                        .WithMany("Lines")
                        .HasForeignKey("PurchaseOrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ProcureToPay.Domain.Entities.VendorProduct", "VendorProduct")
                        .WithMany("PurchaseOrderLines")
                        .HasForeignKey("VendorProductId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.OwnsOne("ProcureToPay.Domain.ValueObjects.Money", "LineTotal", b1 =>
                        {
                            b1.Property<Guid>("PurchaseOrderLineId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasPrecision(18, 4)
                                .HasColumnType("numeric(18,4)")
                                .HasColumnName("line_total_amount");

                            b1.Property<string>("CurrencyCode")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("line_total_currency_code");

                            b1.HasKey("PurchaseOrderLineId");

                            b1.ToTable("purchase_order_lines");

                            b1.WithOwner()
                                .HasForeignKey("PurchaseOrderLineId");
                        });

                    b.OwnsOne("ProcureToPay.Domain.ValueObjects.Money", "UnitPriceSnapshot", b1 =>
                        {
                            b1.Property<Guid>("PurchaseOrderLineId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasPrecision(18, 4)
                                .HasColumnType("numeric(18,4)")
                                .HasColumnName("unit_price_amount");

                            b1.Property<string>("CurrencyCode")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("unit_price_currency_code");

                            b1.HasKey("PurchaseOrderLineId");

                            b1.ToTable("purchase_order_lines");

                            b1.WithOwner()
                                .HasForeignKey("PurchaseOrderLineId");
                        });

                    b.Navigation("LineTotal")
                        .IsRequired();

                    b.Navigation("PurchaseOrder");

                    b.Navigation("UnitPriceSnapshot")
                        .IsRequired();

                    b.Navigation("VendorProduct");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.PurchaseRequisition", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.PurchaseOrder", "AssociatedPurchaseOrder")
                        .WithMany()
                        .HasForeignKey("AssociatedPurchaseOrderId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.OwnsOne("ProcureToPay.Domain.ValueObjects.Address", "ShippingAddress", b1 =>
                        {
                            b1.Property<Guid>("PurchaseRequisitionId")
                                .HasColumnType("uuid");

                            b1.Property<string>("City")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("shipping_city");

                            b1.Property<string>("Country")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("shipping_country");

                            b1.Property<string>("PostalCode")
                                .IsRequired()
                                .HasMaxLength(20)
                                .HasColumnType("character varying(20)")
                                .HasColumnName("shipping_postal_code");

                            b1.Property<string>("State")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("shipping_state");

                            b1.Property<string>("Street")
                                .IsRequired()
                                .HasMaxLength(200)
                                .HasColumnType("character varying(200)")
                                .HasColumnName("shipping_street");

                            b1.HasKey("PurchaseRequisitionId");

                            b1.ToTable("purchase_requisitions");

                            b1.WithOwner()
                                .HasForeignKey("PurchaseRequisitionId");
                        });

                    b.OwnsOne("ProcureToPay.Domain.ValueObjects.Money", "TotalEstimatedCost", b1 =>
                        {
                            b1.Property<Guid>("PurchaseRequisitionId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasPrecision(18, 4)
                                .HasColumnType("numeric(18,4)")
                                .HasColumnName("total_estimated_cost_amount");

                            b1.HasKey("PurchaseRequisitionId");

                            b1.ToTable("purchase_requisitions");

                            b1.WithOwner()
                                .HasForeignKey("PurchaseRequisitionId");
                        });

                    b.Navigation("AssociatedPurchaseOrder");

                    b.Navigation("ShippingAddress");

                    b.Navigation("TotalEstimatedCost")
                        .IsRequired();
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.PurchaseRequisitionLine", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.ProductDefinition", "ProductDefinition")
                        .WithMany()
                        .HasForeignKey("ProductDefinitionId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("ProcureToPay.Domain.Entities.PurchaseRequisition", "PurchaseRequisition")
                        .WithMany("Lines")
                        .HasForeignKey("PurchaseRequisitionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ProcureToPay.Domain.Entities.Vendor", "SuggestedVendor")
                        .WithMany()
                        .HasForeignKey("SuggestedVendorId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("ProcureToPay.Domain.Entities.VendorProduct", "VendorProduct")
                        .WithMany()
                        .HasForeignKey("VendorProductId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.OwnsOne("ProcureToPay.Domain.ValueObjects.Money", "EstimatedLineCost", b1 =>
                        {
                            b1.Property<Guid>("PurchaseRequisitionLineId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasPrecision(18, 4)
                                .HasColumnType("numeric(18,4)")
                                .HasColumnName("estimated_line_cost_amount");

                            b1.Property<string>("CurrencyCode")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("estimated_line_cost_currency_code");

                            b1.HasKey("PurchaseRequisitionLineId");

                            b1.ToTable("purchase_requisition_lines");

                            b1.WithOwner()
                                .HasForeignKey("PurchaseRequisitionLineId");
                        });

                    b.OwnsOne("ProcureToPay.Domain.ValueObjects.Money", "EstimatedUnitPrice", b1 =>
                        {
                            b1.Property<Guid>("PurchaseRequisitionLineId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasPrecision(18, 4)
                                .HasColumnType("numeric(18,4)")
                                .HasColumnName("estimated_unit_price_amount");

                            b1.Property<string>("CurrencyCode")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("estimated_unit_price_currency_code");

                            b1.HasKey("PurchaseRequisitionLineId");

                            b1.ToTable("purchase_requisition_lines");

                            b1.WithOwner()
                                .HasForeignKey("PurchaseRequisitionLineId");
                        });

                    b.Navigation("EstimatedLineCost");

                    b.Navigation("EstimatedUnitPrice");

                    b.Navigation("ProductDefinition");

                    b.Navigation("PurchaseRequisition");

                    b.Navigation("SuggestedVendor");

                    b.Navigation("VendorProduct");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.RequestForInformation", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.Project", "Project")
                        .WithMany("RequestsForInformation")
                        .HasForeignKey("ProjectId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Project");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.RequestForProposal", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.Contract", "AwardedContract")
                        .WithMany()
                        .HasForeignKey("AwardedContractId");

                    b.HasOne("ProcureToPay.Domain.Entities.Vendor", "AwardedVendor")
                        .WithMany()
                        .HasForeignKey("AwardedVendorId");

                    b.HasOne("ProcureToPay.Domain.Entities.Project", "Project")
                        .WithMany("RequestsForProposal")
                        .HasForeignKey("ProjectId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("AwardedContract");

                    b.Navigation("AwardedVendor");

                    b.Navigation("Project");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.RequestForQuote", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.Vendor", "AwardedVendor")
                        .WithMany()
                        .HasForeignKey("AwardedVendorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ProcureToPay.Domain.Entities.PurchaseRequisition", "OriginatingRequisition")
                        .WithMany()
                        .HasForeignKey("OriginatingRequisitionId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("ProcureToPay.Domain.Entities.Contract", "RelatedAgreement")
                        .WithMany()
                        .HasForeignKey("RelatedAgreementId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.OwnsOne("ProcureToPay.Domain.ValueObjects.Address", "DeliverToAddress", b1 =>
                        {
                            b1.Property<Guid>("RequestForQuoteId")
                                .HasColumnType("uuid");

                            b1.Property<string>("City")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("deliver_to_city");

                            b1.Property<string>("Country")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("deliver_to_country");

                            b1.Property<string>("PostalCode")
                                .IsRequired()
                                .HasMaxLength(20)
                                .HasColumnType("character varying(20)")
                                .HasColumnName("deliver_to_postal_code");

                            b1.Property<string>("State")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("deliver_to_state");

                            b1.Property<string>("Street")
                                .IsRequired()
                                .HasMaxLength(200)
                                .HasColumnType("character varying(200)")
                                .HasColumnName("deliver_to_street");

                            b1.HasKey("RequestForQuoteId");

                            b1.ToTable("request_for_quotes", "public");

                            b1.WithOwner()
                                .HasForeignKey("RequestForQuoteId");
                        });

                    b.Navigation("AwardedVendor");

                    b.Navigation("DeliverToAddress")
                        .IsRequired();

                    b.Navigation("OriginatingRequisition");

                    b.Navigation("RelatedAgreement");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.RequestForQuoteLine", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.ProductDefinition", "ProductDefinition")
                        .WithMany()
                        .HasForeignKey("ProductDefinitionId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("ProcureToPay.Domain.Entities.RequestForQuote", "RequestForQuote")
                        .WithMany("Lines")
                        .HasForeignKey("RequestForQuoteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ProcureToPay.Domain.Entities.VendorProduct", "VendorProduct")
                        .WithMany()
                        .HasForeignKey("VendorProductId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.OwnsOne("ProcureToPay.Domain.ValueObjects.Money", "EstimatedTcoValue", b1 =>
                        {
                            b1.Property<Guid>("RequestForQuoteLineId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasPrecision(18, 4)
                                .HasColumnType("numeric(18,4)")
                                .HasColumnName("est_tco_value_amount");

                            b1.Property<string>("CurrencyCode")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("est_tco_value_currency_code");

                            b1.HasKey("RequestForQuoteLineId");

                            b1.ToTable("request_for_quote_lines", "public");

                            b1.WithOwner()
                                .HasForeignKey("RequestForQuoteLineId");
                        });

                    b.OwnsOne("ProcureToPay.Domain.ValueObjects.Money", "TargetUnitPrice", b1 =>
                        {
                            b1.Property<Guid>("RequestForQuoteLineId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasPrecision(18, 4)
                                .HasColumnType("numeric(18,4)")
                                .HasColumnName("target_unit_price_amount");

                            b1.Property<string>("CurrencyCode")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("target_unit_price_currency_code");

                            b1.HasKey("RequestForQuoteLineId");

                            b1.ToTable("request_for_quote_lines", "public");

                            b1.WithOwner()
                                .HasForeignKey("RequestForQuoteLineId");
                        });

                    b.Navigation("EstimatedTcoValue");

                    b.Navigation("ProductDefinition");

                    b.Navigation("RequestForQuote");

                    b.Navigation("TargetUnitPrice");

                    b.Navigation("VendorProduct");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.ReturnAuthorization", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.Customer", "Customer")
                        .WithMany("ReturnAuthorizations")
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ProcureToPay.Domain.Entities.Invoice", "Invoice")
                        .WithMany()
                        .HasForeignKey("InvoiceId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ProcureToPay.Domain.Entities.SalesOrder", "SalesOrder")
                        .WithMany()
                        .HasForeignKey("SalesOrderId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Customer");

                    b.Navigation("Invoice");

                    b.Navigation("SalesOrder");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.ReturnAuthorizationLine", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.ProductDefinition", "ProductDefinition")
                        .WithMany()
                        .HasForeignKey("ProductDefinitionId");

                    b.HasOne("ProcureToPay.Domain.Entities.Product", "Product")
                        .WithMany()
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ProcureToPay.Domain.Entities.ReturnAuthorization", "ReturnAuthorization")
                        .WithMany("Lines")
                        .HasForeignKey("ReturnAuthorizationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ProcureToPay.Domain.Entities.VendorProduct", "VendorProduct")
                        .WithMany()
                        .HasForeignKey("VendorProductId");

                    b.HasOne("ProcureToPay.Domain.Entities.InvoiceLine", "InvoiceLine")
                        .WithMany()
                        .HasForeignKey("InvoiceId", "InvoiceLineNumber")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ProcureToPay.Domain.Entities.SalesOrderLine", "OriginalSalesOrderLine")
                        .WithMany()
                        .HasForeignKey("OriginalSalesOrderLineSalesOrderId", "OriginalSalesOrderLineLineNumber");

                    b.HasOne("ProcureToPay.Domain.Entities.SalesOrderLine", "SalesOrderLine")
                        .WithMany()
                        .HasForeignKey("SalesOrderId", "SalesOrderLineNumber")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("InvoiceLine");

                    b.Navigation("OriginalSalesOrderLine");

                    b.Navigation("Product");

                    b.Navigation("ProductDefinition");

                    b.Navigation("ReturnAuthorization");

                    b.Navigation("SalesOrderLine");

                    b.Navigation("VendorProduct");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.RolePermission", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.Role", "Role")
                        .WithMany("RolePermissions")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Role");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.SalesOrder", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.Customer", "Customer")
                        .WithMany("SalesOrders")
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ProcureToPay.Domain.Entities.ReturnAuthorization", "ReturnAuthorization")
                        .WithMany()
                        .HasForeignKey("RelatedReturnAuthorizationId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("ProcureToPay.Domain.Entities.SalesTerritory", "SalesTerritory")
                        .WithMany()
                        .HasForeignKey("SalesTerritoryId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("ProcureToPay.Domain.Entities.SalesTerritory", null)
                        .WithMany("SalesOrders")
                        .HasForeignKey("SalesTerritoryId1");

                    b.OwnsOne("ProcureToPay.Domain.ValueObjects.Address", "BillingAddress", b1 =>
                        {
                            b1.Property<Guid>("SalesOrderId")
                                .HasColumnType("uuid");

                            b1.Property<string>("City")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("billing_city");

                            b1.Property<string>("Country")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("billing_country");

                            b1.Property<string>("PostalCode")
                                .IsRequired()
                                .HasMaxLength(20)
                                .HasColumnType("character varying(20)")
                                .HasColumnName("billing_postal_code");

                            b1.Property<string>("State")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("billing_state");

                            b1.Property<string>("Street")
                                .IsRequired()
                                .HasMaxLength(200)
                                .HasColumnType("character varying(200)")
                                .HasColumnName("billing_street");

                            b1.HasKey("SalesOrderId");

                            b1.ToTable("sales_orders", "public");

                            b1.WithOwner()
                                .HasForeignKey("SalesOrderId");
                        });

                    b.OwnsOne("ProcureToPay.Domain.ValueObjects.Address", "ShippingAddress", b1 =>
                        {
                            b1.Property<Guid>("SalesOrderId")
                                .HasColumnType("uuid");

                            b1.Property<string>("City")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("shipping_city");

                            b1.Property<string>("Country")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("shipping_country");

                            b1.Property<string>("PostalCode")
                                .IsRequired()
                                .HasMaxLength(20)
                                .HasColumnType("character varying(20)")
                                .HasColumnName("shipping_postal_code");

                            b1.Property<string>("State")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("shipping_state");

                            b1.Property<string>("Street")
                                .IsRequired()
                                .HasMaxLength(200)
                                .HasColumnType("character varying(200)")
                                .HasColumnName("shipping_street");

                            b1.HasKey("SalesOrderId");

                            b1.ToTable("sales_orders", "public");

                            b1.WithOwner()
                                .HasForeignKey("SalesOrderId");
                        });

                    b.OwnsOne("ProcureToPay.Domain.ValueObjects.Money", "TotalAmount", b1 =>
                        {
                            b1.Property<Guid>("SalesOrderId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasPrecision(18, 4)
                                .HasColumnType("numeric(18,4)")
                                .HasColumnName("total_amount");

                            b1.HasKey("SalesOrderId");

                            b1.ToTable("sales_orders", "public");

                            b1.WithOwner()
                                .HasForeignKey("SalesOrderId");
                        });

                    b.Navigation("BillingAddress")
                        .IsRequired();

                    b.Navigation("Customer");

                    b.Navigation("ReturnAuthorization");

                    b.Navigation("SalesTerritory");

                    b.Navigation("ShippingAddress")
                        .IsRequired();

                    b.Navigation("TotalAmount")
                        .IsRequired();
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.SalesOrderLine", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.Product", "Product")
                        .WithMany()
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ProcureToPay.Domain.Entities.Project", null)
                        .WithMany("SalesOrderLines")
                        .HasForeignKey("ProjectId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("ProcureToPay.Domain.Entities.Project", "Project")
                        .WithMany()
                        .HasForeignKey("ProjectId1");

                    b.HasOne("ProcureToPay.Domain.Entities.SalesOrder", "SalesOrder")
                        .WithMany("Lines")
                        .HasForeignKey("SalesOrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ProcureToPay.Domain.Entities.VendorProduct", "VendorProduct")
                        .WithMany()
                        .HasForeignKey("VendorProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ProcureToPay.Domain.Entities.SalesOrderLine", "ParentSalesOrderLine")
                        .WithMany("ChildSalesOrderLines")
                        .HasForeignKey("ParentSalesOrderLineSalesOrderId", "ParentSalesOrderLineLineNumber");

                    b.Navigation("ParentSalesOrderLine");

                    b.Navigation("Product");

                    b.Navigation("Project");

                    b.Navigation("SalesOrder");

                    b.Navigation("VendorProduct");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.SalesTerritory", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.SalesTerritory", "ParentTerritory")
                        .WithMany("ChildTerritories")
                        .HasForeignKey("ParentTerritoryId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("ParentTerritory");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.SpeckleObjectMetadata", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.SpeckleProjectLink", "SpeckleProjectLink")
                        .WithMany()
                        .HasForeignKey("SpeckleProjectLinkId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("SpeckleProjectLink");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.SpeckleProjectLink", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.Project", "Project")
                        .WithMany()
                        .HasForeignKey("P2PProjectId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Project");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.SubmittalReview", b =>
                {
                    b.HasOne("ProcureToPay.Infrastructure.Identity.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("ReviewerId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ProcureToPay.Domain.Entities.TechnicalSubmittal", "TechnicalSubmittal")
                        .WithMany("Reviews")
                        .HasForeignKey("TechnicalSubmittalId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ProcureToPay.Domain.Entities.TechnicalSubmittal", null)
                        .WithMany("ReviewHistory")
                        .HasForeignKey("TechnicalSubmittalId1");

                    b.Navigation("TechnicalSubmittal");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.Supplier", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.Vendor", "Vendor")
                        .WithOne("SupplierProfile")
                        .HasForeignKey("ProcureToPay.Domain.Entities.Supplier", "VendorId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsMany("ProcureToPay.Domain.ValueObjects.ContactPerson", "EmergencyContacts", b1 =>
                        {
                            b1.Property<Guid>("SupplierId")
                                .HasColumnType("uuid");

                            b1.Property<int>("__synthesizedOrdinal")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("integer");

                            b1.Property<string>("Email")
                                .HasColumnType("text");

                            b1.Property<int?>("HierarchyLevel")
                                .HasColumnType("integer");

                            b1.Property<string>("Name")
                                .IsRequired()
                                .HasColumnType("text");

                            b1.Property<string>("Phone")
                                .HasColumnType("text");

                            b1.Property<string>("Role")
                                .HasColumnType("text");

                            b1.HasKey("SupplierId", "__synthesizedOrdinal");

                            b1.ToTable("suppliers");

                            b1.ToJson("EmergencyContacts");

                            b1.WithOwner()
                                .HasForeignKey("SupplierId");
                        });

                    b.Navigation("EmergencyContacts");

                    b.Navigation("Vendor");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.TechnicalSubmittal", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.Contract", "Contract")
                        .WithMany()
                        .HasForeignKey("ContractId");

                    b.HasOne("ProcureToPay.Domain.Entities.Project", "Project")
                        .WithMany("TechnicalSubmittals")
                        .HasForeignKey("ProjectId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ProcureToPay.Domain.Entities.PurchaseOrderLine", "PurchaseOrderLine")
                        .WithMany()
                        .HasForeignKey("PurchaseOrderLineId");

                    b.HasOne("ProcureToPay.Infrastructure.Identity.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("SubmittedByUserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ProcureToPay.Domain.Entities.Vendor", "Vendor")
                        .WithMany()
                        .HasForeignKey("VendorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Contract");

                    b.Navigation("Project");

                    b.Navigation("PurchaseOrderLine");

                    b.Navigation("Vendor");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.Vendor", b =>
                {
                    b.OwnsOne("ProcureToPay.Domain.ValueObjects.Address", "PrimaryAddress", b1 =>
                        {
                            b1.Property<Guid>("VendorId")
                                .HasColumnType("uuid");

                            b1.Property<string>("City")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("primary_address_city");

                            b1.Property<string>("Country")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("primary_address_country");

                            b1.Property<string>("PostalCode")
                                .IsRequired()
                                .HasMaxLength(20)
                                .HasColumnType("character varying(20)")
                                .HasColumnName("primary_address_postal_code");

                            b1.Property<string>("State")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("primary_address_state");

                            b1.Property<string>("Street")
                                .IsRequired()
                                .HasMaxLength(200)
                                .HasColumnType("character varying(200)")
                                .HasColumnName("primary_address_street");

                            b1.HasKey("VendorId");

                            b1.ToTable("vendors");

                            b1.WithOwner()
                                .HasForeignKey("VendorId");
                        });

                    b.Navigation("PrimaryAddress")
                        .IsRequired();
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.VendorProduct", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.ProductDefinition", "ProductDefinition")
                        .WithMany("VendorProducts")
                        .HasForeignKey("ProductDefinitionId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ProcureToPay.Domain.Entities.Vendor", "Vendor")
                        .WithMany("VendorProducts")
                        .HasForeignKey("VendorId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.OwnsOne("ProcureToPay.Domain.ValueObjects.Money", "UnitPrice", b1 =>
                        {
                            b1.Property<Guid>("VendorProductId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasPrecision(18, 4)
                                .HasColumnType("numeric(18,4)")
                                .HasColumnName("unit_price_amount");

                            b1.Property<string>("CurrencyCode")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("unit_price_currency_code");

                            b1.HasKey("VendorProductId");

                            b1.ToTable("vendor_products");

                            b1.WithOwner()
                                .HasForeignKey("VendorProductId");
                        });

                    b.Navigation("ProductDefinition");

                    b.Navigation("UnitPrice")
                        .IsRequired();

                    b.Navigation("Vendor");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.VendorProposal", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.RequestForProposal", "RequestForProposal")
                        .WithMany("VendorProposals")
                        .HasForeignKey("RequestForProposalId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ProcureToPay.Domain.Entities.Vendor", "Vendor")
                        .WithMany("VendorProposals")
                        .HasForeignKey("VendorId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("RequestForProposal");

                    b.Navigation("Vendor");
                });

            modelBuilder.Entity("sales_territory_representatives", b =>
                {
                    b.HasOne("ProcureToPay.Infrastructure.Identity.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("representative_id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ProcureToPay.Domain.Entities.SalesTerritory", null)
                        .WithMany()
                        .HasForeignKey("sales_territory_id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.Budget", b =>
                {
                    b.Navigation("BudgetAllocations");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.Catalog.ProductCategory", b =>
                {
                    b.Navigation("CatalogProducts");

                    b.Navigation("Subcategories");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.Category", b =>
                {
                    b.Navigation("ChildCategories");

                    b.Navigation("ProductDefinitions");

                    b.Navigation("Products");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.Contract", b =>
                {
                    b.Navigation("PurchaseOrders");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.Customer", b =>
                {
                    b.Navigation("Invoices");

                    b.Navigation("ReturnAuthorizations");

                    b.Navigation("SalesOrders");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.DeliveryNote", b =>
                {
                    b.Navigation("Lines");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.Department", b =>
                {
                    b.Navigation("BudgetAllocations");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.GoodsReceiptNote", b =>
                {
                    b.Navigation("Lines");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.Invoice", b =>
                {
                    b.Navigation("Lines");

                    b.Navigation("PaymentTransactions");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.ProcurementWorkflow", b =>
                {
                    b.Navigation("Steps");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.ProductDefinition", b =>
                {
                    b.Navigation("VendorProducts");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.Project", b =>
                {
                    b.Navigation("RequestsForInformation");

                    b.Navigation("RequestsForProposal");

                    b.Navigation("SalesOrderLines");

                    b.Navigation("TechnicalSubmittals");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.PurchaseOrder", b =>
                {
                    b.Navigation("DeliveryNotes");

                    b.Navigation("GoodsReceiptNotes");

                    b.Navigation("Invoices");

                    b.Navigation("Lines");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.PurchaseOrderLine", b =>
                {
                    b.Navigation("DeliveryNoteLines");

                    b.Navigation("GoodsReceiptNoteLines");

                    b.Navigation("InvoiceLines");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.PurchaseRequisition", b =>
                {
                    b.Navigation("Lines");

                    b.Navigation("PurchaseOrders");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.RequestForProposal", b =>
                {
                    b.Navigation("VendorProposals");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.RequestForQuote", b =>
                {
                    b.Navigation("Lines");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.ReturnAuthorization", b =>
                {
                    b.Navigation("Lines");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.Role", b =>
                {
                    b.Navigation("RolePermissions");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.SalesOrder", b =>
                {
                    b.Navigation("Lines");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.SalesOrderLine", b =>
                {
                    b.Navigation("ChildSalesOrderLines");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.SalesTerritory", b =>
                {
                    b.Navigation("ChildTerritories");

                    b.Navigation("SalesOrders");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.TechnicalSubmittal", b =>
                {
                    b.Navigation("ReviewHistory");

                    b.Navigation("Reviews");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.Vendor", b =>
                {
                    b.Navigation("Contracts");

                    b.Navigation("Invoices");

                    b.Navigation("PaymentTransactions");

                    b.Navigation("PurchaseOrders");

                    b.Navigation("SupplierProfile");

                    b.Navigation("VendorProducts");

                    b.Navigation("VendorProposals");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.VendorProduct", b =>
                {
                    b.Navigation("PurchaseOrderLines");
                });
#pragma warning restore 612, 618
        }
    }
}
