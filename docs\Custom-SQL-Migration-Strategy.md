# Custom SQL Migration Strategy

This document outlines the custom SQL script execution mechanism implemented alongside EF Core migrations for the ProcureToPay project. This hybrid approach is inspired by nopCommerce's strategy and provides flexibility for data migrations and complex schema adjustments.

## Overview

While EF Core migrations handle standard schema changes, the custom SQL migration system allows for:
- Data seeding and manipulation.
- Complex schema changes not easily expressed with EF Core.
- Execution of arbitrary SQL scripts during the deployment process.

Scripts are executed in alphabetical order and their execution is tracked to prevent re-application.

## Components

1.  **`SqlMigrationHistory.cs` Entity**:
    *   **Location**: `ProcureToPay.Infrastructure/Persistence/Entities/SqlMigrationHistory.cs`
    *   **Purpose**: An EF Core entity that tracks which custom SQL migration scripts have been applied to the database.
    *   **Fields**: `Id`, `ScriptName` (name of the .sql file without extension), `AppliedDate`.
    *   **Database Table**: This entity maps to the `SqlMigrationHistories` table, created via an EF Core migration (`AddSqlMigrationHistoryTable`).

2.  **`SqlMigrationService.cs`**:
    *   **Location**: `ProcureToPay.Infrastructure/Services/SqlMigrationService.cs`
    *   **Interface**: `ISqlMigrationService`
    *   **Purpose**: This service is responsible for discovering, executing, and tracking custom SQL scripts.
    *   **Functionality**:
        *   Scans the `Migrations/Scripts/` directory for `.sql` files.
        *   Compares found scripts against the `SqlMigrationHistory` table.
        *   Executes pending scripts in alphabetical order.
        *   Records successfully executed scripts in the `SqlMigrationHistory` table.

3.  **`Migrations/Scripts/` Directory**:
    *   **Location**: `ProcureToPay.Infrastructure/Migrations/Scripts/`
    *   **Purpose**: This directory stores all custom SQL migration files.
    *   **Naming Convention**: Files must end with `.sql`. It is highly recommended to prefix them with a timestamp or a sequential number to control execution order (e.g., `20240101000000_MyCustomDataMigration.sql` or `001_InitialDataSeed.sql`).

4.  **Integration into Application Startup (`Program.cs`)**:
    *   **Location**: `ProcureToPay.WebApp/ProcureToPay.WebApp/Program.cs`
    *   **Details**:
        *   `SqlMigrationService` is registered as a scoped service (`ISqlMigrationService`).
        *   Its `ApplyMigrationsAsync()` method is called during application startup, immediately after EF Core migrations are successfully applied and before any application-level data seeding.

## How to Use

1.  **Create a SQL Script**:
    *   Author a new `.sql` file containing your SQL commands (compatible with PostgreSQL).
    *   Place this file in the `ProcureToPay.Infrastructure/Migrations/Scripts/` directory.
    *   **Important**: Name the file using a timestamp prefix (e.g., `YYYYMMDDHHMMSS_DescriptiveName.sql`) or a numerical prefix to ensure correct execution order.

2.  **Ensure Idempotency**:
    *   Write your SQL scripts to be idempotent. This means they can be run multiple times without causing errors or corrupting data (e.g., use `CREATE TABLE IF NOT EXISTS`, check for existing data before `INSERT`).

3.  **Run the Application**:
    *   The next time the application starts, the `SqlMigrationService` will automatically detect and execute any new, unapplied SQL scripts.

## Recommendations

*   **Version Control**: Always commit your custom SQL scripts to version control along with EF Core migrations.
*   **Testing**: Thoroughly test your SQL scripts in a development or staging environment before deploying to production.
*   **Backup**: Always back up your database before applying any migrations (EF Core or custom SQL) in a production environment.
*   **Transaction Management**: The `SqlMigrationService` executes each script within its own database transaction. If a script fails, its transaction is rolled back, and an error is logged. Subsequent scripts will not be processed after a failure.
