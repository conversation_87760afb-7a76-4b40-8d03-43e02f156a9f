using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProcureToPay.Domain.Entities; // Assuming PO, POLine, Vendor, Contract, Requisition entities exist
using ProcureToPay.Domain.ValueObjects; // Assuming Address, Money VOs exist
using ProcureToPay.Domain.Enums; // Assuming PurchaseOrderStatus enum exists
using System;

namespace ProcureToPay.Infrastructure.Persistence.Configurations
{
    /// <summary>
    /// EF Core Configuration for the PurchaseOrder entity.
    /// Configures TPT inheritance, Value Objects, relationships, concurrency, and indexes.
    /// </summary>
    public class PurchaseOrderConfiguration : IEntityTypeConfiguration<PurchaseOrder>
    {

        public void Configure(EntityTypeBuilder<PurchaseOrder> builder)
        {
            builder.ToTable("purchase_orders");

            // Assuming PurchaseOrder inherits from BaseEntity<Guid>
            builder.HasKey(po => po.Id);

            // --- Table-Per-Type (TPT) Inheritance Strategy ---
            // Configures PurchaseOrder as the base type in a TPT hierarchy.
            // Specific PO types (e.g., StandardPurchaseOrder, ServicePurchaseOrder)
            // would inherit from PurchaseOrder and have their own tables.
            builder.UseTptMappingStrategy();

            builder.Property<uint>("xmin")
                   .HasColumnType("xid") // Explicitly map to PostgreSQL xid type
                   .ValueGeneratedOnAddOrUpdate() // Indicate it's generated by the database
                   .IsConcurrencyToken();

            // --- Properties ---
            builder.Property(po => po.OrderNumber)
                .IsRequired()
                .HasMaxLength(100);
            builder.HasIndex(po => po.OrderNumber).IsUnique(); // Ensure PO numbers are unique

            builder.Property(po => po.OrderDate)
                .IsRequired();

            // Configure DeliveryDate and add an index
            builder.Property(po => po.DeliveryDate)
                .IsRequired(false); // Make DeliveryDate optional if needed
            builder.HasIndex(po => po.DeliveryDate)
                   .HasDatabaseName("IX_PurchaseOrder_DeliveryDate"); // Optional: Custom index name

            // Configure Status enum mapping (store as string for readability)
            builder.Property(po => po.Status)
                .IsRequired()
                .HasConversion<string>() // Convert enum to string
                .HasMaxLength(50); // Adjust max length based on enum value lengths
            builder.HasIndex(po => po.Status); // Index for filtering by status

            // Note on Status Transition Tracking:
            // This configuration maps the *current* status. Tracking the history of status
            // changes typically involves Domain Events, separate Audit Log tables, or potentially
            // database-level history features if available and configured (like temporal tables,
            // but those aren't standard in PostgreSQL in the same way as SQL Server).

            // Configure Vendor Payment Terms
            builder.Property(po => po.PaymentTerms)
                .HasMaxLength(255); // Adjust max length as needed

            // Configure Currency Code (Part of Money VO for TotalAmount, but also stored directly)
            builder.Property(po => po.CurrencyCode)
                .IsRequired()
                .HasMaxLength(3); // Standard 3-letter ISO currency code


            // --- Value Object Embedding ---

            // Embed ShipmentAddress (Address VO)
            builder.OwnsOne(po => po.ShipmentAddress, addr =>
            {
                addr.Property(a => a.Street).HasColumnName("shipment_street").HasMaxLength(200).IsRequired();
                addr.Property(a => a.City).HasColumnName("shipment_city").HasMaxLength(100).IsRequired();
                addr.Property(a => a.State).HasColumnName("shipment_state").HasMaxLength(100).IsRequired();
                addr.Property(a => a.Country).HasColumnName("shipment_country").HasMaxLength(100).IsRequired();
                addr.Property(a => a.PostalCode).HasColumnName("shipment_postal_code").HasMaxLength(20).IsRequired();
            });
            builder.Navigation(po => po.ShipmentAddress).IsRequired();

            // Embed TotalAmount (Money VO)
            // Note on Currency Conversion Handling: EF Core maps the stored value.
            // Actual conversion logic (if needed, e.g., displaying in another currency)
            // belongs in the Application or Presentation layers, not in persistence configuration.
            builder.OwnsOne(po => po.TotalAmount, total =>
            {
                total.Property(m => m.Amount)
                    .HasColumnName("total_amount") // Using TotalAmount directly as column name
                    .HasPrecision(18, 4) // Consistent precision
                    .IsRequired();

                // CurrencyCode is already a direct property on PurchaseOrder,
                // so we don't map it again here to avoid duplication.
                // Ensure consistency between PO.CurrencyCode and PO.TotalAmount.CurrencyCode in domain logic.
                total.Ignore(m => m.CurrencyCode); // Ignore mapping from VO if stored directly on PO

                // Alternative: Map both from VO if CurrencyCode is NOT a direct property on PO
                // total.Property(m => m.CurrencyCode)
                //     .HasColumnName("CurrencyCode") // Map to the PO's CurrencyCode column
                //     .HasMaxLength(3)
                //     .IsRequired();
            });
            builder.Navigation(po => po.TotalAmount).IsRequired();


            // --- Relationships ---

            // Relationship to Vendor (Many POs to One Vendor)
            builder.HasOne(po => po.Vendor)
                   .WithMany(v => v.PurchaseOrders) // Assumes Vendor has ICollection<PurchaseOrder> PurchaseOrders
                   .HasForeignKey(po => po.VendorId)
                   .IsRequired() // A PO must have a Vendor
                   .OnDelete(DeleteBehavior.Restrict); // Prevent deleting Vendor if POs exist

            // Relationship to Contract (Optional Many POs to One Contract)
            builder.HasOne(po => po.Contract)
                   .WithMany(c => c.PurchaseOrders) // Assumes Contract has ICollection<PurchaseOrder> PurchaseOrders
                   .HasForeignKey(po => po.ContractId)
                   .IsRequired(false) // A PO might not originate from a Contract
                   .OnDelete(DeleteBehavior.SetNull); // If Contract deleted, set PO.ContractId to null (or Restrict)

            // Relationship to PurchaseRequisition (Optional Many POs to One Requisition)
            builder.HasOne(po => po.Requisition)
                   .WithMany(r => r.PurchaseOrders) // Assumes PurchaseRequisition has ICollection<PurchaseOrder> PurchaseOrders
                   .HasForeignKey(po => po.RequisitionId)
                   .IsRequired(false) // A PO might not originate from a Requisition
                   .OnDelete(DeleteBehavior.SetNull); // If Requisition deleted, set PO.RequisitionId to null (or Restrict)

            // Relationship to PurchaseOrderLine (One PO to Many Lines)
            builder.HasMany(po => po.Lines)
                   .WithOne(line => line.PurchaseOrder) // Assumes PurchaseOrderLine has PurchaseOrder nav property
                   .HasForeignKey(line => line.PurchaseOrderId) // Assumes PurchaseOrderLine has PurchaseOrderId FK
                   .IsRequired()
                   .OnDelete(DeleteBehavior.Cascade); // Deleting a PO cascades to delete its lines


            // --- Indexes ---
            // Included unique index on OrderNumber and index on DeliveryDate, Status above.
            builder.HasIndex(po => po.VendorId);
            builder.HasIndex(po => po.ContractId);
            builder.HasIndex(po => po.RequisitionId);
            builder.HasIndex(po => po.OrderDate);


            // --- Tenant Isolation ---
            // Note: Tenant filtering should be implemented at the DbContext level
            // Example:
            // builder.HasQueryFilter(po => po.TenantId == currentTenantId);
            // Where currentTenantId is obtained from the DbContext or a tenant provider service

        }
    }
}

