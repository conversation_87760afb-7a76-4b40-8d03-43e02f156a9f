using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using ProcureToPay.Application.DTOs.Auth;
using ProcureToPay.Application.Services;
using System;
using System.Linq;
using System.Security.Claims; // For UserId in MFA setup/enable
using System.Threading.Tasks;

namespace ProcureToPay.WebApp.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class AuthController : ControllerBase
    {
        private readonly IAuthenticationService _authenticationService;
        private readonly IMfaService _mfaService;

        public AuthController(IAuthenticationService authenticationService, IMfaService mfaService)
        {
            _authenticationService = authenticationService;
            _mfaService = mfaService;
        }

        [HttpPost("register")]
        [AllowAnonymous]
        public async Task<IActionResult> Register(RegisterUserRequest request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var (success, user, errors) = await _authenticationService.RegisterUserAsync(request, HttpContext.Connection.RemoteIpAddress?.ToString());

            if (success && user != null)
            {
                // In a real app, you might return a 201 Created with user info or a token
                return Ok(new { Message = "Registration successful", UserId = user.Id });
            }
            return BadRequest(new { Message = "Registration failed", Errors = errors });
        }

        [HttpPost("login")]
        [AllowAnonymous]
        public async Task<IActionResult> Login(LoginUserRequest request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var response = await _authenticationService.LoginUserAsync(request, HttpContext.Connection.RemoteIpAddress?.ToString());

            if (response.Success)
            {
                // In a real app, you would issue a JWT token or session cookie here.
                // For now, just returning the response object.
                return Ok(response);
            }

            if (response.Message == "MFA code required.") // Specific case for MFA prompt
            {
                return StatusCode(StatusCodes.Status401Unauthorized, response); // Or a custom status code / response structure for MFA
            }

            return Unauthorized(new { response.Message });
        }

        [HttpPost("logout")]
        [Authorize] // User must be authenticated to logout
        public IActionResult Logout()
        {
            // For JWT, logout is primarily a client-side operation (discarding the token).
            // Server-side blocklisting is an advanced option not implemented here.
            // Optionally, log the logout event.
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
             _authenticationService.LogAuditEvent(
                actionType: "UserLogout",
                success: true,
                userId: Guid.TryParse(userId, out var uid) ? uid : null,
                performingUserId: Guid.TryParse(userId, out var puid) ? puid : null,
                ipAddress: HttpContext.Connection.RemoteIpAddress?.ToString(),
                details: $"User {User.Identity?.Name} logged out."
            ); // Fire-and-forget logging is okay here

            return Ok(new { Message = "Logout successful. Please discard your token." });
        }

        [HttpGet("mfa/setup")]
        // [Authorize] // This endpoint should require authentication
        public async Task<IActionResult> GetMfaSetup() // Normally, userId would come from ClaimsPrincipal
        {
            // --- Placeholder: Authentication is required to know which user to setup MFA for ---
            // This is a simplified version. In a real app, get UserId from User.Claims.
            // For now, let's assume a way to get UserId, or this endpoint is illustrative.
            // Guid currentUserId;
            // var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
            // if (string.IsNullOrEmpty(userIdClaim) || !Guid.TryParse(userIdClaim, out currentUserId))
            // {
            //     return Unauthorized(new { Message = "User not authenticated." });
            // }
            // --- End Placeholder ---

            // This is a dummy user ID for demonstration as authentication isn't fully set up for API
            Guid dummyUserId = Guid.NewGuid(); // Replace with actual user ID from authenticated context
            if (dummyUserId == Guid.Empty) return BadRequest("User ID not available. Endpoint requires authentication.");


            // It's good practice to use the application name or a recognizable issuer.
            // The account title should uniquely identify the account to the user (e.g., their email or username).
            var setupDetails = await _mfaService.GenerateMfaSetupAsync(dummyUserId, "ProcureToPayApp", $"user@{dummyUserId.ToString().Substring(0,4)}");

            if (setupDetails == null)
            {
                return BadRequest(new { Message = "Could not generate MFA setup details." });
            }

            // IMPORTANT: The TempSecretKey should NOT be sent to the client if it's the same as the final secret.
            // The flow here assumes TempSecretKey is used to validate the first OTP, then the actual MfaSecretKey is stored.
            // If setupDetails.ManualEntryKey is the final secret, that's fine.
            // The key is that EnableMfaAsync must use the same secret that GenerateMfaSetupAsync provided for the QR code.
            // The current MfaService.GenerateMfaSetupAsync returns TempSecretKey (plain text) and ManualEntryKey (Base32 of plain text).
            // The QrCodeSetupImageUrl uses the plain text secret.
            // So, client needs TempSecretKey to verify, then this TempSecretKey becomes the MfaSecretKey on User.
            // This is acceptable.
            return Ok(new { setupDetails.ManualEntryKey, setupDetails.QrCodeSetupImageUrl, setupDetails.TempSecretKey });
        }

        public class EnableMfaRequest
        {
            public string TempSecretKey { get; set; } = string.Empty;
            public string TotpCode { get; set; } = string.Empty;
        }

        [HttpPost("mfa/enable")]
        // [Authorize] // This endpoint should require authentication
        public async Task<IActionResult> EnableMfa([FromBody] EnableMfaRequest request)
        {
            if (!ModelState.IsValid) return BadRequest(ModelState);

            // --- Placeholder: Authentication is required ---
            // Guid currentUserId;
            // var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
            // if (string.IsNullOrEmpty(userIdClaim) || !Guid.TryParse(userIdClaim, out currentUserId))
            // {
            //     return Unauthorized(new { Message = "User not authenticated." });
            // }
            // --- End Placeholder ---
            Guid dummyUserId = Guid.NewGuid(); // Replace with actual user ID
             if (dummyUserId == Guid.Empty) return BadRequest("User ID not available. Endpoint requires authentication.");


            var success = await _mfaService.EnableMfaAsync(dummyUserId, request.TempSecretKey, request.TotpCode);

            if (success)
            {
                await _authenticationService.LogAuditEvent(dummyUserId, "MfaEnabled", true, HttpContext.Connection.RemoteIpAddress?.ToString(), "MFA enabled by user.");
                return Ok(new { Message = "MFA enabled successfully." });
            }

            await _authenticationService.LogAuditEvent(dummyUserId, "MfaEnableAttemptFailed", false, HttpContext.Connection.RemoteIpAddress?.ToString(), "Failed to enable MFA, invalid TOTP code or secret.");
            return BadRequest(new { Message = "Failed to enable MFA. Invalid code or secret." });
        }
    }
}
