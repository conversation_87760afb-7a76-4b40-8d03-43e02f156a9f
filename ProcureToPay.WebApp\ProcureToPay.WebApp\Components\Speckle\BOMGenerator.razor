@page "/speckle-bom-generator"
@inject HttpClient Http
@inject IJSRuntime JSRuntime
@using ProcureToPay.Application.DTOs.Speckle
@using System.Text.Json
@using System.ComponentModel.DataAnnotations // For ValidationResult

<h3>Speckle BOM Generator</h3>

<div class="mb-3">
    <label for="projectLinkId" class="form-label">Speckle Project Link ID:</label>
    <input type="text" class="form-control" id="projectLinkId" @bind="projectLinkIdInput" />
    <div class="form-text">Enter the Guid of the SpeckleProjectLink to generate BOM for.</div>
</div>

<div class="mb-3">
    <label for="groupByProps" class="form-label">Group By Properties (comma-separated):</label>
    <input type="text" class="form-control" id="groupByProps" @bind="groupByPropsInput" />
    <div class="form-text">E.g., ObjectType,Material,Size</div>
</div>

<div class="mb-3">
    <label for="includeParams" class="form-label">Include Parameters (comma-separated):</label>
    <input type="text" class="form-control" id="includeParams" @bind="includeParamsInput" />
    <div class="form-text">E.g., Length,Area,Volume</div>
</div>

<button class="btn btn-primary" @onclick="GenerateBOM" disabled="@isLoading">
    @if (isLoading)
    {
        <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
        <span> Generating...</span>
    }
    else
    {
        <span>Generate BOM</span>
    }
</button>

@if (!string.IsNullOrEmpty(errorMessage))
{
    <div class="alert alert-danger mt-3" role="alert">
        @errorMessage
    </div>
}

@if (generatedBomJson != null)
{
    <div class="mt-3">
        <h4>Generated BOM (Raw JSON):</h4>
        <pre style="background-color: #f5f5f5; border: 1px solid #ccc; padding: 10px; max-height: 500px; overflow-y: auto;">@generatedBomJson</pre>
    </div>
}

@code {
    private string projectLinkIdInput { get; set; } = "00000000-0000-0000-0000-000000000000"; // Placeholder
    private string groupByPropsInput { get; set; } = "ObjectType,category"; // Example default
    private string includeParamsInput { get; set; } = "Length,Volume"; // Example default

    private string? generatedBomJson;
    private string? errorMessage;
    private bool isLoading = false;

    private async Task GenerateBOM()
    {
        isLoading = true;
        errorMessage = null;
        generatedBomJson = null;

        if (!Guid.TryParse(projectLinkIdInput, out Guid projectLinkId))
        {
            errorMessage = "Invalid Speckle Project Link ID format.";
            isLoading = false;
            return;
        }

        var groupBy = string.IsNullOrWhiteSpace(groupByPropsInput)
                        ? new List<string>()
                        : groupByPropsInput.Split(',').Select(s => s.Trim()).Where(s => !string.IsNullOrEmpty(s)).ToList();

        if (!groupBy.Any())
        {
            errorMessage = "At least one 'Group By' property must be specified.";
            isLoading = false;
            return;
        }

        var includeParams = string.IsNullOrWhiteSpace(includeParamsInput)
                              ? new List<string>()
                              : includeParamsInput.Split(',').Select(s => s.Trim()).Where(s => !string.IsNullOrEmpty(s)).ToList();

        var request = new BOMGenerationRequestDto
        {
            SpeckleProjectLinkId = projectLinkId,
            GroupByProperties = groupBy,
            IncludeParametersInBom = includeParams,
            // FilterJson can be added here if a UI for it is developed
        };

        try
        {
            var response = await Http.PostAsJsonAsync("/api/speckle/boms/generate", request);

            if (response.IsSuccessStatusCode)
            {
                var bomDto = await response.Content.ReadFromJsonAsync<BillOfMaterialDto>();
                generatedBomJson = JsonSerializer.Serialize(bomDto, new JsonSerializerOptions { WriteIndented = true });
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                errorMessage = $"Error generating BOM: {response.ReasonPhrase}. Details: {errorContent}";
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"An unexpected error occurred: {ex.Message}";
        }
        finally
        {
            isLoading = false;
        }
    }
}
