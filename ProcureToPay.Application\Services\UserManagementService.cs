using Microsoft.EntityFrameworkCore;
using ProcureToPay.Application.DTOs.Admin;
using ProcureToPay.Application.DTOs.Auth; // For RegisterUserRequest for password validation logic reuse
using ProcureToPay.Domain.Constants;
using ProcureToPay.Domain.Entities;
using ProcureToPay.Domain.Enums;
using ProcureToPay.Infrastructure.Persistence;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace ProcureToPay.Application.Services
{
    public interface IUserManagementService
    {
        Task<IEnumerable<AdminUserDto>> GetUsersAsync(Guid performingAdminUserId, string? ipAddress);
        Task<(bool Success, Guid? UserId, IEnumerable<string> Errors)> AdminCreateUserAsync(Guid performingAdminUserId, AdminCreateUserRequest request, string? ipAddress);
        Task<(bool Success, IEnumerable<string> Errors)> AdminAssignUserRoleAsync(Guid performingAdminUserId, Guid targetUserId, UserRole newRole, string? ipAddress);
        Task<(bool Success, IEnumerable<string> Errors)> AdminSetUserActivationAsync(Guid performingAdminUserId, Guid targetUserId, bool isActive, string? ipAddress);
        Task<(bool Success, string? ResetTokenPreview, IEnumerable<string> Errors)> AdminInitiatePasswordResetAsync(Guid performingAdminUserId, Guid targetUserId, string? ipAddress);
    }

    public class UserManagementService : IUserManagementService
    {
        private readonly ApplicationDbContext _context;
        private readonly IPermissionValidationService _permissionService;
        private readonly IPasswordService _passwordService;
        private readonly IAuthenticationService _authenticationService; // For password validation and audit logging

        public UserManagementService(
            ApplicationDbContext context,
            IPermissionValidationService permissionService,
            IPasswordService passwordService,
            IAuthenticationService authenticationService)
        {
            _context = context;
            _permissionService = permissionService;
            _passwordService = passwordService;
            _authenticationService = authenticationService;
        }

        public async Task<IEnumerable<AdminUserDto>> GetUsersAsync(Guid performingAdminUserId, string? ipAddress)
        {
            var performingAdmin = await _context.P2PUsers.FindAsync(performingAdminUserId);
            if (performingAdmin == null) return Enumerable.Empty<AdminUserDto>(); // Or throw

            IQueryable<User> query = _context.P2PUsers.AsNoTracking();

            if (await _permissionService.HasPermissionAsync(performingAdminUserId, Permissions.ViewUsersPlatform, ipAddress))
            {
                // Marketplace Admin can see all users
            }
            else if (await _permissionService.HasPermissionAsync(performingAdminUserId, Permissions.ViewUsersOrganization, ipAddress))
            {
                if (!performingAdmin.OrganizationId.HasValue)
                {
                    await _authenticationService.LogAuditEvent(actionType: "GetUsersAttempt_Failure", success: false, performingUserId: performingAdminUserId, ipAddress: ipAddress, details: "Org admin has no OrganizationId, cannot filter users.");
                    return Enumerable.Empty<AdminUserDto>();
                }
                query = query.Where(u => u.OrganizationId == performingAdmin.OrganizationId);
            }
            else
            {
                await _authenticationService.LogAuditEvent(actionType: "GetUsersAttempt_PermissionDenied", success: false, performingUserId: performingAdminUserId, ipAddress: ipAddress, details: "User does not have permission to view users.");
                return Enumerable.Empty<AdminUserDto>(); // No permission
            }

            return await query.Select(u => new AdminUserDto
            {
                Id = u.Id,
                Username = u.Username,
                Email = u.Email,
                IsEmailVerified = u.IsEmailVerified,
                UserRole = u.UserRole.ToString(),
                IsActive = u.IsActive,
                OrganizationId = u.OrganizationId,
                FullName = u.FullName,
                PhoneNumber = u.PhoneNumber,
                LastLoginDate = u.LastLoginDate,
                LockoutEndDateUtc = u.LockoutEndDateUtc
            }).ToListAsync();
        }

        public async Task<(bool Success, Guid? UserId, IEnumerable<string> Errors)> AdminCreateUserAsync(Guid performingAdminUserId, AdminCreateUserRequest request, string? ipAddress)
        {
            var performingAdmin = await _context.P2PUsers.FindAsync(performingAdminUserId);
            if (performingAdmin == null) return (false, null, new List<string> { "Performing admin user not found." });

            Guid? targetOrganizationId = null;

            if (await _permissionService.HasPermissionAsync(performingAdminUserId, Permissions.ManageUsersPlatform, ipAddress))
            {
                targetOrganizationId = request.OrganizationId; // Marketplace admin can specify an org or leave null
            }
            else if (await _permissionService.HasPermissionAsync(performingAdminUserId, Permissions.ManageUsersOrganization, ipAddress))
            {
                if (!performingAdmin.OrganizationId.HasValue)
                {
                    return (false, null, new List<string> { "Organization admin does not have an assigned organization." });
                }
                // Org admin creating user for their own organization
                targetOrganizationId = performingAdmin.OrganizationId.Value;
                if (request.OrganizationId.HasValue && request.OrganizationId != targetOrganizationId)
                {
                     return (false, null, new List<string> { "Organization admin cannot create users for a different organization." });
                }
                // Ensure role is not MarketplaceAdmin if org admin is creating
                if(request.Role == UserRole.MarketplaceAdmin)
                {
                    return (false, null, new List<string> { "Organization admin cannot create MarketplaceAdmin users." });
                }
            }
            else
            {
                return (false, null, new List<string> { "User does not have permission to create users." });
            }

            // Validate password policy
            var passwordPolicy = await _context.PasswordPolicies.FirstOrDefaultAsync(); // Assuming global policy
            if (passwordPolicy == null) return (false, null, new List<string> { "Password policy not configured." });

            var passwordErrors = _authenticationService.ValidatePassword(request.Password, passwordPolicy, new List<string>(), _passwordService);
            if (passwordErrors.Any()) return (false, null, passwordErrors);

            if (await _context.P2PUsers.AnyAsync(u => u.Username == request.Username))
                return (false, null, new List<string> { "Username already exists." });
            if (await _context.P2PUsers.AnyAsync(u => u.Email == request.Email))
                return (false, null, new List<string> { "Email already exists." });

            var newUser = new User(Guid.NewGuid(), request.Username, request.Email, request.Role)
            {
                PasswordHash = _passwordService.HashPassword(request.Password),
                FullName = request.FullName,
                PhoneNumber = request.PhoneNumber,
                OrganizationId = targetOrganizationId,
                IsActive = true, // Default to active
                IsEmailVerified = false // Admin created users might need to verify email too, or flag as admin-verified
            };
            newUser.PasswordHistory.Add(newUser.PasswordHash);

            _context.P2PUsers.Add(newUser);
            await _context.SaveChangesAsync();
            await _authenticationService.LogAuditEvent(actionType: "AdminUserCreated", success: true, userId: newUser.Id, performingUserId: performingAdminUserId, targetEntityType: "User", targetEntityId: newUser.Id.ToString(), ipAddress: ipAddress, details: $"Admin created user {newUser.Username}.");
            return (true, newUser.Id, Enumerable.Empty<string>());
        }

        public async Task<(bool Success, IEnumerable<string> Errors)> AdminAssignUserRoleAsync(Guid performingAdminUserId, Guid targetUserId, UserRole newRole, string? ipAddress)
        {
            var performingAdmin = await _context.P2PUsers.FindAsync(performingAdminUserId);
            var targetUser = await _context.P2PUsers.FindAsync(targetUserId);

            if (performingAdmin == null) return (false, new List<string> { "Performing admin user not found." });
            if (targetUser == null) return (false, new List<string> { "Target user not found." });

            bool canManagePlatform = await _permissionService.HasPermissionAsync(performingAdminUserId, Permissions.ManageUsersPlatform, ipAddress);
            bool canManageOrganization = await _permissionService.HasPermissionAsync(performingAdminUserId, Permissions.ManageUsersOrganization, ipAddress);

            if (!canManagePlatform && !canManageOrganization)
                return (false, new List<string> { "User does not have permission to assign roles." });

            if (canManageOrganization && !canManagePlatform) // Org admin
            {
                if (performingAdmin.OrganizationId != targetUser.OrganizationId || !performingAdmin.OrganizationId.HasValue)
                    return (false, new List<string> { "Organization admin can only assign roles to users within their own organization." });
                if (newRole == UserRole.MarketplaceAdmin) // Org admin cannot assign MarketplaceAdmin role
                     return (false, new List<string> { "Organization admin cannot assign MarketplaceAdmin role." });
            }
            // MarketplaceAdmin (canManagePlatform) can assign any role to any user.

            targetUser.UserRole = newRole;
            await _context.SaveChangesAsync();
            await _authenticationService.LogAuditEvent(actionType: "AdminUserRoleAssigned", success: true, userId: targetUserId, performingUserId: performingAdminUserId, targetEntityType: "User", targetEntityId: targetUserId.ToString(), ipAddress: ipAddress, details: $"Admin assigned role {newRole} to user {targetUser.Username}.");
            return (true, Enumerable.Empty<string>());
        }

        public async Task<(bool Success, IEnumerable<string> Errors)> AdminSetUserActivationAsync(Guid performingAdminUserId, Guid targetUserId, bool isActive, string? ipAddress)
        {
            var performingAdmin = await _context.P2PUsers.FindAsync(performingAdminUserId);
            var targetUser = await _context.P2PUsers.FindAsync(targetUserId);

            if (performingAdmin == null) return (false, new List<string> { "Performing admin user not found." });
            if (targetUser == null) return (false, new List<string> { "Target user not found." });

            bool canManagePlatform = await _permissionService.HasPermissionAsync(performingAdminUserId, Permissions.ManageUsersPlatform, ipAddress);
            bool canManageOrganization = await _permissionService.HasPermissionAsync(performingAdminUserId, Permissions.ManageUsersOrganization, ipAddress);

            if (!canManagePlatform && !canManageOrganization)
                return (false, new List<string> { "User does not have permission to set user activation status." });

            if (canManageOrganization && !canManagePlatform) // Org admin
            {
                if (performingAdmin.OrganizationId != targetUser.OrganizationId || !performingAdmin.OrganizationId.HasValue)
                    return (false, new List<string> { "Organization admin can only (de)activate users within their own organization." });
            }
            // MarketplaceAdmin (canManagePlatform) can (de)activate any user.

            targetUser.IsActive = isActive;
            await _context.SaveChangesAsync();
            await _authenticationService.LogAuditEvent(actionType: isActive ? "AdminUserActivated" : "AdminUserDeactivated", success: true, userId: targetUserId, performingUserId: performingAdminUserId, targetEntityType: "User", targetEntityId: targetUserId.ToString(), ipAddress: ipAddress, details: $"Admin set user {targetUser.Username} active status to {isActive}.");
            return (true, Enumerable.Empty<string>());
        }

        public async Task<(bool Success, string? ResetTokenPreview, IEnumerable<string> Errors)> AdminInitiatePasswordResetAsync(Guid performingAdminUserId, Guid targetUserId, string? ipAddress)
        {
            var performingAdmin = await _context.P2PUsers.FindAsync(performingAdminUserId);
            var targetUser = await _context.P2PUsers.FindAsync(targetUserId);

            if (performingAdmin == null) return (false, null, new List<string> { "Performing admin user not found." });
            if (targetUser == null) return (false, null, new List<string> { "Target user not found." });

            bool canManagePlatform = await _permissionService.HasPermissionAsync(performingAdminUserId, Permissions.ManageUsersPlatform, ipAddress);
            bool canManageOrganization = await _permissionService.HasPermissionAsync(performingAdminUserId, Permissions.ManageUsersOrganization, ipAddress);

            if (!canManagePlatform && !canManageOrganization)
                return (false, null, new List<string> { "User does not have permission to initiate password resets." });

            if (canManageOrganization && !canManagePlatform) // Org admin
            {
                 if (performingAdmin.OrganizationId != targetUser.OrganizationId || !performingAdmin.OrganizationId.HasValue)
                    return (false, null, new List<string> { "Organization admin can only initiate password resets for users within their own organization." });
            }

            // Generate token (simple random string for this example, should be cryptographically secure)
            var tokenBytes = RandomNumberGenerator.GetBytes(32);
            var resetToken = Convert.ToBase64String(tokenBytes)
                .Replace("+", "-").Replace("/", "_").TrimEnd('='); // URL-safe

            targetUser.PasswordResetTokenHash = _passwordService.HashPassword(resetToken); // Hash the token before storing
            targetUser.PasswordResetTokenExpiryUtc = DateTime.UtcNow.AddHours(24); // Token valid for 24 hours

            await _context.SaveChangesAsync();
            await _authenticationService.LogAuditEvent(actionType: "AdminPasswordResetInitiated", success: true, userId: targetUserId, performingUserId: performingAdminUserId, targetEntityType: "User", targetEntityId: targetUserId.ToString(), ipAddress: ipAddress, details: $"Admin initiated password reset for user {targetUser.Username}.");

            // For testing/demo purposes, returning the token. In production, this would be emailed.
            return (true, resetToken, Enumerable.Empty<string>());
        }
    }
}
