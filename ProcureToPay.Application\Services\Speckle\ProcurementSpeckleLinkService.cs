using Microsoft.EntityFrameworkCore;
using ProcureToPay.Application.DTOs.Speckle;
using ProcureToPay.Domain.Entities;
using ProcureToPay.Infrastructure.Persistence;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace ProcureToPay.Application.Services.Speckle
{
    public class ProcurementSpeckleLinkService : IProcurementSpeckleLinkService
    {
        private readonly ApplicationDbContext _context;
        // private readonly IMapper _mapper; // If using AutoMapper

        public ProcurementSpeckleLinkService(ApplicationDbContext context /*, IMapper mapper*/)
        {
            _context = context;
            // _mapper = mapper;
        }

        public async Task<ProcurementSpeckleLinkDto?> CreateLinkAsync(CreateProcurementSpeckleLinkRequest request, Guid linkedByUserId)
        {
            // Validate that the SpeckleObjectMetadataId exists
            var metadataExists = await _context.SpeckleObjectMetadata.AnyAsync(m => m.Id == request.SpeckleObjectMetadataId);
            if (!metadataExists)
            {
                // Consider throwing a specific exception or returning a result object indicating failure
                throw new ArgumentException($"SpeckleObjectMetadata with ID {request.SpeckleObjectMetadataId} not found.", nameof(request.SpeckleObjectMetadataId));
            }

            // Validate that the LinkedByUserId exists (User) using the P2PUsers DbSet
            var userExists = await _context.P2PUsers.AnyAsync(u => u.Id == linkedByUserId);
            if (!userExists) {
                throw new ArgumentException($"User with ID {linkedByUserId} not found.", nameof(linkedByUserId));
            }

            // Check for duplicate link (optional, based on business rules)
            var duplicateLink = await _context.ProcurementSpeckleLinks
                .FirstOrDefaultAsync(l => l.SpeckleObjectMetadataId == request.SpeckleObjectMetadataId &&
                                          l.ProcurementItemType == request.ProcurementItemType &&
                                          l.ProcurementItemId == request.ProcurementItemId);
            if (duplicateLink != null)
            {
                // Return existing link or throw error based on requirements
                // For now, let's assume we don't create duplicates and return the existing one (or could throw)
                // Or, if an error is preferred: throw new InvalidOperationException("This Speckle object is already linked to this procurement item.");
                var existingDto = await GetLinkByIdAsync(duplicateLink.Id); // This will map it with details
                return existingDto;
            }


            var newLink = new ProcurementSpeckleLink(
                request.SpeckleObjectMetadataId,
                request.ProcurementItemType!, // Null forgiving: validated by [Required] attribute
                request.ProcurementItemId,
                linkedByUserId
            );

            _context.ProcurementSpeckleLinks.Add(newLink);
            await _context.SaveChangesAsync();

            return await GetLinkByIdAsync(newLink.Id); // Fetch again to populate navigation properties for the DTO
        }

        public async Task<IEnumerable<ProcurementSpeckleLinkDto>> GetLinksByProcurementItemAsync(string itemType, Guid itemId)
        {
            var links = await _context.ProcurementSpeckleLinks
                .Where(l => l.ProcurementItemType == itemType && l.ProcurementItemId == itemId)
                .Include(l => l.SpeckleObjectMetadata) // Include related data for DTO
                .Include(l => l.LinkedByUser)          // Include related data for DTO
                .ToListAsync();

            return links.Select(MapToDto);
        }

        public async Task<IEnumerable<ProcurementSpeckleLinkDto>> GetLinksBySpeckleObjectAsync(Guid speckleObjectMetadataId)
        {
            var links = await _context.ProcurementSpeckleLinks // Corrected typo here
                .Where(l => l.SpeckleObjectMetadataId == speckleObjectMetadataId)
                .Include(l => l.SpeckleObjectMetadata)
                .Include(l => l.LinkedByUser)
                .ToListAsync();

            return links.Select(MapToDto);
        }

        public async Task<ProcurementSpeckleLinkDto?> GetLinkByIdAsync(Guid linkId)
        {
            var link = await _context.ProcurementSpeckleLinks
                .Where(l => l.Id == linkId)
                .Include(l => l.SpeckleObjectMetadata)
                .Include(l => l.LinkedByUser)
                .FirstOrDefaultAsync();

            return link == null ? null : MapToDto(link);
        }

        public async Task<bool> DeleteLinkAsync(Guid linkId, Guid userId)
        {
            // TODO: Add permission check: does 'userId' have permission to delete this link?
            var link = await _context.ProcurementSpeckleLinks.FindAsync(linkId);
            if (link == null)
            {
                return false;
            }

            _context.ProcurementSpeckleLinks.Remove(link);
            return await _context.SaveChangesAsync() > 0;
        }

        private ProcurementSpeckleLinkDto MapToDto(ProcurementSpeckleLink link)
        {
            // Assumes link.SpeckleObjectMetadata and link.LinkedByUser are loaded if needed for DTO
            return new ProcurementSpeckleLinkDto
            {
                Id = link.Id,
                SpeckleObjectMetadataId = link.SpeckleObjectMetadataId,
                SpeckleObjectId = link.SpeckleObjectMetadata?.SpeckleObjectId, // From navigation
                ObjectType = link.SpeckleObjectMetadata?.ObjectType,     // From navigation
                ProcurementItemType = link.ProcurementItemType,
                ProcurementItemId = link.ProcurementItemId,
                LinkedByUserId = link.LinkedByUserId,
                LinkedByUserName = link.LinkedByUser?.Username, // User entity has Username property
                LinkedAt = link.LinkedAt
            };
        }
    }
}
