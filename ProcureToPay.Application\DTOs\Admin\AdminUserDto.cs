using System;

namespace ProcureToPay.Application.DTOs.Admin
{
    public class AdminUserDto
    {
        public Guid Id { get; set; }
        public string Username { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public bool IsEmailVerified { get; set; }
        public string UserRole { get; set; } = string.Empty; // Enum to string
        public bool IsActive { get; set; }
        public Guid? OrganizationId { get; set; }
        public string? FullName { get; set; }
        public string? PhoneNumber { get; set; }
        public DateTime? LastLoginDate { get; set; }
        public DateTime? LockoutEndDateUtc { get; set; }
    }
}
