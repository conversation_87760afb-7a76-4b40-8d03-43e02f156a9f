﻿START TRANSACTION;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250608101125_UpdateCategoryEntityNames') THEN
    DROP TABLE catalog."Products";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250608101125_UpdateCategoryEntityNames') THEN
    DROP TABLE catalog."Categories";
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250608101125_UpdateCategoryEntityNames') THEN
    CREATE TABLE catalog."ProductCategories" (
        "Id" uuid NOT NULL,
        "Name" character varying(200) NOT NULL,
        "Description" character varying(1000),
        "ParentCategoryId" uuid,
        "IsActive" boolean NOT NULL,
        "CreatedAt" timestamp with time zone NOT NULL,
        "ModifiedAt" timestamp with time zone,
        CONSTRAINT "PK_ProductCategories" PRIMARY KEY ("Id"),
        CONSTRAINT "FK_ProductCategories_ProductCategories_ParentCategoryId" FOREIGN KEY ("ParentCategoryId") REFERENCES catalog."ProductCategories" ("Id") ON DELETE RESTRICT
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250608101125_UpdateCategoryEntityNames') THEN
    CREATE TABLE catalog."CatalogProducts" (
        "Id" uuid NOT NULL,
        "SellerOrgId" uuid NOT NULL,
        "Name" character varying(250) NOT NULL,
        "Description" character varying(4000),
        "CategoryId" uuid NOT NULL,
        "ListPrice" numeric(18,2) NOT NULL,
        "ProductStatus" character varying(50) NOT NULL,
        "UnitOfMeasure" character varying(50),
        "StockQuantity" integer,
        "InventoryStatus" character varying(50) NOT NULL,
        "AvailableDate" timestamp with time zone,
        "StandardLeadTimeDays" integer,
        "QuantityBasedPricingTiersJson" text,
        "SubmittedAt" timestamp with time zone,
        "ApprovedByUserId" uuid,
        "ApprovedAt" timestamp with time zone,
        "RejectionReason" character varying(1000),
        "Version" integer NOT NULL,
        "CreatedAt" timestamp with time zone NOT NULL,
        "ModifiedAt" timestamp with time zone,
        CONSTRAINT "PK_CatalogProducts" PRIMARY KEY ("Id"),
        CONSTRAINT "FK_CatalogProducts_ProductCategories_CategoryId" FOREIGN KEY ("CategoryId") REFERENCES catalog."ProductCategories" ("Id") ON DELETE RESTRICT
    );
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250608101125_UpdateCategoryEntityNames') THEN
    CREATE INDEX "IX_CatalogProducts_CategoryId" ON catalog."CatalogProducts" ("CategoryId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250608101125_UpdateCategoryEntityNames') THEN
    CREATE INDEX "IX_CatalogProducts_SellerOrgId" ON catalog."CatalogProducts" ("SellerOrgId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250608101125_UpdateCategoryEntityNames') THEN
    CREATE INDEX "IX_ProductCategories_ParentCategoryId" ON catalog."ProductCategories" ("ParentCategoryId");
    END IF;
END $EF$;

DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM "__EFMigrationsHistory" WHERE "MigrationId" = '20250608101125_UpdateCategoryEntityNames') THEN
    INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
    VALUES ('20250608101125_UpdateCategoryEntityNames', '9.0.5');
    END IF;
END $EF$;
COMMIT;
