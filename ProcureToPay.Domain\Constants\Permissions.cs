namespace ProcureToPay.Domain.Constants
{
    public static class Permissions
    {
        // User Profile Management
        public const string ViewUserProfile = "Permissions.UserProfile.View";
        public const string UpdateUserProfile = "Permissions.UserProfile.Update";
        public const string ChangePassword = "Permissions.UserProfile.ChangePassword";
        public const string ManageMfaSettings = "Permissions.UserProfile.ManageMfa";

        // User Management (Admin)
        public const string ViewUsers = "Permissions.Users.View";
        public const string ManageUsers = "Permissions.Users.Manage"; // Create, Update, Delete, Assign Roles
        public const string UnlockUsers = "Permissions.Users.Unlock";

        // Role & Permission Management (Marketplace Admin)
        public const string ViewRoles = "Permissions.Roles.View";
        public const string ManageRoles = "Permissions.Roles.Manage"; // CRUD roles
        public const string AssignPermissionsToRole = "Permissions.Roles.AssignPermissions";

        // Procurement - Requisitions
        public const string CreateRequisition = "Permissions.Requisitions.Create";
        public const string ViewOwnRequisitions = "Permissions.Requisitions.ViewOwn";
        public const string ViewAllRequisitions = "Permissions.Requisitions.ViewAll"; // For admins/approvers
        public const string ApproveRequisition = "Permissions.Requisitions.Approve";
        public const string ManageRequisitions = "Permissions.Requisitions.Manage"; // Edit, Delete (admin)

        // Procurement - Purchase Orders
        public const string CreatePurchaseOrder = "Permissions.PurchaseOrders.Create";
        public const string ViewOwnPurchaseOrders = "Permissions.PurchaseOrders.ViewOwn";
        public const string ViewAllPurchaseOrders = "Permissions.PurchaseOrders.ViewAll";
        public const string ApprovePurchaseOrder = "Permissions.PurchaseOrders.Approve";
        public const string ManagePurchaseOrders = "Permissions.PurchaseOrders.Manage";

        // Catalog Management
        public const string ViewCatalog = "Permissions.Catalog.View";
        public const string ManageCatalog = "Permissions.Catalog.Manage"; // Add, Edit, Delete products (SellerAdmin, MarketplaceAdmin)

        // Reporting
        public const string ViewSystemReports = "Permissions.Reports.ViewSystem";
        public const string ViewOwnReports = "Permissions.Reports.ViewOwn";

        // Tenant Management (Marketplace Admin)
        public const string ManageTenants = "Permissions.Tenants.Manage";

        // User Administration Permissions (FR-USR-004)
        public const string ViewUsersPlatform = "Permissions.Users.ViewPlatform";           // Marketplace Admin: View all users across system
        public const string ManageUsersPlatform = "Permissions.Users.ManagePlatform";       // Marketplace Admin: Create any user, assign any top-level role, activate/deactivate, initiate password reset
        public const string ViewUsersOrganization = "Permissions.Users.ViewOrganization";   // Buyer/Seller Admin: View users within their own organization
        public const string ManageUsersOrganization = "Permissions.Users.ManageOrganization"; // Buyer/Seller Admin: Create users in their org, assign org-specific sub-roles (future), activate/deactivate org users, initiate password reset for org users

        // Audit Log Permissions (FR-CMP-003)
        public const string ViewAuditLogs = "Permissions.AuditLogs.View";

        // Catalog & Product Permissions (FR-CAT-001)
        public const string ManageCategories = "Permissions.Categories.Manage";       // MarketplaceAdmin: CRUD categories
        public const string ViewCategories = "Permissions.Categories.View";           // All users (for browsing, selecting)
        public const string ManageOwnProducts = "Permissions.Products.ManageOwn";       // SellerUser/SellerAdmin: CRUD own products
        public const string SubmitProductsForApproval = "Permissions.Products.SubmitOwn"; // SellerUser/SellerAdmin
        public const string ViewOwnProducts = "Permissions.Products.ViewOwn";           // SellerUser/SellerAdmin: View their own products regardless of status
        public const string ApproveProducts = "Permissions.Products.Approve";         // MarketplaceAdmin: Approve/Reject products
        public const string ViewAllProducts = "Permissions.Products.ViewAll";           // MarketplaceAdmin: View all products, any status
        public const string ViewApprovedProducts = "Permissions.Products.ViewApproved"; // All users: View approved products in public catalog

        // Placeholder for other modules like Invoicing, Payments, etc.
        // Example: public const string ManageInvoices = "Permissions.Invoices.Manage";

        /// <summary>
        /// Returns a list of all defined permissions.
        /// Useful for populating UI for role management.
        /// </summary>
        public static List<string> All()
        {
            return typeof(Permissions)
                .GetFields(System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Static | System.Reflection.BindingFlags.FlattenHierarchy)
                .Where(fi => fi.IsLiteral && !fi.IsInitOnly && fi.FieldType == typeof(string))
                .Select(x => (string)x.GetRawConstantValue()!)
                .ToList();
        }
    }
}
