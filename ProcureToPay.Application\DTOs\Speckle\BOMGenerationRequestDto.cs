using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace ProcureToPay.Application.DTOs.Speckle
{
    public class BOMGenerationRequestDto
    {
        [Required]
        public Guid SpeckleProjectLinkId { get; set; }

        [Required]
        [MinLength(1, ErrorMessage = "At least one property must be specified for grouping.")]
        public List<string> GroupByProperties { get; set; } = new List<string>();

        // Parameters to include as distinct columns in the BOM output, beyond the grouped ones.
        public List<string> IncludeParametersInBom { get; set; } = new List<string>();

        // Optional: JSON string for more complex filtering logic before grouping.
        // Example: { "Level": "Level 3", "Material": "Concrete" }
        // For this subtask, filter implementation will be skipped in the service.
        public string? FilterJson { get; set; }
    }
}
