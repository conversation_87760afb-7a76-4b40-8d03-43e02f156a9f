namespace ProcureToPay.Application.DTOs.Auth
{
    public class LoginUserResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public string? UserId { get; set; }
        public string? Username { get; set; }
        public bool RequiresMfaSetup { get; set; } // For admin users needing to set up MFA
        public bool RequiresPasswordChange { get; set; } // If password has expired
        public string? Token { get; set; }
    }
}
