using System;

namespace ProcureToPay.Domain.Entities
{
    public class RolePermission : BaseEntity<Guid>
    {
        public Guid RoleId { get; set; }
        public virtual Role Role { get; set; } = null!; // Navigation property

        public string PermissionString { get; set; } // Stores the permission string, e.g., "Permissions.UserProfile.View"

        // Private constructor for EF Core
        private RolePermission() : base(Guid.NewGuid())
        {
            PermissionString = string.Empty; // Initialize non-nullable string
        }

        public RolePermission(Guid roleId, string permissionString) : base(Guid.NewGuid())
        {
            if (Guid.Empty == roleId)
                throw new ArgumentException("RoleId cannot be empty.", nameof(roleId));
            if (string.IsNullOrWhiteSpace(permissionString))
                throw new ArgumentException("PermissionString cannot be empty.", nameof(permissionString));

            RoleId = roleId;
            PermissionString = permissionString;
        }
    }
}
