using System;
using System.Collections.Generic;

namespace ProcureToPay.Domain.Entities
{
    public class Role : BaseEntity<Guid>
    {
        public string Name { get; set; } // e.g., "BuyerUser", "MarketplaceAdmin", "SystemRole_Buyer"
        public string? Description { get; set; }

        public ICollection<RolePermission> RolePermissions { get; set; }

        // Constructor for EF Core and application code
        public Role(Guid id, string name, string? description = null) : base(id == Guid.Empty ? Guid.NewGuid() : id)
        {
            if (string.IsNullOrWhiteSpace(name))
                throw new ArgumentException("Role name cannot be empty.", nameof(name));

            Name = name;
            Description = description;
            RolePermissions = new HashSet<RolePermission>(); // Initialize collection
        }

        // Parameterless constructor for EF Core
        private Role() : base(Guid.NewGuid())
        {
            Name = string.Empty; // Initialize non-nullable string
            RolePermissions = new HashSet<RolePermission>();
        }
    }
}
