using Microsoft.EntityFrameworkCore;
using ProcureToPay.Application.DTOs.Catalog;
using ProcureToPay.Domain.Entities.Catalog; // This correctly brings in ProductCategory and CatalogProduct
using ProcureToPay.Infrastructure.Persistence;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace ProcureToPay.Application.Services.Catalog
{
    public interface ICategoryService
    {
        Task<(bool Success, CategoryDto? Category, IEnumerable<string> Errors)> CreateCategoryAsync(CreateCategoryRequest request, Guid performingUserId, string? ipAddress);
        Task<(bool Success, CategoryDto? Category, IEnumerable<string> Errors)> UpdateCategoryAsync(Guid categoryId, UpdateCategoryRequest request, Guid performingUserId, string? ipAddress);
        Task<CategoryDto?> GetCategoryAsync(Guid categoryId);
        Task<IEnumerable<CategoryDto>> GetAllCategoriesAsync(bool hierarchical = false); // Add option for flat or hierarchical
        Task<(bool Success, IEnumerable<string> Errors)> DeleteCategoryAsync(Guid categoryId, Guid performingUserId, string? ipAddress); // Soft delete
    }

    public class CategoryService : ICategoryService
    {
        private readonly ApplicationDbContext _context;
        private readonly IAuthenticationService _authenticationService; // For logging

        public CategoryService(ApplicationDbContext context, IAuthenticationService authenticationService)
        {
            _context = context;
            _authenticationService = authenticationService;
        }

        public async Task<(bool Success, CategoryDto? Category, IEnumerable<string> Errors)> CreateCategoryAsync(CreateCategoryRequest request, Guid performingUserId, string? ipAddress)
        {
            if (await _context.CatalogProductCategories.AnyAsync(c => c.Name == request.Name && (c.ParentCategoryId == request.ParentCategoryId || (c.ParentCategoryId == null && request.ParentCategoryId == null))))
            {
                return (false, null, new List<string> { "A category with this name already exists under the same parent." });
            }

            if (request.ParentCategoryId.HasValue && !await _context.CatalogProductCategories.AnyAsync(c => c.Id == request.ParentCategoryId.Value))
            {
                return (false, null, new List<string> { "Parent category not found." });
            }

            var category = new ProductCategory(Guid.NewGuid(), request.Name) // Use ProductCategory
            {
                Description = request.Description,
                ParentCategoryId = request.ParentCategoryId,
                IsActive = true
            };

            _context.CatalogProductCategories.Add(category); // Use ProductCategories DbSet
            await _context.SaveChangesAsync();

            await _authenticationService.LogAuditEvent(
                actionType: "ProductCategoryCreated", success: true, performingUserId: performingUserId, // Changed actionType
                targetEntityType: "ProductCategory", targetEntityId: category.Id.ToString(), ipAddress: ipAddress, // Changed targetEntityType
                details: $"ProductCategory '{category.Name}' created.");

            return (true, MapProductCategoryToDto(category), Enumerable.Empty<string>()); // Use new mapping
        }

        public async Task<(bool Success, CategoryDto? Category, IEnumerable<string> Errors)> UpdateCategoryAsync(Guid categoryId, UpdateCategoryRequest request, Guid performingUserId, string? ipAddress)
        {
            var category = await _context.CatalogProductCategories.Include(c=>c.Subcategories).FirstOrDefaultAsync(c => c.Id == categoryId); // Use ProductCategory
            if (category == null)
            {
                return (false, null, new List<string> { "ProductCategory not found." }); // Changed message
            }

            if (await _context.CatalogProductCategories.AnyAsync(c => c.Id != categoryId && c.Name == request.Name && (c.ParentCategoryId == request.ParentCategoryId || (c.ParentCategoryId == null && request.ParentCategoryId == null))))
            {
                 return (false, null, new List<string> { "Another category with this name already exists under the same parent." });
            }

            if (request.ParentCategoryId.HasValue)
            {
                if (!await _context.CatalogProductCategories.AnyAsync(c => c.Id == request.ParentCategoryId.Value))
                    return (false, null, new List<string> { "Parent category not found." });
                if (request.ParentCategoryId.Value == categoryId)
                    return (false, null, new List<string> { "Category cannot be its own parent." });
            }

            category.Name = request.Name;
            category.Description = request.Description;
            category.ParentCategoryId = request.ParentCategoryId;
            if(request.IsActive.HasValue) category.IsActive = request.IsActive.Value;

            await _context.SaveChangesAsync();
            await _authenticationService.LogAuditEvent(
                actionType: "ProductCategoryUpdated", success: true, performingUserId: performingUserId, // Changed
                targetEntityType: "ProductCategory", targetEntityId: category.Id.ToString(), ipAddress: ipAddress, // Changed
                details: $"ProductCategory '{category.Name}' updated.");

            return (true, MapProductCategoryToDto(category), Enumerable.Empty<string>()); // Use new mapping
        }

        public async Task<CategoryDto?> GetCategoryAsync(Guid categoryId)
        {
            var category = await _context.CatalogProductCategories // Use ProductCategory
                .AsNoTracking()
                .Include(c => c.ParentCategory)
                .Include(c => c.Subcategories)
                .Include(c => c.CatalogProducts)
                .FirstOrDefaultAsync(c => c.Id == categoryId);

            return category == null ? null : MapProductCategoryToDto(category, true);
        }

        public async Task<IEnumerable<CategoryDto>> GetAllCategoriesAsync(bool hierarchical = false)
        {
            var categories = await _context.CatalogProductCategories // Use ProductCategory
                .AsNoTracking()
                .Include(c => c.ParentCategory)
                .Include(c => c.CatalogProducts)
                .OrderBy(c => c.Name)
                .ToListAsync();

            if (!hierarchical)
            {
                return categories.Select(c => MapProductCategoryToDto(c, false)).ToList();
            }

            var categoryDtos = categories.Select(c => MapProductCategoryToDto(c, false)).ToDictionary(c => c.Id);
            var rootCategories = new List<CategoryDto>();

            foreach (var catEntity in categories)
            {
                var catDto = categoryDtos[catEntity.Id];
                if (catEntity.ParentCategoryId.HasValue && categoryDtos.TryGetValue(catEntity.ParentCategoryId.Value, out var parentDto))
                {
                    parentDto.Subcategories.Add(catDto);
                }
                else
                {
                    rootCategories.Add(catDto);
                }
            }
            return rootCategories;
        }

        public async Task<(bool Success, IEnumerable<string> Errors)> DeleteCategoryAsync(Guid categoryId, Guid performingUserId, string? ipAddress)
        {
            var category = await _context.CatalogProductCategories.Include(c => c.Subcategories).Include(c => c.CatalogProducts).FirstOrDefaultAsync(c => c.Id == categoryId); // Use ProductCategory
            if (category == null)
            {
                return (false, new List<string> { "ProductCategory not found." }); // Changed message
            }

            if (category.CatalogProducts.Any())
            {
                return (false, new List<string> { "Cannot delete category with associated products." });
            }
            if (category.Subcategories.Any())
            {
                 return (false, new List<string> { "Cannot delete category with subcategories." });
            }

            category.IsActive = false;

            await _context.SaveChangesAsync();
            await _authenticationService.LogAuditEvent(
                actionType: "ProductCategorySoftDeleted", success: true, performingUserId: performingUserId, // Changed
                targetEntityType: "ProductCategory", targetEntityId: category.Id.ToString(), ipAddress: ipAddress, // Changed
                details: $"ProductCategory '{category.Name}' soft deleted (set to inactive).");

            return (true, Enumerable.Empty<string>());
        }

        private CategoryDto MapProductCategoryToDto(ProductCategory category, bool mapSubcategories = false) // Renamed parameter
        {
            var dto = new CategoryDto
            {
                Id = category.Id,
                Name = category.Name,
                Description = category.Description,
                ParentCategoryId = category.ParentCategoryId,
                ParentCategoryName = category.ParentCategory?.Name,
                IsActive = category.IsActive,
                ProductCount = category.CatalogProducts?.Count ?? 0,
                SubcategoryCount = category.Subcategories?.Count ?? 0
            };

            if (mapSubcategories && category.Subcategories != null)
            {
                dto.Subcategories = category.Subcategories.Select(sc => MapProductCategoryToDto(sc, true)).ToList(); // Recursive call
            }
            return dto;
        }
    }
}
