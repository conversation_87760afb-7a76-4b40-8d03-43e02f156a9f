using System;

namespace ProcureToPay.Domain.Entities
{
    public class PasswordPolicy : BaseEntity<Guid>
    {
        public Guid TenantId { get; set; } // For multi-tenancy, if applicable
        public int MinLength { get; set; }
        public bool RequireUppercase { get; set; }
        public bool RequireLowercase { get; set; }
        public bool RequireDigit { get; set; }
        public bool RequireSpecialCharacter { get; set; }
        public int PasswordHistoryCount { get; set; } // How many old passwords to remember
        public int PasswordExpirationDays { get; set; }
        public int MaxFailedLoginAttempts { get; set; } // Before lockout
        public int AccountLockoutDurationMinutes { get; set; }

        public PasswordPolicy(Guid id, Guid tenantId) : base(id == Guid.Empty ? Guid.NewGuid() : id)
        {
            if (tenantId == Guid.Empty)
            {
                throw new ArgumentException("TenantId cannot be empty.", nameof(tenantId));
            }
            TenantId = tenantId;
            // Set default values
            MinLength = 8;
            RequireUppercase = true;
            RequireLowercase = true;
            RequireDigit = true;
            RequireSpecialCharacter = true;
            PasswordHistoryCount = 5;
            PasswordExpirationDays = 90;
            MaxFailedLoginAttempts = 5;
            AccountLockoutDurationMinutes = 30;
        }

        // Parameterless constructor for EF Core
        private PasswordPolicy() : base(Guid.NewGuid())
        {
            // Initialize TenantId to a sensible default if necessary, or ensure it's handled by EF.
            // For now, we rely on the public constructor to set TenantId.
            // If EF Core needs to create an instance and TenantId is non-nullable,
            // it might require further handling or a specific default here,
            // or making TenantId nullable if appropriate for the design.
            // However, since TenantId has an index, it's likely required.
            // Let's assume EF will require TenantId to be set upon creation or configuration.
            // Default values for value types are set automatically.
        }
    }
}
