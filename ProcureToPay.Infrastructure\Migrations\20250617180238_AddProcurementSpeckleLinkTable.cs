﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ProcureToPay.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddProcurementSpeckleLinkTable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "ProcurementSpeckleLinks",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    SpeckleObjectMetadataId = table.Column<Guid>(type: "uuid", nullable: false),
                    ProcurementItemType = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    ProcurementItemId = table.Column<Guid>(type: "uuid", nullable: false),
                    LinkedByUserId = table.Column<Guid>(type: "uuid", nullable: false),
                    LinkedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ProcurementSpeckleLinks", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ProcurementSpeckleLinks_P2PUsers_LinkedByUserId",
                        column: x => x.LinkedByUserId,
                        principalTable: "P2PUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_ProcurementSpeckleLinks_SpeckleObjectMetadata_SpeckleObject~",
                        column: x => x.SpeckleObjectMetadataId,
                        principalTable: "SpeckleObjectMetadata",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_ProcurementSpeckleLinks_LinkedByUserId",
                table: "ProcurementSpeckleLinks",
                column: "LinkedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_ProcurementSpeckleLinks_ProcurementItemType_ProcurementItem~",
                table: "ProcurementSpeckleLinks",
                columns: new[] { "ProcurementItemType", "ProcurementItemId" });

            migrationBuilder.CreateIndex(
                name: "IX_ProcurementSpeckleLinks_SpeckleObjectMetadataId",
                table: "ProcurementSpeckleLinks",
                column: "SpeckleObjectMetadataId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ProcurementSpeckleLinks");
        }
    }
}
