using Google.Authenticator;

using ProcureToPay.Domain.Entities; // For User entity
using ProcureToPay.Infrastructure.Persistence; // For ApplicationDbContext
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore; // For FirstOrDefaultAsync
using System; // For Guid, ArgumentNullException

namespace ProcureToPay.Application.Services
{
    public class MfaSetupDetails
    {
        public string ManualEntryKey { get; set; } = string.Empty;
        public string QrCodeSetupImageUrl { get; set; } = string.Empty; // Data URL for the QR code image
        public string TempSecretKey { get; set; } = string.Empty; // Temporary secret to verify setup
    }

    public interface IMfaService
    {
        Task<MfaSetupDetails?> GenerateMfaSetupAsync(Guid userId, string issuer, string accountTitle);
        Task<bool> EnableMfaAsync(Guid userId, string tempSecretKey, string totpCode);
        Task<bool> ValidateMfaCodeAsync(Guid userId, string totpCode);
        Task<bool> DisableMfaAsync(Guid userId, string totpCode); // Optional: for users to disable MFA
    }

    public class MfaService : IMfaService
    {
        private readonly ApplicationDbContext _context;

        public MfaService(ApplicationDbContext context)
        {
            _context = context;
        }

        public Task<MfaSetupDetails?> GenerateMfaSetupAsync(Guid userId, string issuer, string accountTitle)
        {
            var tfa = new TwoFactorAuthenticator();
            // Generate a unique secret key for the user.
            // This key should be stored securely if it's persisted before verification (e.g. in a temporary cache or user record).
            // For this flow, we'll treat it as a temporary secret passed to the client and verified in EnableMfaAsync.
            // The library generates a base32 encoded secret.
            var secretKey = Guid.NewGuid().ToString().Replace("-", ""); // Generate a suitably random secret.
                                                                        // The GoogleAuthenticator library itself can generate a secure secret if you provide a byte array.
                                                                        // For simplicity here, we create a random string.
                                                                        // A more robust approach: use RandomNumberGenerator to create a byte array, then Base32 encode it.
                                                                        // However, the GenerateSetupCode method below takes the plain text secret.

            // The library expects a plain text secret for GenerateSetupCode.
            // It will internally convert this to the required format for OTP generation.
            // Let's use a strong, unique key per user.
            // A common practice is to generate a 16-character base32 string for the secret.
            // For example purposes, we'll generate a random GUID and take a portion.
            // IMPORTANT: For production, ensure this secret is cryptographically strong and unique.
            // The library's internal mechanisms are generally secure, but the initial seed is crucial.
            // A GUID is not ideal for a crypto secret directly. Use a proper random byte generator.
            // For this example, we'll use a simpler approach that the library can work with.
            // The `GenerateSetupCode` itself doesn't require a pre-encoded secret; it takes a plain string.

            // The library's GenerateSetupCode method takes the account secret as a string.
            // This secret string should be high-entropy. For this example, we use a new GUID.
            // In production, ensure this string is cryptographically strong.
            // The library uses this string to derive the actual Base32 key (ManualEntryKey).
            // The string passed here as 'tempGeneratedSecret' is what should be stored by the user
            // and used for validation with ValidateTwoFactorPIN.
            var tempGeneratedSecret = Guid.NewGuid().ToString().Replace("-", "");

            var setupInfo = tfa.GenerateSetupCode(issuer, accountTitle, tempGeneratedSecret, false, 3);

            return Task.FromResult<MfaSetupDetails?>(new MfaSetupDetails
            {
                ManualEntryKey = setupInfo.ManualEntryKey, // This is the Base32 encoded secret
                QrCodeSetupImageUrl = setupInfo.QrCodeSetupImageUrl, // This is a data URI for the QR code image
                TempSecretKey = tempGeneratedSecret // Store the plain text secret temporarily for verification
            });
        }

        public async Task<bool> EnableMfaAsync(Guid userId, string tempSecretKey, string totpCode)
        {
            var user = await _context.P2PUsers.FindAsync(userId);
            if (user == null) return false;

            var tfa = new TwoFactorAuthenticator();
            // Validate the TOTP code using the *plain text* temporary secret key that was used to generate the setup.
            bool isValid = tfa.ValidateTwoFactorPIN(tempSecretKey, totpCode);

            if (isValid)
            {
                // Once validated, store the *plain text* secret key with the user.
                // The library will use this plain text secret for subsequent OTP validations.
                // Ensure MfaSecretKey field in User entity can store this.
                user.MfaSecretKey = tempSecretKey;
                user.IsMfaEnabled = true;
                await _context.SaveChangesAsync();
                return true;
            }
            return false;
        }

        public async Task<bool> ValidateMfaCodeAsync(Guid userId, string totpCode)
        {
            var user = await _context.P2PUsers.FindAsync(userId);
            if (user == null || !user.IsMfaEnabled || string.IsNullOrEmpty(user.MfaSecretKey))
            {
                return false;
            }

            var tfa = new TwoFactorAuthenticator();
            // Validate using the stored plain text secret key.
            return tfa.ValidateTwoFactorPIN(user.MfaSecretKey, totpCode);
        }

        public async Task<bool> DisableMfaAsync(Guid userId, string totpCode)
        {
            var user = await _context.P2PUsers.FindAsync(userId);
            if (user == null || !user.IsMfaEnabled || string.IsNullOrEmpty(user.MfaSecretKey))
            {
                return false; // MFA not enabled or user not found
            }

            var tfa = new TwoFactorAuthenticator();
            // Verify with current code before disabling, using the stored plain text secret.
            bool isValid = tfa.ValidateTwoFactorPIN(user.MfaSecretKey, totpCode);

            if (isValid)
            {
                user.IsMfaEnabled = false;
                user.MfaSecretKey = null; // Clear the secret key
                await _context.SaveChangesAsync();
                return true;
            }
            return false;
        }
    }
}

// Note: The GoogleAuthenticator library's KeyGeneration class is internal.
// If you need a utility for generating cryptographically strong keys for the initial secret,
// you'd typically use System.Security.Cryptography.RandomNumberGenerator.
// For example, to generate a 20-byte random key and then Base32 encode it:
//
// using System.Security.Cryptography;
//
// public static class SecureKeyGenerator
// {
//     public static stringGenerateBase32Secret(int length = 20) // Number of bytes before base32 encoding
//     {
//         byte[] buffer = new byte[length];
//         using (var rng = RandomNumberGenerator.Create())
//         {
//             rng.GetBytes(buffer);
//         }
//         // The GoogleAuthenticator library does not directly expose a Base32 encoder.
//         // You might need a separate Base32 encoding utility or rely on the ManualEntryKey
//         // from GenerateSetupCode if you pass it a raw byte array (if supported by that method, need to check lib specifics).
//         // However, GenerateSetupCode takes a string secret. So, the string itself should be the secret.
//         // A common approach is to use a string of allowed characters or let the library generate it if it has such a utility.
//         // The library's `GenerateSetupCode` seems to handle the "plain text" secret and derive necessary keys.
//         // The `ManualEntryKey` it returns IS the base32 secret.
//         // So, `tempGeneratedSecret` in `GenerateMfaSetupAsync` should be a high-entropy string.
//         // The library then Base32 encodes this for `ManualEntryKey`.
//
//         // Let's re-evaluate the secret generation in GenerateMfaSetupAsync.
//         // The library's internal secret generation might be tied to specific formats.
//         // The simplest is to provide a high-entropy string as the "account secret" to GenerateSetupCode.
//         // The ManualEntryKey returned by GenerateSetupCode is the one to show the user.
//         // The string you pass as "secret" to GenerateSetupCode is what you should store and use for ValidateTwoFactorPIN.
//         // So, `tempGeneratedSecret` should be cryptographically random.
//     }
// }
// The above comment block is for consideration. The current implementation uses a GUID portion,
// which is not ideal for cryptographic secrets. A better `tempGeneratedSecret` would be:
// byte[] randomBytes = RandomNumberGenerator.GetBytes(20); // 20 bytes = 160 bits
// string tempGeneratedSecret = Base32Encoding.ToString(randomBytes); // Using a Base32 encoder
// However, `GoogleAuthenticator.GenerateSetupCode` takes a `string secret`.
// It seems the library expects this `string secret` to be the actual secret, not pre-encoded.
// The `ManualEntryKey` it produces is the Base32 version for user display.
// So, the `tempGeneratedSecret` should be a high-entropy string.
// A GUID is somewhat random but not designed for crypto.
// A better `tempGeneratedSecret`:
// var tfa = new TwoFactorAuthenticator();
// var tempGeneratedSecret = tfa.GeneratePIN(); // This is not for secret generation, but for OTP.
// The library does not seem to expose a direct secret generator.
// So, using `Guid.NewGuid().ToString().Replace("-", "")` or similar high-entropy string and letting the library process it
// into the `ManualEntryKey` (Base32) and internal forms is the intended usage.
// The important part is that `user.MfaSecretKey` should store the same string that was passed as `secret` to `GenerateSetupCode`.
