using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProcureToPay.Domain.Entities.Catalog;

namespace ProcureToPay.Infrastructure.Persistence.Configurations.Catalog
{
    public class ProductCategoryConfiguration : IEntityTypeConfiguration<ProductCategory>
    {
        public void Configure(EntityTypeBuilder<ProductCategory> builder)
        {
            builder.ToTable("ProductCategories", "catalog");
            builder.HasKey(c => c.Id);
            builder.Property(c => c.Name).IsRequired().HasMaxLength(200);
            builder.Property(c => c.Description).HasMaxLength(1000).IsRequired(false); // Allow null
            builder.Property(c => c.IsActive).IsRequired();

            builder.HasOne(c => c.ParentCategory)
                   .WithMany(c => c.Subcategories)
                   .HasForeignKey(c => c.ParentCategoryId)
                   .IsRequired(false) // ParentCategoryId is nullable
                   .OnDelete(DeleteBehavior.Restrict);

            // Relationship with CatalogProducts is implicitly configured by CatalogProductConfiguration
            // or can be explicitly defined here if preferred.
            // builder.HasMany(c => c.CatalogProducts)
            //        .WithOne(p => p.Category)
            //        .HasForeignKey(p => p.CategoryId);
        }
    }
}
