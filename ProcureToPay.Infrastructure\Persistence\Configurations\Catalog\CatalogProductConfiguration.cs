using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProcureToPay.Domain.Entities.Catalog;
using ProcureToPay.Domain.Enums;

namespace ProcureToPay.Infrastructure.Persistence.Configurations.Catalog
{
    public class CatalogProductConfiguration : IEntityTypeConfiguration<CatalogProduct>
    {
        public void Configure(EntityTypeBuilder<CatalogProduct> builder)
        {
            builder.ToTable("CatalogProducts", "catalog");
            builder.HasKey(p => p.Id);

            builder.Property(p => p.Name).IsRequired().HasMaxLength(250);
            builder.Property(p => p.Description).HasMaxLength(4000).IsRequired(false);
            builder.Property(p => p.SellerOrgId).IsRequired();
            builder.HasIndex(p => p.SellerOrgId);

            builder.Property(p => p.ListPrice).HasColumnType("decimal(18,2)");

            builder.Property(p => p.ProductStatus)
                   .IsRequired()
                   .HasConversion<string>()
                   .HasMaxLength(50);

            builder.Property(p => p.UnitOfMeasure).HasMaxLength(50).IsRequired(false);
            builder.Property(p => p.StockQuantity).IsRequired(false);

            builder.Property(p => p.InventoryStatus)
                   .IsRequired()
                   .HasConversion<string>()
                   .HasMaxLength(50);

            builder.Property(p => p.AvailableDate).IsRequired(false);
            builder.Property(p => p.StandardLeadTimeDays).IsRequired(false);

            // For JSON column, specific DbType might be needed depending on provider e.g. jsonb for PostgreSQL
            // For now, HasColumnType("nvarchar(max)") is a general approach for SQL Server.
            // PostgreSQL would use .HasColumnType("jsonb")
            builder.Property(p => p.QuantityBasedPricingTiersJson).HasColumnType("text").IsRequired(false);

            builder.Property(p => p.SubmittedAt).IsRequired(false);
            builder.Property(p => p.ApprovedByUserId).IsRequired(false);
            builder.Property(p => p.ApprovedAt).IsRequired(false);
            builder.Property(p => p.RejectionReason).HasMaxLength(1000).IsRequired(false);
            builder.Property(p => p.Version).IsConcurrencyToken(); // Optimistic concurrency

            builder.HasOne(p => p.Category)
                   .WithMany(c => c.CatalogProducts)
                   .HasForeignKey(p => p.CategoryId)
                   .OnDelete(DeleteBehavior.Restrict);
        }
    }
}
