using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ProcureToPay.Application.DTOs.Profile;
using ProcureToPay.Application.Services;
using System;
using System.Security.Claims;
using System.Threading.Tasks;

namespace ProcureToPay.WebApp.Controllers
{
    [Authorize] // All endpoints in this controller require authentication
    [ApiController]
    [Route("api/profile")]
    public class UserProfileController : ControllerBase
    {
        private readonly IUserProfileService _userProfileService;
        private readonly IHttpContextAccessor _httpContextAccessor; // To get IP Address
        private readonly IPermissionValidationService _permissionValidationService; // Added

        public UserProfileController(
            IUserProfileService userProfileService,
            IHttpContextAccessor httpContextAccessor,
            IPermissionValidationService permissionValidationService) // Added
        {
            _userProfileService = userProfileService;
            _httpContextAccessor = httpContextAccessor;
            _permissionValidationService = permissionValidationService; // Added
        }

        private Guid GetCurrentUserId()
        {
            var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
            if (string.IsNullOrEmpty(userIdClaim) || !Guid.TryParse(userIdClaim, out var userId))
            {
                // This should ideally not happen if [Authorize] is effective
                throw new InvalidOperationException("User ID not found in token.");
            }
            return userId;
        }

        private string? GetIpAddress() => _httpContextAccessor.HttpContext?.Connection.RemoteIpAddress?.ToString();

        [HttpGet]
        // [Authorize(Policy = ProcureToPay.Domain.Constants.Permissions.ViewUserProfile)] // Policy removed for service-based check
        public async Task<IActionResult> GetUserProfile()
        {
            try
            {
                var userId = GetCurrentUserId();
                var profile = await _userProfileService.GetUserProfileAsync(userId);
                if (profile == null) return NotFound();
                return Ok(profile);
            }
            catch (InvalidOperationException ex)
            {
                return Unauthorized(new { Message = ex.Message });
            }
        }

        [HttpPut]
        // [Authorize(Policy = ProcureToPay.Domain.Constants.Permissions.UpdateUserProfile)] // Policy removed for service-based check
        public async Task<IActionResult> UpdateUserProfile([FromBody] UpdateUserProfileRequest request)
        {
            if (!ModelState.IsValid) return BadRequest(ModelState);
            try
            {
                var userId = GetCurrentUserId();
                var ipAddress = GetIpAddress();

                if (!await _permissionValidationService.HasPermissionAsync(userId, ProcureToPay.Domain.Constants.Permissions.UpdateUserProfile, ipAddress))
                {
                    return Forbid();
                }

                var (success, errors) = await _userProfileService.UpdateUserProfileAsync(userId, request, ipAddress);
                if (success) return NoContent(); // Or Ok("Profile updated");
                return BadRequest(new { Message = "Failed to update profile", Errors = errors });
            }
            catch (InvalidOperationException ex)
            {
                return Unauthorized(new { Message = ex.Message });
            }
        }

        [HttpPost("change-password")]
        // [Authorize(Policy = ProcureToPay.Domain.Constants.Permissions.ChangePassword)] // Policy removed for service-based check
        public async Task<IActionResult> ChangePassword([FromBody] ChangePasswordRequest request)
        {
            if (!ModelState.IsValid) return BadRequest(ModelState);
            try
            {
                var userId = GetCurrentUserId();
                var (success, errors) = await _userProfileService.ChangePasswordAsync(userId, request, GetIpAddress());
                if (success) return Ok(new { Message = "Password changed successfully." });
                return BadRequest(new { Message = "Failed to change password", Errors = errors });
            }
            catch (InvalidOperationException ex)
            {
                return Unauthorized(new { Message = ex.Message });
            }
        }

        [HttpGet("mfa-settings")]
        // [Authorize(Policy = ProcureToPay.Domain.Constants.Permissions.ManageMfaSettings)] // Policy removed for service-based check
        public async Task<IActionResult> GetMfaSettings()
        {
            try
            {
                var userId = GetCurrentUserId();
                // App name and account base can be configurable or constants
                var settings = await _userProfileService.GetMfaSettingsAsync(userId, "ProcureToPayApp", User.Identity?.Name ?? "user");
                if (settings == null) return NotFound(new { Message = "User not found or MFA settings could not be retrieved."});
                return Ok(settings);
            }
            catch (InvalidOperationException ex)
            {
                return Unauthorized(new { Message = ex.Message });
            }
        }

        public class EnableMfaApiRequest // DTO specific to this controller action
        {
            [Required]
            public string TempMfaSecret { get; set; } = string.Empty;
            [Required]
            [StringLength(6, MinimumLength = 6, ErrorMessage = "TOTP code must be 6 digits.")]
            public string TotpCode { get; set; } = string.Empty;
        }


        [HttpPost("mfa/enable")]
        // [Authorize(Policy = ProcureToPay.Domain.Constants.Permissions.ManageMfaSettings)] // Policy removed for service-based check
        public async Task<IActionResult> EnableMfa([FromBody] EnableMfaApiRequest request)
        {
            if (!ModelState.IsValid) return BadRequest(ModelState);
            try
            {
                var userId = GetCurrentUserId();
                var (success, errors) = await _userProfileService.EnableMfaAsync(userId, request.TempMfaSecret, request.TotpCode, GetIpAddress());
                if (success) return Ok(new { Message = "MFA enabled successfully." });
                return BadRequest(new { Message = "Failed to enable MFA", Errors = errors });
            }
            catch (InvalidOperationException ex)
            {
                return Unauthorized(new { Message = ex.Message });
            }
        }

        public class DisableMfaApiRequest // DTO specific to this controller action
        {
            [Required]
            [StringLength(6, MinimumLength = 6, ErrorMessage = "TOTP code must be 6 digits.")]
            public string TotpCode { get; set; } = string.Empty;
        }

        [HttpPost("mfa/disable")]
        // [Authorize(Policy = ProcureToPay.Domain.Constants.Permissions.ManageMfaSettings)] // Policy removed for service-based check
        public async Task<IActionResult> DisableMfa([FromBody] DisableMfaApiRequest request)
        {
             if (!ModelState.IsValid) return BadRequest(ModelState);
            try
            {
                var userId = GetCurrentUserId();
                var (success, errors) = await _userProfileService.DisableMfaAsync(userId, request.TotpCode, GetIpAddress());
                if (success) return Ok(new { Message = "MFA disabled successfully." });
                return BadRequest(new { Message = "Failed to disable MFA", Errors = errors });
            }
            catch (InvalidOperationException ex)
            {
                return Unauthorized(new { Message = ex.Message });
            }
        }
    }
}
