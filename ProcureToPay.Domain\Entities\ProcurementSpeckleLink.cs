using System;

namespace ProcureToPay.Domain.Entities
{
    public class ProcurementSpeckleLink : BaseEntity<Guid>
    {
        public Guid SpeckleObjectMetadataId { get; set; }
        public virtual SpeckleObjectMetadata SpeckleObjectMetadata { get; set; } = null!;

        public string ProcurementItemType { get; set; } = null!; // E.g., "PurchaseOrderLine", "RequisitionLine"
        public Guid ProcurementItemId { get; set; } // FK to the specific procurement item table

        public Guid LinkedByUserId { get; set; }
        public virtual User LinkedByUser { get; set; } = null!; // Navigation property to User

        public DateTime LinkedAt { get; set; }

        // Constructor for required fields
        public ProcurementSpeckleLink(
            Guid speckleObjectMetadataId,
            string procurementItemType,
            Guid procurementItemId,
            Guid linkedByUserId)
            : base(Guid.NewGuid())
        {
            SpeckleObjectMetadataId = speckleObjectMetadataId;
            ProcurementItemType = procurementItemType ?? throw new ArgumentNullException(nameof(procurementItemType));
            ProcurementItemId = procurementItemId;
            LinkedByUserId = linkedByUserId;
            LinkedAt = DateTime.UtcNow;
        }

        // Parameterless constructor for EF Core
        protected ProcurementSpeckleLink() : base(Guid.NewGuid())
        {
            // Properties will be set by EF Core.
            // Null forgiving operator used for non-nullable ones not set in constructor.
        }
    }
}
