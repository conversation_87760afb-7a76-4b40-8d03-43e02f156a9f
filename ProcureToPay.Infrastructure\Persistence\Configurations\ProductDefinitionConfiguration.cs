using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProcureToPay.Domain.Entities; // Assuming ProductDefinition, Category, VendorProduct entities exist here
using ProcureToPay.Domain.Enums;   // Assuming ProductLifecycleState enum exists
using System;

namespace ProcureToPay.Infrastructure.Persistence.Configurations
{
    /// <summary>
    /// EF Core Configuration for the ProductDefinition entity.
    /// Configures master product data, versioning, dynamic attributes, and relationships.
    /// </summary>
    public class ProductDefinitionConfiguration : IEntityTypeConfiguration<ProductDefinition>
    {

        public void Configure(EntityTypeBuilder<ProductDefinition> builder)
        {
            builder.ToTable("product_definitions");

            // Assuming ProductDefinition inherits from BaseEntity<Guid>
            builder.HasKey(pd => pd.Id);

            // --- Soft Delete Configuration ---
            // Assuming ProductDefinition has: public bool IsDeleted { get; private set; }
            // TODO: Add IsDeleted property to ProductDefinition entity
            builder.Property("IsDeleted") // Use shadow property if not on entity, or map directly
                   .HasDefaultValue(false)
                   .IsRequired();
            // Global query filter to exclude soft-deleted entities
            builder.HasQueryFilter(pd => !EF.Property<bool>(pd, "IsDeleted"));
            // Index to potentially improve query performance on active items
            builder.HasIndex("IsDeleted");


            // --- Properties ---
            // Using Sku as the DefinitionCode
            builder.Property(pd => pd.Sku)
                .IsRequired()
                .HasMaxLength(100);
            // Index on DefinitionCode (Sku) for quick lookups
            builder.HasIndex(pd => pd.Sku).IsUnique(); // Master SKU should be unique

            builder.Property(pd => pd.Name)
                .IsRequired()
                .HasMaxLength(250);

            builder.Property(pd => pd.Description)
                .HasMaxLength(2000);

            // GTIN/UPC/EAN codes
            builder.Property(pd => pd.Gtin).HasMaxLength(14);
            builder.HasIndex(pd => pd.Gtin).IsUnique().HasFilter(@"""gtin"" IS NOT NULL"); // PostgreSQL syntax for unique nullable

            builder.Property(pd => pd.Upc).HasMaxLength(12);
            builder.HasIndex(pd => pd.Upc).IsUnique().HasFilter(@"""upc"" IS NOT NULL");

            builder.Property(pd => pd.Ean).HasMaxLength(13);
            builder.HasIndex(pd => pd.Ean).IsUnique().HasFilter(@"""ean"" IS NOT NULL");

            // Enum Conversion for Lifecycle State
            builder.Property(pd => pd.LifecycleState)
                   .IsRequired()
                   .HasConversion<string>() // Store as string
                   .HasMaxLength(50)
                   .HasDefaultValue(ProductLifecycleState.Active);

            // --- Dynamic Attribute Sets (using JSONB for PostgreSQL) ---
            // Assuming ProductDefinition has: public string AttributesJson { get; private set; }
            // TODO: Add AttributesJson property (or Dictionary<string, object> Attributes) to ProductDefinition entity
            // TODO: Implement logic in Domain/Application layer to manage serialization/deserialization if using string property
            builder.Property("AttributesJson") // Or map direct Dictionary property if using EF Core 7+ .ToJson()
                   .HasColumnType("jsonb");
            // If using Dictionary property directly with EF Core 7+:
            // builder.Property(pd => pd.Attributes).ToJson();

            // --- Definition Versioning System (Manual Versioning Example) ---
            // Assuming ProductDefinition has: public int Version { get; private set; }
            // TODO: Add Version property to ProductDefinition entity
            // TODO: Implement version increment logic in Domain/Application layer upon significant changes
            builder.Property("Version") // Or map directly: builder.Property(pd => pd.Version)
                   .IsRequired()
                   .HasDefaultValue(1);
            // Optional: Could add concurrency check on Version if needed
            // builder.Property(pd => pd.Version).IsConcurrencyToken();


            // --- Relationships ---

            // Relationship to Category (Many ProductDefinitions to One Category)
            // Assumes Category entity has been updated to have: ICollection<ProductDefinition> ProductDefinitions
            // TODO: Update Category entity and configuration if needed
            builder.HasOne(pd => pd.Category)
                   .WithMany(c => c.ProductDefinitions) // Assumes Category has ICollection<ProductDefinition>
                   .HasForeignKey(pd => pd.CategoryId)
                   .IsRequired(false) // Product definition might not have a category
                   .OnDelete(DeleteBehavior.SetNull); // Setting category to null if deleted

            // Relationship to VendorProduct (One ProductDefinition to Many VendorProducts)
            // Configures how VendorProduct entities link to this definition
            builder.HasMany(pd => pd.VendorProducts) // Assumes ProductDefinition has ICollection<VendorProduct>
                   .WithOne(vp => vp.ProductDefinition) // Assumes VendorProduct has ProductDefinition navigation property
                   .HasForeignKey(vp => vp.ProductDefinitionId) // Assumes VendorProduct has ProductDefinitionId FK
                   .IsRequired() // A VendorProduct must link to a ProductDefinition
                   .OnDelete(DeleteBehavior.Restrict); // Prevent deleting definition if vendor offerings exist


            // --- Rules for Linked Entities ---
            // "Rules defining mandatory vs. optional attributes for linked Product entities"
            // This is typically business logic or metadata, not direct EF configuration.
            // These rules might be stored within the AttributesJson property,
            // or checked in the Application Service layer before creating/updating VendorProduct entities.
            // Example: Store required attributes schema within AttributesJson.


            // --- Tenant Isolation/Sharing Strategy ---
            // Note: Tenant filtering should be implemented at the DbContext level
            // Example:
            // builder.HasQueryFilter(pd => !EF.Property<bool>(pd, "IsDeleted") && pd.TenantId == currentTenantId);
            // Where currentTenantId is obtained from the DbContext or a tenant provider service

        }
    }
}

