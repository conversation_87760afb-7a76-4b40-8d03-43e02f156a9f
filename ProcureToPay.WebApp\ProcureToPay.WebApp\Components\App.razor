﻿<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <base href="/" />
    <link rel="stylesheet" href="@Assets["lib/bootstrap/dist/css/bootstrap.min.css"]" />
    <link rel="stylesheet" href="@Assets["app.css"]" />
    <link rel="stylesheet" href="@Assets["ProcureToPay.WebApp.styles.css"]" />
    <ImportMap />
    <link rel="icon" type="image/png" href="favicon.png" />
    <HeadOutlet @rendermode="PageRenderMode" />
</head>

<body>
    <Routes @rendermode="PageRenderMode" />
    <script src="_framework/blazor.web.js"></script>
    <!--
        In a real application, the Speckle Viewer library would be included here,
        either from a CDN or as a bundled asset from an npm package.
        Example using a hypothetical CDN link for the ES Module version:
        <script type="module" src="https://cdn.jsdelivr.net/npm/@speckle/viewer@2/dist/index.esm.js"></script>
        Or for UMD version:
        <script src="https://cdn.jsdelivr.net/npm/@speckle/viewer@2/dist/index.umd.min.js"></script>

        For this subtask, we are using a local placeholder script.
    -->
    <script src="js/speckleViewer.js"></script> <!-- Ensure path is correct from wwwroot -->
</body>

</html>

@code {
    [CascadingParameter]
    private HttpContext HttpContext { get; set; } = default!;

    private IComponentRenderMode? PageRenderMode =>
        HttpContext.AcceptsInteractiveRouting() ? InteractiveAuto : null;
}
