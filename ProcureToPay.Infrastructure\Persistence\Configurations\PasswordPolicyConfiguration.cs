using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProcureToPay.Domain.Entities;

namespace ProcureToPay.Infrastructure.Persistence.Configurations
{
    public class PasswordPolicyConfiguration : IEntityTypeConfiguration<PasswordPolicy>
    {
        public void Configure(EntityTypeBuilder<PasswordPolicy> builder)
        {
            builder.HasKey(p => p.Id);

            builder.Property(p => p.TenantId).IsRequired();
            // It's common to have one policy per tenant, or a global default.
            // If TenantId is part of a unique constraint, ensure your seeding/logic handles this.
            // For a single global policy, you might remove TenantId or have a known Guid for the global tenant.
            builder.HasIndex(p => p.TenantId).IsUnique(); // Assuming one policy per tenant.

            builder.Property(p => p.MinLength).IsRequired();
            builder.Property(p => p.RequireUppercase).IsRequired();
            builder.Property(p => p.RequireLowercase).IsRequired();
            builder.Property(p => p.RequireDigit).IsRequired();
            builder.Property(p => p.RequireSpecialCharacter).IsRequired();
            builder.Property(p => p.PasswordHistoryCount).IsRequired();
            builder.Property(p => p.PasswordExpirationDays).IsRequired();
            builder.Property(p => p.MaxFailedLoginAttempts).IsRequired();
            builder.Property(p => p.AccountLockoutDurationMinutes).IsRequired();
        }
    }
}
