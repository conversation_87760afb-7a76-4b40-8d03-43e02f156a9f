using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProcureToPay.Domain.Entities; // Assuming Vendor, Supplier, PO, VP, Contract entities exist here
using ProcureToPay.Domain.ValueObjects; // Assuming Address VO exists here
using ProcureToPay.Domain.Enums; // Assuming VendorStatus enum exists
using System;

namespace ProcureToPay.Infrastructure.Persistence.Configurations
{
    /// <summary>
    /// EF Core Configuration for the Vendor entity.
    /// Maps the enhanced Vendor entity properties, relationships, and concurrency settings.
    /// </summary>
    public class VendorConfiguration : IEntityTypeConfiguration<Vendor>
    {
        // Placeholder for Tenant ID - Implement when adding tenant isolation
        // private readonly Guid? _tenantId;

        public void Configure(EntityTypeBuilder<Vendor> builder)
        {
            builder.ToTable("vendors");

            // Assuming Vendor inherits from BaseEntity<Guid>
            builder.HasKey(v => v.Id);

            // --- Properties ---
            builder.Property(v => v.Name)
                .IsRequired()
                .HasMaxLength(250);

            builder.Property(v => v.VendorCode) // Optional unique code
                .HasMaxLength(50);
            // Unique index on VendorCode, but only when it's not null (PostgreSQL syntax)
            builder.HasIndex(v => v.VendorCode).IsUnique().HasFilter(@"""vendor_code"" IS NOT NULL");

            // Map Status Enum
            builder.Property(v => v.Status)
                .IsRequired()
                .HasConversion<string>() // Store as string
                .HasMaxLength(50)
                .HasDefaultValue(VendorStatus.Pending);

            // Identification Numbers
            builder.Property(v => v.VatNumber).HasMaxLength(50); // Adjust length as needed
            builder.HasIndex(v => v.VatNumber); // Index if frequently queried

            builder.Property(v => v.CommercialRegistrationNumber).HasMaxLength(50); // Adjust length
            builder.HasIndex(v => v.CommercialRegistrationNumber);

            builder.Property(v => v.TaxId).HasMaxLength(50); // Adjust length
            builder.HasIndex(v => v.TaxId);

            // Contact Information
            builder.Property(v => v.ContactName).HasMaxLength(150);
            builder.Property(v => v.ContactEmail).HasMaxLength(254); // Standard max email length
            builder.Property(v => v.PhoneNumber).HasMaxLength(50);
            builder.Property(v => v.Website).HasMaxLength(500);


            // --- Value Object Embedding (Primary Address) ---
            // Assumes Vendor has: public Address PrimaryAddress { get; private set; }
            builder.OwnsOne(v => v.PrimaryAddress, addr =>
            {
                // Configure Address properties, prefixing column names
                addr.Property(a => a.Street).HasColumnName("primary_address_street").HasMaxLength(200).IsRequired();
                addr.Property(a => a.City).HasColumnName("primary_address_city").HasMaxLength(100).IsRequired();
                addr.Property(a => a.State).HasColumnName("primary_address_state").HasMaxLength(100).IsRequired();
                addr.Property(a => a.Country).HasColumnName("primary_address_country").HasMaxLength(100).IsRequired();
                addr.Property(a => a.PostalCode).HasColumnName("primary_address_postal_code").HasMaxLength(20).IsRequired();
            });
            // Mark the navigation to the owned type as required if Address cannot be null
            builder.Navigation(v => v.PrimaryAddress).IsRequired();


            // --- Concurrency Control (PostgreSQL xmin - EF Core 7+) ---
            // Configure a shadow property named "xmin" of type uint
            // and map it to the PostgreSQL xid system column using IsConcurrencyToken() or IsRowVersion().
            // No corresponding property is needed on the Vendor entity class itself.
            builder.Property<uint>("xmin")
                   .HasColumnType("xid") // Explicitly map to PostgreSQL xid type
                   .ValueGeneratedOnAddOrUpdate() // Indicate it's generated by the database
                   .IsConcurrencyToken(); // Use IsConcurrencyToken() (or IsRowVersion())


            // --- Relationships ---

            // Relationship to Supplier (One-to-One, configured on Supplier side with FK)
            // We configure the inverse navigation property here if needed for queries from Vendor
            // Assumes Vendor has: public virtual Supplier? SupplierProfile { get; private set; }
            builder.HasOne(v => v.SupplierProfile)
                   .WithOne(s => s.Vendor) // Matches the navigation property on Supplier
                   .HasForeignKey<Supplier>(s => s.VendorId); // FK is on Supplier

            // Relationship to PurchaseOrders (One Vendor to Many POs)
            // Assumes Vendor has: public virtual ICollection<PurchaseOrder> PurchaseOrders { get; private set; }
            builder.HasMany(v => v.PurchaseOrders)
                   .WithOne(po => po.Vendor) // Matches navigation property on PurchaseOrder
                   .HasForeignKey(po => po.VendorId) // FK on PurchaseOrder
                   .IsRequired() // PO must have a vendor
                   .OnDelete(DeleteBehavior.Restrict); // Prevent deleting vendor if POs exist

            // Relationship to VendorProducts (One Vendor to Many VendorProducts)
            // Assumes Vendor has: public virtual ICollection<VendorProduct> VendorProducts { get; private set; }
            builder.HasMany(v => v.VendorProducts)
                   .WithOne(vp => vp.Vendor) // Matches navigation property on VendorProduct
                   .HasForeignKey(vp => vp.VendorId) // FK on VendorProduct
                   .IsRequired() // VendorProduct must have a vendor
                   .OnDelete(DeleteBehavior.Cascade); // If vendor deleted, delete their product offerings. Review if Restrict is better.

            // Relationship to Contracts (One Vendor to Many Contracts)
            // Assumes Vendor has: public virtual ICollection<Contract> Contracts { get; private set; }
            // Assumes Contract entity has VendorId FK and Vendor navigation property
            builder.HasMany(v => v.Contracts)
                   .WithOne(c => c.Vendor) // Matches navigation property on Contract
                   .HasForeignKey(c => c.VendorId) // FK on Contract
                   .IsRequired(false) // Can a contract exist without a vendor? Make IsRequired() if not.
                   .OnDelete(DeleteBehavior.Restrict); // Prevent deleting vendor if Contracts exist


            // --- Indexes ---
            // Included unique index on VendorCode above.
            builder.HasIndex(v => v.Name); // Index for searching/sorting by name
            builder.HasIndex(v => v.Status); // Index for filtering by status


            // --- Tenant Isolation (Deferred) ---
            // Placeholder for multi-tenancy configuration
            // TODO: Implement Tenant Isolation in next phase if required
            // builder.Property<Guid>("TenantId").IsRequired();
            // builder.HasIndex("TenantId");
            // builder.HasQueryFilter(v => EF.Property<Guid>(v, "TenantId") == _tenantId);

        }
    }
}

