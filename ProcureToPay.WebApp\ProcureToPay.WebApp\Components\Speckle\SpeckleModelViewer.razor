@page "/speckle-viewer/{ProjectLinkIdString}"
@inject HttpClient Http
@inject IJSRuntime JSRuntime
@inject NavigationManager NavigationManager
@using ProcureToPay.Application.DTOs.Speckle
@using Microsoft.AspNetCore.Components.Authorization // Required for CascadingAuthenticationState and AuthorizeView
@using System.Security.Claims // Required for accessing user claims

<PageTitle>Speckle Model Viewer</PageTitle>

<AuthorizeView>
    <Authorized>
        <h3>Speckle Model Viewer</h3>

        @if (linkDetails == null && !string.IsNullOrEmpty(errorMessage))
        {
            <p class="text-danger">@errorMessage</p>
        }
        else if (linkDetails != null)
        {
            <p>Loading viewer for Stream: <strong>@linkDetails.SpeckleStreamId</strong> on Server: <strong>@linkDetails.SpeckleServerUrl</strong></p>
            <p>Commit ID: <strong>@(linkDetails.SpeckleCommitId ?? "latest")</strong></p>
            <div id="speckle-viewer-container" style="width: 100%; height: 70vh; min-height: 500px; border: 1px solid black; position: relative;">
                </div>
            <p><em>Viewer integration requires JavaScript. Ensure <code>wwwroot/js/speckleViewer.js</code> is correctly implemented and Speckle Viewer library is loaded.</em></p>
        }
        else
        {
            <p><em>Loading link details...</em></p>
        }
    </Authorized>
    <NotAuthorized>
        <p>You are not authorized to view this page. Please log in.</p>
    </NotAuthorized>
</AuthorizeView>


@code {
    [Parameter]
    public string? ProjectLinkIdString { get; set; }

    private SpeckleProjectLinkDto? linkDetails;
    private List<SpeckleObjectProcurementStatusDto>? objectStatuses;
    private string? errorMessage;
    private bool viewerInitialized = false;
    private bool statusesApplied = false;

    [CascadingParameter]
    private Task<AuthenticationState>? authenticationStateTask { get; set; }

    protected override async Task OnInitializedAsync()
    {
        if (string.IsNullOrEmpty(ProjectLinkIdString))
        {
            errorMessage = "Project Link ID is required.";
            return;
        }

        if (Guid.TryParse(ProjectLinkIdString, out Guid projectLinkId))
        {
            try
            {
                // Ensure user is authenticated before making API calls
                if (authenticationStateTask != null) {
                    var authState = await authenticationStateTask;
                    if (authState?.User?.Identity?.IsAuthenticated != true)
                    {
                        errorMessage = "User is not authenticated.";
                        // Optionally redirect to login: NavigationManager.NavigateTo($"Account/Login?returnUrl={NavigationManager.Uri}", true);
                        return;
                    }
                } else {
                     errorMessage = "Authentication state not available.";
                     return;
                }

                linkDetails = await Http.GetFromJsonAsync<SpeckleProjectLinkDto>($"/api/speckle/links/{projectLinkId}");

                if (linkDetails != null)
                {
                    // Fetch object statuses
                    objectStatuses = await Http.GetFromJsonAsync<List<SpeckleObjectProcurementStatusDto>>(
                        $"/api/speckle/projectlinks/{projectLinkId}/objectstatuses");
                }
            }
            catch (HttpRequestException ex) when (ex.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                errorMessage = $"Speckle link details not found for ID {projectLinkId}.";
            }
            catch (Exception ex)
            {
                errorMessage = $"Error loading Speckle link details: {ex.Message}";
            }
        }
        else
        {
            errorMessage = "Invalid Project Link ID format.";
        }
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender && linkDetails != null && !viewerInitialized)
        {
            try
            {
                // SECURITY NOTE: Passing SpeckleAuthToken directly to client-side JS is a risk.
                await JSRuntime.InvokeVoidAsync("speckleViewer.init",
                    "speckle-viewer-container",
                    linkDetails.SpeckleServerUrl,
                    linkDetails.SpeckleStreamId,
                    linkDetails.SpeckleCommitId ?? "latest",
                    linkDetails.SpeckleAuthToken);
                viewerInitialized = true; // Mark viewer as initialized
            }
            catch (Exception ex)
            {
                errorMessage = $"Error initializing Speckle viewer: {ex.Message}.";
                StateHasChanged();
            }
        }

        // Apply statuses after viewer is initialized and statuses are fetched
        if (viewerInitialized && objectStatuses != null && !statusesApplied)
        {
            try
            {
                await JSRuntime.InvokeVoidAsync("speckleViewer.applyObjectStatuses", objectStatuses);
                statusesApplied = true; // Mark statuses as applied
            }
            catch (Exception ex)
            {
                errorMessage = $"Error applying object statuses: {ex.Message}. {errorMessage}";
                StateHasChanged();
            }
        }
    }
}
