using Microsoft.AspNetCore.Mvc;
using ProcureToPay.Application.DTOs.Speckle;
using ProcureToPay.Application.Services.Speckle;
using System;
using System.Security.Claims;
using System.Threading.Tasks;
// using Microsoft.AspNetCore.Authorization; // Uncomment when authorization is fully set up

namespace ProcureToPay.WebApp.Controllers
{
    [ApiController]
    [Route("api/speckle")]
    // [Authorize] // General authorization for the controller (e.g., user must be logged in)
    public class SpeckleController : ControllerBase
    {
        private readonly ISpeckleLinkService _speckleLinkService;
        private readonly IProcurementSpeckleLinkService _procurementSpeckleLinkService;
        private readonly IBomService _bomService;

        public SpeckleController(
            ISpeckleLinkService speckleLinkService,
            IProcurementSpeckleLinkService procurementSpeckleLinkService,
            IBomService bomService)
        {
            _speckleLinkService = speckleLinkService;
            _procurementSpeckleLinkService = procurementSpeckleLinkService;
            _bomService = bomService;
        }

        private Guid GetCurrentUserId()
        {
            // Example: Extract user ID from claims. Adjust if your user ID claim is different.
            var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
            if (Guid.TryParse(userIdClaim, out Guid userId))
            {
                return userId;
            }
            // Fallback or error handling if user ID is not found or invalid.
            // Forcing a default Guid might be a security risk if not handled carefully.
            // Consider throwing an exception or returning Unauthorized.
            throw new UnauthorizedAccessException("User ID could not be determined.");
        }

        /// <summary>
        /// Links a P2P Project to a Speckle Stream.
        /// </summary>
        /// <param name="request">The details for creating the Speckle link.</param>
        /// <returns>The created Speckle project link details.</returns>
        [HttpPost("links")]
        // [Authorize(Roles = "ProjectManager,BuyerAdmin")] // Example role-based authorization
        public async Task<IActionResult> CreateSpeckleLink([FromBody] CreateSpeckleLinkRequest request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            try
            {
                var userId = GetCurrentUserId(); // Get user ID for auditing/permissions
                var result = await _speckleLinkService.CreateLinkAsync(request, userId);
                return CreatedAtAction(nameof(GetSpeckleLinkByP2PProjectId), new { p2pProjectId = result.P2PProjectId }, result);
            }
            catch (InvalidOperationException ex) // e.g., link already exists
            {
                return Conflict(new { message = ex.Message });
            }
            catch (ArgumentException ex) // e.g., project not found
            {
                return NotFound(new { message = ex.Message });
            }
            catch (UnauthorizedAccessException ex)
            {
                return Unauthorized(new { message = ex.Message });
            }
            // Add more specific exception handling as needed
        }

        /// <summary>
        /// Gets the Speckle link information for a given P2P Project ID.
        /// </summary>
        /// <param name="p2pProjectId">The ID of the P2P Project.</param>
        /// <returns>The Speckle project link details if found.</returns>
        [HttpGet("links/project/{p2pProjectId:guid}")]
        // [Authorize] // Or more specific permission/role
        public async Task<IActionResult> GetSpeckleLinkByP2PProjectId(Guid p2pProjectId)
        {
            try
            {
                // Optionally, check if user has access to this p2pProjectId before fetching.
                var result = await _speckleLinkService.GetLinkByP2PProjectIdAsync(p2pProjectId);
                if (result == null)
                {
                    return NotFound(new { message = $"No Speckle link found for P2P Project ID {p2pProjectId}." });
                }
                return Ok(result);
            }
            catch (UnauthorizedAccessException ex)
            {
                 return Unauthorized(new { message = ex.Message });
            }
        }

        /// <summary>
        /// Gets a specific Speckle link by its own ID.
        /// </summary>
        /// <param name="linkId">The ID of the Speckle link.</param>
        /// <returns>The Speckle project link details if found.</returns>
        [HttpGet("links/{linkId:guid}")]
        // [Authorize]
        public async Task<IActionResult> GetSpeckleLinkById(Guid linkId)
        {
            try
            {
                 var result = await _speckleLinkService.GetLinkByIdAsync(linkId);
                 if (result == null)
                 {
                     return NotFound(new { message = $"No Speckle link found with ID {linkId}." });
                 }
                 return Ok(result);
            }
            catch (UnauthorizedAccessException ex)
            {
                 return Unauthorized(new { message = ex.Message });
            }
        }

        /// <summary>
        /// Deletes a Speckle link.
        /// </summary>
        /// <param name="linkId">The ID of the Speckle link to delete.</param>
        [HttpDelete("links/{linkId:guid}")]
        // [Authorize(Roles = "ProjectManager,BuyerAdmin")]
        public async Task<IActionResult> DeleteSpeckleLink(Guid linkId)
        {
            try
            {
                var userId = GetCurrentUserId();
                var success = await _speckleLinkService.DeleteLinkAsync(linkId, userId);
                if (!success)
                {
                    return NotFound(new { message = $"Speckle link with ID {linkId} not found or could not be deleted." });
                }
                return NoContent(); // Successfully deleted
            }
            catch (UnauthorizedAccessException ex)
            {
                 return Unauthorized(new { message = ex.Message });
            }
        }

        /// <summary>
        /// Gets the procurement status for all Speckle objects within a given SpeckleProjectLink.
        /// Used by the viewer to color-code objects.
        /// </summary>
        [HttpGet("projectlinks/{speckleProjectLinkId:guid}/objectstatuses")]
        // [Authorize]
        public async Task<IActionResult> GetSpeckleObjectStatuses(Guid speckleProjectLinkId)
        {
            try
            {
                // Optional: Add permission check for speckleProjectLinkId access
                var results = await _speckleLinkService.GetSpeckleObjectStatusesAsync(speckleProjectLinkId);
                return Ok(results);
            }
            catch (UnauthorizedAccessException ex)
            {
                return Unauthorized(new { message = ex.Message });
            }
            // Consider other exceptions like NotFound if speckleProjectLinkId is invalid, though the service might return empty list.
        }

        // TODO: Add endpoints for listing all links (GetAllLinksAsync) with pagination
        // TODO: Add PUT endpoint for UpdateLinkAsync
        // TODO: Add PATCH endpoint for UpdateLinkCommitAsync

        // --- ProcurementSpeckleLink Endpoints ---

        /// <summary>
        /// Creates a link between a Speckle object and a procurement item.
        /// </summary>
        [HttpPost("itemlinks")]
        // [Authorize(Roles = "Buyer,Requisitioner")] // Example authorization
        public async Task<IActionResult> CreateProcurementSpeckleLink([FromBody] CreateProcurementSpeckleLinkRequest request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }
            try
            {
                var userId = GetCurrentUserId();
                var result = await _procurementSpeckleLinkService.CreateLinkAsync(request, userId);
                if (result == null)
                {
                    // This case might happen if the service implements "get or create" and returns existing without error
                    // Or if creation genuinely fails without an exception that ArgumentException covers (e.g. metadata/user not found)
                    return Conflict(new { message = "Failed to create the link or link already exists and no new link was made."});
                }
                // Return 201 Created with the DTO, pointing to a "GetById" for this new resource type
                return CreatedAtAction(nameof(GetProcurementSpeckleLinkById), new { linkId = result.Id }, result);
            }
            catch (ArgumentException ex) // Covers cases like SpeckleObjectMetadataId not found, User not found
            {
                return NotFound(new { message = ex.Message });
            }
            catch (InvalidOperationException ex) // E.g. if service explicitly throws for duplicate
            {
                return Conflict(new { message = ex.Message });
            }
            catch (UnauthorizedAccessException ex)
            {
                return Unauthorized(new { message = ex.Message });
            }
        }

        /// <summary>
        /// Gets a specific procurement speckle link by its ID.
        /// </summary>
        [HttpGet("itemlinks/{linkId:guid}")]
        // [Authorize]
        public async Task<IActionResult> GetProcurementSpeckleLinkById(Guid linkId)
        {
            try
            {
                var result = await _procurementSpeckleLinkService.GetLinkByIdAsync(linkId);
                if (result == null)
                {
                    return NotFound();
                }
                return Ok(result);
            }
            catch (UnauthorizedAccessException ex)
            {
                return Unauthorized(new { message = ex.Message });
            }
        }


        /// <summary>
        /// Gets all links for a specific procurement item.
        /// </summary>
        [HttpGet("itemlinks/procurement/{itemType}/{itemId:guid}")]
        // [Authorize]
        public async Task<IActionResult> GetLinksByProcurementItem(string itemType, Guid itemId)
        {
            try
            {
                var results = await _procurementSpeckleLinkService.GetLinksByProcurementItemAsync(itemType, itemId);
                return Ok(results);
            }
            catch (UnauthorizedAccessException ex)
            {
                return Unauthorized(new { message = ex.Message });
            }
        }

        /// <summary>
        /// Gets all links for a specific Speckle object metadata entry.
        /// </summary>
        [HttpGet("itemlinks/speckleobject/{speckleObjectMetadataId:guid}")]
        // [Authorize]
        public async Task<IActionResult> GetLinksBySpeckleObject(Guid speckleObjectMetadataId)
        {
            try
            {
                var results = await _procurementSpeckleLinkService.GetLinksBySpeckleObjectAsync(speckleObjectMetadataId);
                return Ok(results);
            }
            catch (UnauthorizedAccessException ex)
            {
                return Unauthorized(new { message = ex.Message });
            }
        }

        /// <summary>
        /// Deletes a specific procurement speckle link.
        /// </summary>
        [HttpDelete("itemlinks/{linkId:guid}")]
        // [Authorize(Roles = "Buyer,Requisitioner")] // Or based on who created it
        public async Task<IActionResult> DeleteProcurementSpeckleLink(Guid linkId)
        {
            try
            {
                var userId = GetCurrentUserId(); // For permission check in service, if implemented
                var success = await _procurementSpeckleLinkService.DeleteLinkAsync(linkId, userId);
                if (!success)
                {
                    return NotFound(new { message = "Link not found or user does not have permission to delete."});
                }
                return NoContent();
            }
            catch (UnauthorizedAccessException ex)
            {
                return Unauthorized(new { message = ex.Message });
            }
        }

        // --- BOM Endpoints ---

        /// <summary>
        /// Generates a Bill of Materials (BOM) from Speckle object metadata.
        /// </summary>
        [HttpPost("boms/generate")]
        // [Authorize] // Or specific roles like "Estimator", "ProjectManager"
        public async Task<IActionResult> GenerateBillOfMaterial([FromBody] BOMGenerationRequestDto request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }
            try
            {
                // Note: GetCurrentUserId() could be used here if BOM generation needs user context,
                // e.g., for permissions or audit logging, but the service doesn't require it currently.
                var bom = await _bomService.GenerateBomAsync(request);
                return Ok(bom);
            }
            catch (ArgumentException ex) // E.g., if SpeckleProjectLinkId is invalid or not found
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex) // Catch-all for other unexpected errors during BOM generation
            {
                // Log the exception ex
                return StatusCode(StatusCodes.Status500InternalServerError, "An unexpected error occurred while generating the BOM.");
            }
        }
    }
}
