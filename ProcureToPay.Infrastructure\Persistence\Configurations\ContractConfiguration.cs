using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProcureToPay.Domain.Entities; // Assuming Contract, Vendor, PurchaseOrder entities exist
using ProcureToPay.Domain.ValueObjects; // Assuming Money VO exists
using ProcureToPay.Domain.Enums; // Assuming ContractStatus enum exists
using System;

namespace ProcureToPay.Infrastructure.Persistence.Configurations
{
    /// <summary>
    /// EF Core Configuration for the Contract entity.
    /// Maps properties including versioning, soft delete, JSON fields, and relationships.
    /// </summary>
    public class ContractConfiguration : IEntityTypeConfiguration<Contract>
    {

        public void Configure(EntityTypeBuilder<Contract> builder)
        {
            builder.ToTable("contracts");

            // Assuming Contract inherits from BaseEntity<Guid>
            builder.HasKey(c => c.Id);

            // --- Soft Delete Configuration ---
            // Assumes Contract has: public bool IsDeleted { get; private set; }
            builder.Property(c => c.IsDeleted)
                   .HasDefaultValue(false)
                   .IsRequired();
            // Global query filter to exclude soft-deleted entities
            builder.HasQueryFilter(c => !c.IsDeleted);
            builder.HasIndex(c => c.IsDeleted);


            // --- Concurrency Control (PostgreSQL xmin - EF Core 7+) ---
            builder.Property<uint>("xmin")
                   .HasColumnType("xid")
                   .ValueGeneratedOnAddOrUpdate()
                   .IsConcurrencyToken();


            // --- Properties ---
            builder.Property(c => c.ContractNumber)
                .IsRequired()
                .HasMaxLength(100);
            builder.HasIndex(c => c.ContractNumber).IsUnique(); // Contract numbers should be unique

            builder.Property(c => c.Title)
                .IsRequired()
                .HasMaxLength(250);

            builder.Property(c => c.ContractType)
                .HasMaxLength(100);

            builder.Property(c => c.StartDate)
                .IsRequired();
            builder.HasIndex(c => c.StartDate);

            builder.Property(c => c.EndDate)
                .IsRequired(); // Still required in DB, logic handles flexibility
            builder.HasIndex(c => c.EndDate);

            // Map Status Enum
            builder.Property(c => c.Status)
                .IsRequired()
                .HasConversion<string>()
                .HasMaxLength(50);
            builder.HasIndex(c => c.Status);

            // Terms
            builder.Property(c => c.PaymentTerms).HasMaxLength(200);
            builder.Property(c => c.RenewalTerms).HasMaxLength(500);
            builder.Property(c => c.IsAutoRenew).IsRequired().HasDefaultValue(false);
            builder.Property(c => c.TerminationPenaltyTerms).HasMaxLength(500);
            builder.Property(c => c.TermsAndConditions); // Max length determined by provider default for text/varchar(max)

            // Scheduling & Tracking (JSON fields for PostgreSQL)
            builder.Property(c => c.MilestonesJson).HasColumnType("jsonb");
            builder.Property(c => c.SlaDetailsJson).HasColumnType("jsonb");

            // Compliance & Performance
            builder.Property(c => c.ComplianceDocumentLinksJson).HasColumnType("jsonb");
            builder.Property(c => c.VendorPerformanceScoreSnapshot).HasPrecision(5, 2); // e.g., 0.00 to 100.00

            // Versioning
            builder.Property(c => c.Version)
                .IsRequired()
                .HasDefaultValue(1);
            // Note: No IsConcurrencyToken here as we use xmin for concurrency


            // --- Value Object Mapping (Money for TotalContractValue) ---
            // Configure TotalContractValue (nullable Money VO)
            builder.OwnsOne(c => c.TotalContractValue, total =>
            {
                // Map properties of the Money VO to columns
                total.Property(m => m.Amount)
                    .HasColumnName("total_contract_value_amount") // Explicit column name
                    .HasPrecision(18, 4) // Consistent precision
                    .IsRequired(); // Amount is required if Money object exists

                total.Property(m => m.CurrencyCode)
                    .HasColumnName("total_contract_value_currency_code") // Explicit column name
                    .HasMaxLength(3) // Standard ISO code length
                    .IsRequired(); // Currency is required if Money object exists
            });
            // Navigation is implicitly optional because TotalContractValue property is nullable


            // --- Relationships ---

            // Relationship to Vendor (Many Contracts to One Vendor)
            builder.HasOne(c => c.Vendor)
                   .WithMany(v => v.Contracts) // Assumes Vendor has ICollection<Contract> Contracts
                   .HasForeignKey(c => c.VendorId)
                   .IsRequired() // Contract must have a Vendor
                   .OnDelete(DeleteBehavior.Restrict); // Prevent deleting Vendor if Contracts exist

            // Relationship to PurchaseOrders (One Contract to Many POs)
            builder.HasMany(c => c.PurchaseOrders)
                   .WithOne(po => po.Contract) // Assumes PurchaseOrder has Contract navigation property
                   .HasForeignKey(po => po.ContractId) // Assumes PurchaseOrder has nullable ContractId FK
                   .IsRequired(false) // PO doesn't always require a Contract
                   .OnDelete(DeleteBehavior.SetNull); // If Contract deleted, nullify FK on PO


            // --- Tenant Isolation ---
            // Assuming Contract belongs to a Tenant or can be shared
            // Assumes Contract has: public Guid? TenantId { get; private set; }
            builder.Property(c => c.TenantId).IsRequired(false); // Allow null for shared contracts
            builder.HasIndex(c => c.TenantId);
            // --- Tenant Isolation ---
            // Note: Tenant filtering should be implemented at the DbContext level
            // Example:
            // builder.HasQueryFilter(c => !c.IsDeleted && (c.TenantId == currentTenantId || c.TenantId == null));
            // Where currentTenantId is obtained from the DbContext or a tenant provider service

            // For now, just implement the soft delete filter
            builder.HasQueryFilter(c => !c.IsDeleted);


            // --- Indexes ---
            // Included unique index on ContractNumber and indexes on dates, status, VendorId, TenantId above.

        }
    }
}

