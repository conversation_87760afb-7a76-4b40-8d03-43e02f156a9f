using System;

namespace ProcureToPay.Application.DTOs.Audit
{
    public class AuditLogQueryParameters
    {
        public Guid? UserId { get; set; }
        public Guid? PerformingUserId { get; set; }
        public string? ActionType { get; set; }
        public DateTime? DateFrom { get; set; }
        public DateTime? DateTo { get; set; }
        public string? TargetEntityType { get; set; }
        public string? TargetEntityId { get; set; }
        public bool? Success { get; set; } // Filter by outcome

        private const int MaxPageSize = 100;
        private int _pageSize = 20;
        public int PageNumber { get; set; } = 1;
        public int PageSize
        {
            get => _pageSize;
            set => _pageSize = (value > MaxPageSize) ? MaxPageSize : value;
        }
    }
}
