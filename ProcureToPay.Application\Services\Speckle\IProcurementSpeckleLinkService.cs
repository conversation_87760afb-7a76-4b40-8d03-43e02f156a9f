using ProcureToPay.Application.DTOs.Speckle;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ProcureToPay.Application.Services.Speckle
{
    public interface IProcurementSpeckleLinkService
    {
        Task<ProcurementSpeckleLinkDto?> CreateLinkAsync(CreateProcurementSpeckleLinkRequest request, Guid linkedByUserId);
        Task<IEnumerable<ProcurementSpeckleLinkDto>> GetLinksByProcurementItemAsync(string itemType, Guid itemId);
        Task<IEnumerable<ProcurementSpeckleLinkDto>> GetLinksBySpeckleObjectAsync(Guid speckleObjectMetadataId);
        Task<ProcurementSpeckleLinkDto?> GetLinkByIdAsync(Guid linkId);
        Task<bool> DeleteLinkAsync(Guid linkId, Guid userId); // userId for permission check
    }
}
