using System;

namespace ProcureToPay.Domain.Entities
{
    public class SpeckleProjectLink : BaseEntity<Guid> // Specify Guid as TId
    {
        public Guid P2PProjectId { get; set; }
        public virtual Project? Project { get; set; } // Navigation property made nullable

        public string SpeckleServerUrl { get; set; } = null!; // Initialized with null forgiving for EF
        public string SpeckleStreamId { get; set; } = null!; // Initialized with null forgiving for EF
        public string? SpeckleCommitId { get; set; } // Optional

        // SECURITY NOTE: Storing tokens directly is a risk.
        // Consider encryption or vault services for production.
        public string SpeckleAuthToken { get; set; } = null!; // Initialized with null forgiving for EF

        public DateTime LinkedAt { get; set; }
        public DateTime? LastRefreshedAt { get; set; } // Optional

        // Constructor for required fields
        public SpeckleProjectLink(Guid p2PProjectId, string speckleServerUrl, string speckleStreamId, string speckleAuthToken)
            : base(Guid.NewGuid()) // Call base constructor with a new Guid
        {
            P2PProjectId = p2PProjectId;
            SpeckleServerUrl = speckleServerUrl; // Already checked by non-nullable type if enabled, or rely on DB
            SpeckleStreamId = speckleStreamId;   // Already checked by non-nullable type if enabled, or rely on DB
            SpeckleAuthToken = speckleAuthToken; // Already checked by non-nullable type if enabled, or rely on DB
            LinkedAt = DateTime.UtcNow;
            // Project property will be set via navigation property by EF Core or manually.
        }

        // Parameterless constructor for EF Core.
        // It also needs to provide an ID to the base constructor.
        // Properties will be set by EF Core. Null forgiving operator is used for non-nullable ones.
        protected SpeckleProjectLink() : base(Guid.NewGuid()) { }
    }
}
