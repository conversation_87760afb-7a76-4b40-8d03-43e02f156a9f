using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using ProcureToPay.Infrastructure.Persistence;
using ProcureToPay.Infrastructure.Persistence.Entities; // For SqlMigrationHistory
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace ProcureToPay.Infrastructure.Services
{
    public interface ISqlMigrationService
    {
        Task ApplyMigrationsAsync();
    }

    public class SqlMigrationService : ISqlMigrationService
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly ILogger<SqlMigrationService> _logger;
        private readonly string _scriptsFolderPath;

        public SqlMigrationService(ApplicationDbContext dbContext, ILogger<SqlMigrationService> logger)
        {
            _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            // Construct the path to the Migrations/Scripts directory relative to the Infrastructure project root
            // Adjust this path if your project structure is different or if Infrastructure is not the base path in execution context.
            var infrastructureAssemblyPath = Path.GetDirectoryName(typeof(SqlMigrationService).Assembly.Location);
            if (infrastructureAssemblyPath == null)
            {
                throw new DirectoryNotFoundException("Could not determine the assembly location for SqlMigrationService.");
            }
            _scriptsFolderPath = Path.Combine(infrastructureAssemblyPath, "..", "..", "Migrations", "Scripts");
             // This relative path assumes the assembly is in ProcureToPay.Infrastructure/bin/Debug/netX.X/
             // and Migrations/Scripts is at ProcureToPay.Infrastructure/Migrations/Scripts
             // If running from WebApp, the base path might be different.
             // A more robust way might be to pass the base path from configuration or use IWebHostEnvironment if available.
             // For now, let's try this common relative path.
             // Fallback for cases where infrastructureAssemblyPath might be too deep (e.g. tests)
            if (!Directory.Exists(_scriptsFolderPath))
            {
                 // Try a path assuming the execution is from the solution root or a similar level
                var projectRootPath = Directory.GetCurrentDirectory(); // This can be unreliable depending on execution context
                var searchPath = Path.Combine(projectRootPath, "ProcureToPay.Infrastructure", "Migrations", "Scripts");

                if (Directory.Exists(searchPath))
                {
                    _scriptsFolderPath = searchPath;
                }
                else // Final fallback, assuming a fixed relative path from where the assembly is located
                {
                     var assemblyLocation = Path.GetDirectoryName(typeof(SqlMigrationService).Assembly.Location) ?? ".";
                     _scriptsFolderPath = Path.GetFullPath(Path.Combine(assemblyLocation, "..", "..", "..", "ProcureToPay.Infrastructure", "Migrations", "Scripts"));

                    // If it's still not found, log a warning. The directory creation is handled in a later plan step.
                    if (!Directory.Exists(_scriptsFolderPath))
                    {
                         _scriptsFolderPath = Path.Combine("ProcureToPay.Infrastructure", "Migrations", "Scripts"); // Default if others fail
                        _logger.LogWarning("Custom SQL scripts folder path could not be reliably determined using assembly location or current directory. Defaulting to '{DefaultPath}'. Ensure this path is correct or manually create the directory if needed. It should be created in a subsequent step.", _scriptsFolderPath);
                    }
                }
            }
            _logger.LogInformation("SqlMigrationService: Scripts folder path set to: {Path}", Path.GetFullPath(_scriptsFolderPath));
        }

        public async Task ApplyMigrationsAsync()
        {
            _logger.LogInformation("Starting custom SQL migration process...");

            if (!Directory.Exists(_scriptsFolderPath))
            {
                _logger.LogWarning("Custom SQL scripts folder not found at '{Path}'. No custom SQL scripts will be applied. This directory should be created by a plan step.", Path.GetFullPath(_scriptsFolderPath));
                return;
            }

            var appliedScripts = await _dbContext.SqlMigrationHistories
                                                 .Select(h => h.ScriptName)
                                                 .ToListAsync();

            var scriptFiles = Directory.GetFiles(_scriptsFolderPath, "*.sql")
                                       .OrderBy(f => Path.GetFileName(f))
                                       .ToList();

            if (!scriptFiles.Any())
            {
                _logger.LogInformation("No SQL script files found in '{Path}'.", Path.GetFullPath(_scriptsFolderPath));
                return;
            }

            _logger.LogInformation("Found {Count} SQL script files. Applied scripts: {AppliedCount}", scriptFiles.Count, appliedScripts.Count);

            foreach (var scriptFile in scriptFiles)
            {
                var scriptName = Path.GetFileNameWithoutExtension(scriptFile);
                if (string.IsNullOrEmpty(scriptName))
                {
                    _logger.LogWarning("Skipping file with no name (or only extension): {File}", scriptFile);
                    continue;
                }

                if (!appliedScripts.Contains(scriptName))
                {
                    _logger.LogInformation("Applying script: {ScriptName}", scriptName);
                    try
                    {
                        var scriptContent = await File.ReadAllTextAsync(scriptFile);
                        if (string.IsNullOrWhiteSpace(scriptContent))
                        {
                            _logger.LogWarning("Script {ScriptName} is empty. Skipping.", scriptName);
                            continue;
                        }

                        // Use a transaction for each script to ensure atomicity
                        using (var transaction = await _dbContext.Database.BeginTransactionAsync())
                        {
                            await _dbContext.Database.ExecuteSqlRawAsync(scriptContent);

                            var historyEntry = new SqlMigrationHistory
                            {
                                ScriptName = scriptName,
                                AppliedDate = DateTime.UtcNow
                            };
                            _dbContext.SqlMigrationHistories.Add(historyEntry);
                            await _dbContext.SaveChangesAsync(); // Save history record

                            await transaction.CommitAsync(); // Commit transaction if all successful
                            _logger.LogInformation("Successfully applied script: {ScriptName} and recorded in history.", scriptName);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error applying script {ScriptName}. Halting further SQL script migrations.", scriptName);
                        // Depending on requirements, you might want to rethrow or handle differently.
                        // For now, we stop processing further scripts if one fails.
                        throw;
                    }
                }
                else
                {
                    _logger.LogInformation("Script {ScriptName} has already been applied. Skipping.", scriptName);
                }
            }

            _logger.LogInformation("Custom SQL migration process finished.");
        }
    }
}
