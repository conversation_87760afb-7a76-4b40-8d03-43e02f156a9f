﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\ProcureToPay.Domain\ProcureToPay.Domain.csproj" />
    <ProjectReference Include="..\ProcureToPay.Infrastructure\ProcureToPay.Infrastructure.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="GoogleAuthenticator" Version="3.2.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="9.0.0" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.12.0" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Speckle.Core" Version="2.17.6" />
    <PackageReference Include="Speckle.Objects" Version="2.17.6" />
  </ItemGroup>

</Project>
