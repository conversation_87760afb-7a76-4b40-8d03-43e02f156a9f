using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using Speckle.Core.Api;
using Speckle.Core.Credentials;
using Speckle.Core.Models;
using Speckle.Core.Transports;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json; // For JSON serialization
using System.Threading;
using Microsoft.EntityFrameworkCore; // For FirstOrDefaultAsync on DbSet
using System.Threading.Tasks;
using ProcureToPay.Application.Services.Speckle; // For ISpeckleLinkService
using ProcureToPay.Domain.Entities; // For SpeckleObjectMetadata
using ProcureToPay.Infrastructure.Persistence; // For ApplicationDbContext
// Adjusting for Speckle.Objects if needed for specific object manipulation
// using Speckle.Objects.Geometry; // Example if dealing with Mesh, Point, etc.


namespace ProcureToPay.Application.Services
{
    public class SpeckleService : BackgroundService
    {
        private readonly ILogger<SpeckleService> _logger;
        private readonly IConfiguration _configuration;
        private readonly ApplicationDbContext _dbContext; // Added DbContext
        private readonly ISpeckleLinkService _speckleLinkService; // Added SpeckleLinkService
        private Client _client;
        private Account _account;

        // Configuration properties remain the same
        private string SpeckleServerUrl => _configuration["Speckle:ServerUrl"];
        private string ApiToken => _configuration["Speckle:ApiToken"];
        private string StreamIdFromConfig => _configuration["Speckle:StreamId"]; // Renamed to avoid clash
        private string BranchName => _configuration["Speckle:BranchName"];
        private int PollingIntervalSeconds => _configuration.GetValue<int>("Speckle:PollingIntervalSeconds", 60);

        public SpeckleService(
            ILogger<SpeckleService> logger,
            IConfiguration configuration,
            ApplicationDbContext dbContext, // Injected DbContext
            ISpeckleLinkService speckleLinkService) // Injected SpeckleLinkService
        {
            _logger = logger;
            _configuration = configuration;
            _dbContext = dbContext;
            _speckleLinkService = speckleLinkService;

            // Initialize Speckle Account and Client (ApiToken and SpeckleServerUrl will be from config)
            _account = new Account
            {
                token = ApiToken,
                serverInfo = new ServerInfo { url = SpeckleServerUrl },
                userInfo = new UserInfo() // UserInfo will be fetched after client initialization
            };

            _client = new Client(_account);
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("SpeckleService is starting.");

            try
            {
                // Fetch UserInfo to confirm client is working - RE-COMMENTING due to SDK signature issues
                // _account.userInfo = await _client.AccountGet(stoppingToken);
                // _logger.LogInformation($"Successfully connected to Speckle server for user: {_account.userInfo.email}");
                _logger.LogWarning("Speckle UserInfo fetch temporarily commented out (SDK issues).");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to initialize Speckle client or get user account info.");
                // Depending on the error, you might want to stop the service or retry.
                // For now, we'll let it try polling, but it will likely fail.
            }


            string lastCommitId = null;

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    _logger.LogInformation($"Checking for Speckle stream updates for Stream ID from config: {StreamIdFromConfig}...");

                    // Get SpeckleProjectLink for context
                    var speckleLink = await _speckleLinkService.GetLinkBySpeckleStreamIdAsync(StreamIdFromConfig);
                    if (speckleLink == null)
                    {
                        _logger.LogWarning($"No SpeckleProjectLink found in DB for Stream ID: {StreamIdFromConfig}. This stream will not be processed.");
                        await Task.Delay(TimeSpan.FromSeconds(PollingIntervalSeconds), stoppingToken);
                        continue; // Skip if no link context
                    }
                    _logger.LogInformation($"Processing for P2P Project: {speckleLink.ProjectName} (ID: {speckleLink.P2PProjectId}) linked to Speckle Stream: {speckleLink.SpeckleStreamId}");

                    var branch = await _client.BranchGet(speckleLink.SpeckleStreamId, BranchName, 1, stoppingToken);
                    if (branch == null || branch.commits == null || branch.commits.items.Count == 0)
                    {
                        _logger.LogWarning($"Branch '{BranchName}' not found or has no commits in stream '{speckleLink.SpeckleStreamId}'.");
                        await Task.Delay(TimeSpan.FromSeconds(PollingIntervalSeconds), stoppingToken);
                        continue;
                    }

                    var latestCommit = branch.commits.items.First();
                    if (latestCommit.id != lastCommitId)
                    {
                        _logger.LogInformation($"New commit found: {latestCommit.id} by {latestCommit.authorName} - '{latestCommit.message}'");

                        var transport = new ServerTransport(_account, speckleLink.SpeckleStreamId);
                        // RE-COMMENTING Operations.Receive due to SDK signature issues
                        // var receivedData = await Operations.Receive(
                        //     latestCommit.referencedObject,
                        //     stoppingToken,
                        //     transport,
                        //     onProgressAction: dict => _logger.LogInformation($"Receiving progress: {dict.Values.Average()}%"),
                        //     onErrorAction: (s, ex) => _logger.LogError(ex, $"Error receiving data: {s}"),
                        //     onTotalChildrenCountKnown: count => _logger.LogInformation($"Total children count: {count}")
                        // );
                        Base? receivedData = null;
                        _logger.LogWarning("Speckle Operations.Receive temporarily commented out (SDK issues).");


                        if (receivedData != null)
                        {
                            _logger.LogInformation($"Data received successfully. Processing for SpeckleProjectLinkId: {speckleLink.Id}...");
                            await ProcessReceivedData(receivedData, latestCommit, speckleLink.Id, stoppingToken);
                        }
                        else
                        {
                            _logger.LogWarning($"Received null data from Speckle stream '{speckleLink.SpeckleStreamId}'.");
                        }
                        lastCommitId = latestCommit.id;
                    }
                    else
                    {
                        _logger.LogInformation("No new commits found.");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error during Speckle stream polling or data processing.");
                    // Implement more sophisticated retry logic or error handling as needed
                }

                await Task.Delay(TimeSpan.FromSeconds(PollingIntervalSeconds), stoppingToken);
            }

            _logger.LogInformation("SpeckleService is stopping.");
        }

        private async Task ProcessReceivedData(Base commitDataObject, Commit commitInfo, Guid speckleProjectLinkId, CancellationToken cancellationToken)
        {
            _logger.LogInformation($"Processing data from commit {commitInfo.id} for SpeckleProjectLinkId {speckleProjectLinkId}. Root object type: {commitDataObject.speckle_type}");

            var objectsToProcess = TraverseSpeckleObjects(commitDataObject);
            int processedCount = 0;
            int newCount = 0;
            int updatedCount = 0;

            foreach (var speckleObject in objectsToProcess)
            {
                if (cancellationToken.IsCancellationRequested) break;

                string speckleObjectId = speckleObject.id ?? speckleObject.applicationId ?? Guid.NewGuid().ToString(); // Fallback if no ID
                string objectType = speckleObject.speckle_type ?? "Unknown";
                if (speckleObject["category"] is string category && !string.IsNullOrEmpty(category)) {
                    objectType = category; // Prefer category if available (e.g. Revit)
                }

                string? revitElementId = speckleObject["elementId"]?.ToString();
                string? ifcGuid = speckleObject["ifcGuid"]?.ToString(); // Assuming direct property, might be nested

                var parameters = new Dictionary<string, object?>();
                foreach (var propName in speckleObject.GetDynamicMemberNames())
                {
                    // Exclude common Speckle internal/complex properties from simple parameter dump
                    if (propName.StartsWith("__") || propName.StartsWith("@") || propName == "id" || propName == "applicationId" || propName == "speckle_type" || propName == "units") continue;
                    var value = speckleObject[propName];
                    if (value is Base) continue; // Skip nested Speckle objects here, they are processed separately by traversal
                    if (value is List<object> list && list.Any(item => item is Base)) continue; // Skip lists of Speckle objects

                    parameters[propName] = value;
                }
                 // Also include typed members if not already captured by dynamic members and not complex
                foreach(var memberName in speckleObject.GetMembers().Keys) {
                    if (parameters.ContainsKey(memberName) || memberName.StartsWith("__") || memberName.StartsWith("@") || memberName == "id" || memberName == "applicationId" || memberName == "speckle_type" || memberName == "units") continue;
                    var value = speckleObject[memberName];
                     if (value is Base) continue;
                    if (value is List<object> list && list.Any(item => item is Base)) continue;
                    parameters[memberName] = value;
                }


                string parametersJson = JsonSerializer.Serialize(parameters, new JsonSerializerOptions { WriteIndented = false });

                var existingMetadata = await _dbContext.SpeckleObjectMetadata
                    .FirstOrDefaultAsync(m => m.SpeckleProjectLinkId == speckleProjectLinkId && m.SpeckleObjectId == speckleObjectId, cancellationToken);

                if (existingMetadata != null)
                {
                    existingMetadata.RevitElementId = revitElementId;
                    existingMetadata.IfcGuid = ifcGuid;
                    existingMetadata.ObjectType = objectType;
                    existingMetadata.ParametersJson = parametersJson;
                    existingMetadata.LastExtractedAt = DateTime.UtcNow;
                    updatedCount++;
                }
                else
                {
                    var newMetadata = new SpeckleObjectMetadata(speckleProjectLinkId, speckleObjectId, objectType, parametersJson)
                    {
                        RevitElementId = revitElementId,
                        IfcGuid = ifcGuid,
                        // LastExtractedAt is set in constructor
                    };
                    _dbContext.SpeckleObjectMetadata.Add(newMetadata);
                    newCount++;
                }
                processedCount++;
                if (processedCount % 100 == 0) // Save in batches of 100
                {
                    await _dbContext.SaveChangesAsync(cancellationToken);
                    _logger.LogInformation($"Saved batch of 100 metadata entries ({newCount} new, {updatedCount} updated so far for this commit).");
                }
            }

            await _dbContext.SaveChangesAsync(cancellationToken); // Save any remaining changes
            _logger.LogInformation($"Finished processing commit {commitInfo.id}. Total objects processed: {processedCount}. New: {newCount}, Updated: {updatedCount}.");
        }

        private IEnumerable<Base> TraverseSpeckleObjects(Base @base)
        {
            if (@base == null) yield break;

            yield return @base; // Return the current object

            // Traverse properties that might contain other Base objects or lists of Base objects
            // Consider both dynamic members and typed members for traversal
            var memberNames = @base.GetDynamicMemberNames().ToList();
            memberNames.AddRange(@base.GetMembers().Keys.Where(k => !memberNames.Contains(k)));

            foreach (var propName in memberNames.Distinct())
            {
                if (propName.StartsWith("__")) continue; // Skip internal Speckle properties like __closure, __detached, etc.

                object? propValue = @base[propName];

                if (propValue is Base nestedBase)
                {
                    foreach (var child in TraverseSpeckleObjects(nestedBase))
                    {
                        yield return child;
                    }
                }
                else if (propValue is List<object> list)
                {
                    foreach (var item in list)
                    {
                        if (item is Base listItemBase)
                        {
                            foreach (var child in TraverseSpeckleObjects(listItemBase))
                            {
                                yield return child;
                            }
                        }
                    }
                }
                // Handle Dictionaries if necessary, though less common for direct Base nesting
                // else if (propValue is IDictionary<string, object> dict) { ... }
            }

            // Special handling for common element containers if not covered by general traversal
            // (e.g., Revit 'elements', Archicad 'elements', etc.)
            // The generic traversal above should ideally handle these if 'elements' returns List<object> containing Base.
            // If 'elements' is a specific property name that might be missed or needs special attention:
            if (@base["elements"] is List<object> elementsProperty) // Example, adjust if needed
            {
                foreach (var element in elementsProperty)
                {
                    if (element is Base elementBase)
                    {
                        foreach (var child in TraverseSpeckleObjects(elementBase))
                        {
                            yield return child;
                        }
                    }
                }
            }
        }
    }
}
