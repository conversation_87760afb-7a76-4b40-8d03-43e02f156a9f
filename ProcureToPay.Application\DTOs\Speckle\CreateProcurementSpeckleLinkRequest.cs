using System;
using System.ComponentModel.DataAnnotations;

namespace ProcureToPay.Application.DTOs.Speckle
{
    public class CreateProcurementSpeckleLinkRequest
    {
        [Required]
        public Guid SpeckleObjectMetadataId { get; set; }

        [Required]
        [StringLength(100, MinimumLength = 3)]
        public string? ProcurementItemType { get; set; }

        [Required]
        public Guid ProcurementItemId { get; set; }

        // LinkedByUserId will typically be inferred from the authenticated user context in the service layer.
        // public Guid LinkedByUserId { get; set; }
    }
}
