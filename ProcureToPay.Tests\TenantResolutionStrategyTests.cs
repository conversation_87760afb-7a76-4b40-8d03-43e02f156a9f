using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Primitives;
using Moq;
using ProcureToPay.Infrastructure.Persistence;
using System.Security.Claims;
using Xunit;

namespace ProcureToPay.Tests
{
    public class TenantResolutionStrategyTests
    {
        private static readonly Guid DefaultTenantId = Guid.Parse("11111111-1111-1111-1111-111111111111");
        private static readonly Guid CustomTenantId = Guid.Parse("*************-2222-2222-************");
        private const string TenantIdClaimType = "tenant_id";
        private const string TenantIdHeaderName = "X-TenantId";

        [Fact]
        public void GetCurrentTenantId_NoHttpContext_ReturnsDefaultTenantId()
        {
            // Arrange
            var httpContextAccessorMock = new Mock<IHttpContextAccessor>();
#pragma warning disable CS8625 // Cannot convert null literal to non-nullable reference type.
            httpContextAccessorMock.Setup(x => x.HttpContext).Returns(default(HttpContext?));
#pragma warning restore CS8625 // Cannot convert null literal to non-nullable reference type.

            var strategy = new TenantResolutionStrategy(httpContextAccessorMock.Object);

            // Act
            var result = strategy.GetCurrentTenantId();

            // Assert
            Assert.Equal(DefaultTenantId, result);
        }

        [Fact]
        public void GetCurrentTenantId_WithTenantIdInClaims_ReturnsTenantIdFromClaims()
        {
            // Arrange
            var httpContextAccessorMock = new Mock<IHttpContextAccessor>();
            var httpContextMock = new Mock<HttpContext>();
            var claimsPrincipalMock = new Mock<ClaimsPrincipal>();
            var identity = new ClaimsIdentity(new[]
            {
                new Claim(TenantIdClaimType, CustomTenantId.ToString())
            }, "test");

            claimsPrincipalMock.Setup(x => x.Identity).Returns(identity);
            claimsPrincipalMock.Setup(x => x.FindFirst(TenantIdClaimType)).Returns(identity.FindFirst(TenantIdClaimType));
            httpContextMock.Setup(x => x.User).Returns(claimsPrincipalMock.Object);
            httpContextAccessorMock.Setup(x => x.HttpContext).Returns(httpContextMock.Object);

            var strategy = new TenantResolutionStrategy(httpContextAccessorMock.Object);

            // Act
            var result = strategy.GetCurrentTenantId();

            // Assert
            Assert.Equal(CustomTenantId, result);
        }

        [Fact]
        public void GetCurrentTenantId_WithTenantIdInHeader_ReturnsTenantIdFromHeader()
        {
            // Arrange
            var httpContextAccessorMock = new Mock<IHttpContextAccessor>();
            var httpContextMock = new Mock<HttpContext>();
            var requestMock = new Mock<HttpRequest>();
            var headersMock = new Mock<IHeaderDictionary>();

            headersMock.Setup(x => x.TryGetValue(TenantIdHeaderName, out It.Ref<StringValues>.IsAny))
                .Returns(true)
                .Callback(new TryGetValueCallback<string, StringValues>((string key, out StringValues value) =>
                {
                    value = new StringValues(CustomTenantId.ToString());
                }));

            requestMock.Setup(x => x.Headers).Returns(headersMock.Object);
            httpContextMock.Setup(x => x.Request).Returns(requestMock.Object);
            httpContextMock.Setup(x => x.User).Returns(new ClaimsPrincipal(new ClaimsIdentity()));
            httpContextAccessorMock.Setup(x => x.HttpContext).Returns(httpContextMock.Object);

            var strategy = new TenantResolutionStrategy(httpContextAccessorMock.Object);

            // Act
            var result = strategy.GetCurrentTenantId();

            // Assert
            Assert.Equal(CustomTenantId, result);
        }

        [Fact]
        public void GetCurrentTenantId_WithTenantIdInPath_ReturnsTenantIdFromPath()
        {
            // Arrange
            var httpContextAccessorMock = new Mock<IHttpContextAccessor>();
            var httpContextMock = new Mock<HttpContext>();
            var requestMock = new Mock<HttpRequest>();
            var pathMock = new PathString("/tenant/custom-tenant/some/path");

            requestMock.Setup(x => x.Path).Returns(pathMock);
            var emptyHeaders = new Mock<IHeaderDictionary>();
            requestMock.Setup(x => x.Headers).Returns(emptyHeaders.Object);
            httpContextMock.Setup(x => x.Request).Returns(requestMock.Object);
            httpContextMock.Setup(x => x.User).Returns(new ClaimsPrincipal(new ClaimsIdentity()));
            httpContextAccessorMock.Setup(x => x.HttpContext).Returns(httpContextMock.Object);

            var strategy = new TenantResolutionStrategy(httpContextAccessorMock.Object);

            // Act
            var result = strategy.GetCurrentTenantId();

            // Assert
            Assert.Equal(DefaultTenantId, result);
        }

        [Fact]
        public void GetCurrentTenantId_NoTenantIdFound_ReturnsDefaultTenantId()
        {
            // Arrange
            var httpContextAccessorMock = new Mock<IHttpContextAccessor>();
            var httpContextMock = new Mock<HttpContext>();
            var requestMock = new Mock<HttpRequest>();
            var pathMock = new PathString("/some/path");

            var emptyHeaders = new Mock<IHeaderDictionary>();
            requestMock.Setup(x => x.Headers).Returns(emptyHeaders.Object);
            requestMock.Setup(x => x.Path).Returns(pathMock);
            httpContextMock.Setup(x => x.Request).Returns(requestMock.Object);
            httpContextMock.Setup(x => x.User).Returns(new ClaimsPrincipal(new ClaimsIdentity()));
            httpContextAccessorMock.Setup(x => x.HttpContext).Returns(httpContextMock.Object);

            var strategy = new TenantResolutionStrategy(httpContextAccessorMock.Object);

            // Act
            var result = strategy.GetCurrentTenantId();

            // Assert
            Assert.Equal(DefaultTenantId, result);
        }
    }

    // Helper delegate for mocking TryGetValue
    public delegate void TryGetValueCallback<TKey, TValue>(TKey key, out TValue value);
}
