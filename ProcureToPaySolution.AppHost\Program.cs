using Aspire.Hosting;
using Aspire.Hosting.ApplicationModel; // Required for IResourceBuilder<ParameterResource> if used explicitly
using System;

var builder = DistributedApplication.CreateBuilder(args);

// Define a consistent name for the resource and the database within the container
string postgresResourceName = "postgresdb";
string dbName = "procuretopaydb"; // Database name inside the container

// Define the password for the local container using an Aspire Parameter Resource
// This allows Aspire to manage the value (e.g., generate a default, read from secrets/config)
// The actual value can be configured via appsettings.json ("Parameters": { "pgpassword": "your_password" }),
// environment variables (Parameters__pgpassword), or user secrets. If not configured, Aspire may generate one.
var pgPassword = builder.AddParameter("pgpassword", secret: true); // Define as a secret parameter

// Define PostgreSQL username parameter for flexibility
var pgUsername = builder.AddParameter("pgusername", value: "postgres");


// Add a PostgreSQL container resource named "postgresdb"
var postgresContainer = builder.AddPostgres(postgresResourceName, port: 5432, userName: pgUsername, password: pgPassword) // Pass the parameter resources here
                        .WithPgAdmin() // Add pgAdmin for database management
                        .WithVolume("postgres-data-volume", "/var/lib/postgresql/data") // Add volume mount to persist data across container restarts
                        .WithEnvironment("POSTGRES_INITDB_ARGS", "--auth-host=scram-sha-256 --auth-local=scram-sha-256"); // Enhanced security

// Explicitly create the database in the container
var postgresdb = postgresContainer.AddDatabase(dbName);

// Add environment variables to ensure the database is created
postgresContainer.WithEnvironment("POSTGRES_DB", dbName);

// Add explicit logging for debugging
Console.WriteLine($"---> AppHost: Added PostgreSQL container '{postgresResourceName}' with database '{dbName}'.");
Console.WriteLine($"---> AppHost: PostgreSQL container will be available at localhost:5432");
Console.WriteLine($"---> AppHost: Database name: {dbName}, Username: postgres, Password: [hidden]");

// Note: The actual password value isn't directly accessible here easily, as it's managed by Aspire.
// Check the Aspire dashboard's environment variables for the container/WebApp after first run to find the value if needed.
Console.WriteLine($"Starting PostgreSQL container '{postgresResourceName}' on port 5432. DB: '{dbName}'. User: 'postgres'. Password managed by Aspire parameter 'pgpassword'.");


// Add the Web Application Project
var webApp = builder.AddProject<Projects.ProcureToPay_WebApp>("ProcureToPay-WebApp")
                    .WithExternalHttpEndpoints(); // Makes it accessible from your browser


// Inject the Connection String into the WebApp
// Aspire automatically generates the correct connection string for the container (including host, port, dbname, user, password parameter reference)
// and makes it available under "ConnectionStrings:postgresdb"
webApp.WithReference(postgresdb);


// Build and Run the Distributed Application
builder.Build().Run();
