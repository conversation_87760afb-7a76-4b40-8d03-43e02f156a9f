using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProcureToPay.Domain.Entities;
using ProcureToPay.Domain.Enums;
using ProcureToPay.Domain.ValueObjects; // For Money VO

namespace ProcureToPay.Infrastructure.Persistence.Configurations
{
    /// <summary>
    /// EF Core Configuration for the PurchaseRequisitionLine entity.
    /// </summary>
    public class PurchaseRequisitionLineConfiguration : IEntityTypeConfiguration<PurchaseRequisitionLine>
    {

        public void Configure(EntityTypeBuilder<PurchaseRequisitionLine> builder)
        {
            builder.ToTable("purchase_requisition_lines");

            // --- Primary Key ---
            // Assuming PurchaseRequisitionLine inherits from BaseEntity<Guid>
            builder.HasKey(prl => prl.Id);

            // --- Unique Constraint (Instead of Composite Key) ---
            // Enforce that LineNumber is unique within a single PurchaseRequisition
            builder.HasIndex(prl => new { prl.PurchaseRequisitionId, prl.LineNumber })
                   .IsUnique();

            // --- Properties ---
            builder.Property(prl => prl.LineNumber)
                   .IsRequired();

            // TenantId configured via relationship and query filter below
            builder.Property(prl => prl.TenantId)
                   .IsRequired();
            builder.HasIndex(prl => prl.TenantId); // Index for tenant filtering

            builder.Property(prl => prl.Description)
                   .IsRequired()
                   .HasMaxLength(1000); // Example max length

            builder.Property(prl => prl.Quantity)
                   .IsRequired()
                   .HasPrecision(18, 4); // Precision for decimal quantity

            // Configure UnitOfMeasure Enum conversion
            builder.Property(prl => prl.UnitOfMeasure)
                   .IsRequired()
                   .HasConversion<string>() // Store as string
                   .HasMaxLength(50);

            builder.Property(prl => prl.GLAccountCode)
                   .HasMaxLength(50); // Example max length
            // Index on GLAccountCode
            builder.HasIndex(prl => prl.GLAccountCode);

            builder.Property(prl => prl.DateNeeded); // Nullable DateTime

            builder.Property(prl => prl.Notes)
                   .HasMaxLength(1000);


            // --- Value Object Mapping (Money?) ---
            // Configure EstimatedUnitPrice (nullable Money VO)
            builder.OwnsOne(prl => prl.EstimatedUnitPrice, price =>
            {
                price.Property(m => m.Amount)
                    .HasColumnName("estimated_unit_price_amount")
                    .HasPrecision(18, 4) // Precision configuration
                    .IsRequired(); // Amount required if Money object exists

                price.Property(m => m.CurrencyCode)
                    .HasColumnName("estimated_unit_price_currency_code")
                    .HasMaxLength(3)
                    .IsRequired(); // Currency required if Money object exists
            });
            // Navigation is implicitly optional as EstimatedUnitPrice property is nullable

            // Configure EstimatedLineCost (nullable Money VO) - Represents NetAmount calculation result
            builder.OwnsOne(prl => prl.EstimatedLineCost, cost =>
            {
                cost.Property(m => m.Amount)
                    .HasColumnName("estimated_line_cost_amount")
                    .HasPrecision(18, 4) // Precision configuration
                    .IsRequired(); // Amount required if Money object exists

                cost.Property(m => m.CurrencyCode)
                    .HasColumnName("estimated_line_cost_currency_code")
                    .HasMaxLength(3)
                    .IsRequired(); // Currency required if Money object exists
            });
            // Navigation is implicitly optional as EstimatedLineCost property is nullable


            // --- Relationships ---

            // Relationship to PurchaseRequisition header (Many Lines to One Header)
            builder.HasOne(prl => prl.PurchaseRequisition)
                   .WithMany(pr => pr.Lines) // Assumes PurchaseRequisition has ICollection<PurchaseRequisitionLine> Lines
                   .HasForeignKey(prl => prl.PurchaseRequisitionId)
                   .IsRequired()
                   .OnDelete(DeleteBehavior.Cascade); // Deleting header deletes lines

            // Relationship to VendorProduct (Optional)
            builder.HasOne(prl => prl.VendorProduct)
                   .WithMany() // Assuming VendorProduct doesn't need direct collection of Req Lines
                   .HasForeignKey(prl => prl.VendorProductId)
                   .IsRequired(false) // Link is optional
                   .OnDelete(DeleteBehavior.SetNull); // If VendorProduct deleted, nullify FK

            // Relationship to ProductDefinition (Optional)
            builder.HasOne(prl => prl.ProductDefinition)
                   .WithMany() // Assuming ProductDefinition doesn't need direct collection of Req Lines
                   .HasForeignKey(prl => prl.ProductDefinitionId)
                   .IsRequired(false) // Link is optional
                   .OnDelete(DeleteBehavior.SetNull); // If ProductDefinition deleted, nullify FK

            // Relationship to SuggestedVendor (Optional)
            builder.HasOne(prl => prl.SuggestedVendor)
                   .WithMany() // Assuming Vendor doesn't need direct collection of suggestions
                   .HasForeignKey(prl => prl.SuggestedVendorId)
                   .IsRequired(false) // Suggestion is optional
                   .OnDelete(DeleteBehavior.SetNull); // If suggested Vendor deleted, nullify FK


            // --- Indexes ---
            // Included unique index on ReqId+LineNumber and index on GLAccountCode, TenantId above.
            // Index on ProductId (using VendorProductId and ProductDefinitionId)
            builder.HasIndex(prl => prl.VendorProductId);
            builder.HasIndex(prl => prl.ProductDefinitionId);
            builder.HasIndex(prl => prl.SuggestedVendorId);


            // --- Tenant Isolation ---
            // Note: Tenant filtering should be implemented at the DbContext level
            // Example:
            // builder.HasQueryFilter(prl => prl.TenantId == currentTenantId);
            // Where currentTenantId is obtained from the DbContext or a tenant provider service

        }
    }
}

