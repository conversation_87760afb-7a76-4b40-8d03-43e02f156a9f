using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProcureToPay.Domain.Entities;

namespace ProcureToPay.Infrastructure.Persistence.Configurations
{
    public class RolePermissionConfiguration : IEntityTypeConfiguration<RolePermission>
    {
        public void Configure(EntityTypeBuilder<RolePermission> builder)
        {
            builder.HasKey(rp => rp.Id); // Using surrogate key

            builder.Property(rp => rp.RoleId).IsRequired();

            builder.Property(rp => rp.PermissionString)
                .IsRequired()
                .HasMaxLength(256); // Max length for permission strings

            // Define a composite unique index to ensure a permission is assigned only once per role
            builder.HasIndex(rp => new { rp.RoleId, rp.PermissionString })
                .IsUnique();

            // Foreign key relationship to Role is configured in RoleConfiguration
            // builder.HasOne(rp => rp.Role)
            //    .WithMany(r => r.RolePermissions)
            //    .<PERSON><PERSON><PERSON><PERSON>(rp => rp.RoleId);
        }
    }
}
