using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ProcureToPay.Application.DTOs.Audit;
using ProcureToPay.Application.Services;
using ProcureToPay.Domain.Constants; // For Permissions
using System;
using System.Security.Claims;
using System.Threading.Tasks;

namespace ProcureToPay.WebApp.Controllers
{
    [Authorize] // All actions require authentication
    [ApiController]
    [Route("api/admin/audit-logs")] // Grouped under admin for now
    public class AuditLogController : ControllerBase
    {
        private readonly IAuditLogService _auditLogService;
        private readonly IPermissionValidationService _permissionService;
        private readonly IHttpContextAccessor _httpContextAccessor;


        public AuditLogController(
            IAuditLogService auditLogService,
            IPermissionValidationService permissionService,
            IHttpContextAccessor httpContextAccessor)
        {
            _auditLogService = auditLogService;
            _permissionService = permissionService;
            _httpContextAccessor = httpContextAccessor;
        }

        private Guid GetCurrentUserId()
        {
            var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
            if (string.IsNullOrEmpty(userIdClaim) || !Guid.TryParse(userIdClaim, out var userId))
            {
                throw new InvalidOperationException("User ID not found or invalid in token.");
            }
            return userId;
        }
        private string? GetIpAddress() => _httpContextAccessor.HttpContext?.Connection.RemoteIpAddress?.ToString();


        [HttpGet]
        public async Task<IActionResult> GetAuditLogs([FromQuery] AuditLogQueryParameters queryParams)
        {
            try
            {
                var performingUserId = GetCurrentUserId();
                if (!await _permissionService.HasPermissionAsync(performingUserId, Permissions.ViewAuditLogs, GetIpAddress()))
                {
                    return Forbid();
                }

                var result = await _auditLogService.GetAuditLogsAsync(queryParams);
                return Ok(result);
            }
            catch (InvalidOperationException ex) // From GetCurrentUserId
            {
                 // Log this exception with more details server-side
                return Unauthorized(new { Message = ex.Message });
            }
            catch (Exception ex)
            {
                // Log ex server-side
                return StatusCode(500, "An unexpected error occurred while retrieving audit logs.");
            }
        }
    }
}
