namespace ProcureToPay.Application.DTOs.Profile
{
    public class UserProfileDto
    {
        public string Username { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public bool IsEmailVerified { get; set; }
        public string? FullName { get; set; }
        public string? PhoneNumber { get; set; }
        public string? PreferredLanguage { get; set; }
        public string? NotificationPreferences { get; set; }
        public string UserRole { get; set; } = string.Empty; // From UserRole enum
    }
}
