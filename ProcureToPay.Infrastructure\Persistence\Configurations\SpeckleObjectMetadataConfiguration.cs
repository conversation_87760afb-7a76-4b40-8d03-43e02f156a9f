using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProcureToPay.Domain.Entities;

namespace ProcureToPay.Infrastructure.Persistence.Configurations
{
    public class SpeckleObjectMetadataConfiguration : IEntityTypeConfiguration<SpeckleObjectMetadata>
    {
        public void Configure(EntityTypeBuilder<SpeckleObjectMetadata> builder)
        {
            builder.ToTable("SpeckleObjectMetadata");

            // Assuming BaseEntity<Guid> configures Id as PK, or uncomment if manual:
            // builder.HasKey(som => som.Id);
            // builder.Property(som => som.Id).ValueGeneratedOnAdd();

            builder.Property(som => som.SpeckleObjectId)
                .IsRequired()
                .HasMaxLength(255); // Speckle object IDs (hashes) are typically around 40-60 chars, but give some room.

            builder.Property(som => som.RevitElementId)
                .HasMaxLength(255); // Revit Element IDs can be strings or longs.

            builder.Property(som => som.IfcGuid)
                .HasMaxLength(255); // IFC Guids are typically 22 chars (base64) or 36 chars (hyphenated).

            builder.Property(som => som.ObjectType)
                .IsRequired()
                .HasMaxLength(512); // speckle_type or category strings.

            // For ParametersJson, consider using HasColumnType("jsonb") if using PostgreSQL for better performance.
            // For SQL Server, HasColumnType("nvarchar(max)") is standard.
            // Default EF Core will map string to nvarchar(max) or text depending on provider.
            builder.Property(som => som.ParametersJson)
                .IsRequired();

            builder.Property(som => som.LastExtractedAt)
                .IsRequired();

            // Configure the foreign key relationship to SpeckleProjectLink
            builder.HasOne(som => som.SpeckleProjectLink)
                   .WithMany() // Assuming SpeckleProjectLink does not have a direct navigation collection property for Metadata.
                   .HasForeignKey(som => som.SpeckleProjectLinkId)
                   .IsRequired()
                   .OnDelete(DeleteBehavior.Cascade); // Deleting a link also deletes its metadata.

            // Indexes
            // Index for faster lookup by SpeckleObjectId within a specific SpeckleProjectLink
            builder.HasIndex(som => new { som.SpeckleProjectLinkId, som.SpeckleObjectId }).IsUnique();

            // Optional: Index for faster lookup by RevitElementId or IfcGuid if these are commonly queried.
            builder.HasIndex(som => som.RevitElementId);
            builder.HasIndex(som => som.IfcGuid);

            // If SpeckleObjectMetadata implements ITenantEntity, ensure TenantId is configured.
            // (Assuming BaseEntity<Guid> handles TenantId if it's an ITenantEntity as it does for SpeckleProjectLink)
        }
    }
}
