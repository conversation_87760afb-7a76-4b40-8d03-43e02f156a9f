using ProcureToPay.Domain.Entities; // Assuming BaseEntity<Guid> is directly in here
using ProcureToPay.Domain.Enums;
using System;
using System.ComponentModel.DataAnnotations.Schema; // For Column attribute if needed by EF Core for decimal precision outside of fluent API

namespace ProcureToPay.Domain.Entities.Catalog
{
    public class CatalogProduct : BaseEntity<Guid>
    {
        public Guid SellerOrgId { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
        public Guid CategoryId { get; set; }
        public virtual ProductCategory Category { get; set; } = null!;

        [Column(TypeName = "decimal(18,2)")] // Good practice to keep for clarity, though can be in fluent API
        public decimal ListPrice { get; set; }

        public ProductStatus ProductStatus { get; set; } = ProductStatus.Draft;
        public string? UnitOfMeasure { get; set; }
        public int? StockQuantity { get; set; }
        public InventoryStatus InventoryStatus {get; set; } = InventoryStatus.OutOfStock;
        public DateTime? AvailableDate { get; set; }
        public int? StandardLeadTimeDays { get; set; }
        public string? QuantityBasedPricingTiersJson { get; set; }
        public DateTime? SubmittedAt { get; set; }
        public Guid? ApprovedByUserId { get; set; }
        public DateTime? ApprovedAt { get; set; }
        public string? RejectionReason { get; set; }
        public int Version { get; set; } = 1;

        // Parameterless constructor for EF Core
        private CatalogProduct() : base(Guid.NewGuid()) { }

        // Constructor for application code
        public CatalogProduct(Guid id, Guid sellerOrgId, string name, Guid categoryId, decimal listPrice)
            : base(id == Guid.Empty ? Guid.NewGuid() : id)
        {
            SellerOrgId = sellerOrgId;
            Name = name;
            CategoryId = categoryId;
            ListPrice = listPrice;
        }
    }
}
